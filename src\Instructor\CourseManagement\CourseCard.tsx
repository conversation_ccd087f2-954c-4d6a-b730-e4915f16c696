import React, { Fragment } from "react";
import { ActivityName } from "../../types/Activity";

interface Props {
  title: string;
  activities: ActivityName[];
  onEditClick?: () => void;
  onSelectClick?: (index: number) => void;
  hideEditButton?: boolean;
  onDeleteClick?: () => void;
}

const ActivityBox = ({
  activityChunk,
  index,
  lastItem,
}: {
  activityChunk: string[];
  index: number;
  lastItem: boolean;
}) => {
  return (
    <div
      className={`flex flex-row ${index % 2 === 0 ? "" : "flex-row-reverse"}`}
    >
      {activityChunk.map((activity, i) => {
        return (
          <Fragment key={activity + i}>
            {activityChunk.length === 2 && index % 2 !== 0 && i === 1 && (
              <div> {"←"}</div>
            )}
            <div className="flex flex-col items-center">
              <div className="h-fit w-[124px] rounded bg-[#DAE3F2] py-2 text-center">
                <h1 className="text-sm font-semibold text-[#36537F]">
                  {activity}
                </h1>
              </div>
              {!lastItem && index % 2 === 0 && i === 1 && <div> {"↓"}</div>}
              {!lastItem && index % 2 !== 0 && i === 1 && <div> {"↓"}</div>}
            </div>
            {activityChunk.length === 2 && index % 2 === 0 && i === 0 && (
              <div> {"→"}</div>
            )}
          </Fragment>
        );
      })}
    </div>
  );
};

function CourseCard(props: Props) {
  const chunks = [];
  const chunkSize = 2;
  for (let i = 0; i < props.activities.length; i += chunkSize) {
    chunks.push(props.activities.slice(i, i + chunkSize));
  }
  return (
    <div className="flex w-[320px] cursor-pointer flex-col gap-11 rounded px-4 py-6 hover:bg-[#FFFFFF]">
      <h2 className="text-sm font-semibold text-[#484848]">{props.title}</h2>

      <div className="flex flex-col px-3">
        {chunks.map((activityList, index) => (
          <div
            className={`flex flex-row ${index % 2 === 0 ? "" : "flex-row-reverse"}`}
            key={index}
          >
            {activityList.map((activity, i) => {
              return (
                <Fragment key={activity.name + i}>
                  {activityList.length === 2 && index % 2 !== 0 && i === 1 && (
                    <div> {"←"}</div>
                  )}
                  <div
                    className="flex flex-col items-center"
                    onClick={() =>
                      props.onSelectClick && props.onSelectClick(index * 2 + i)
                    }
                  >
                    <div
                      className={
                        "h-fit w-[124px] rounded py-2 text-center " +
                        (activity.selected ? "bg-yellow-200" : "bg-[#DAE3F2]")
                      }
                    >
                      <h1 className="text-sm font-semibold text-[#36537F]">
                        {activity.name}
                      </h1>
                    </div>
                    {!(index === chunks.length - 1) &&
                      index % 2 === 0 &&
                      i === 1 && <div> {"↓"}</div>}
                    {!(index === chunks.length - 1) &&
                      index % 2 !== 0 &&
                      i === 1 && <div> {"↓"}</div>}
                  </div>
                  {activityList.length === 2 && index % 2 === 0 && i === 0 && (
                    <div> {"→"}</div>
                  )}
                </Fragment>
              );
            })}
          </div>
        ))}
      </div>

      {props.hideEditButton ? (
        <></>
      ) : (
        <div className="flex gap-2">
          <button
            onClick={props.onEditClick}
            className="h-10 w-44 rounded-lg bg-[#36537F] text-center text-lg font-medium text-[#F5F5F5] hover:bg-blue-400"
          >
            Edit
          </button>
          <button
            onClick={props.onDeleteClick}
            className="h-10 w-44 rounded-lg bg-[#EF4444] text-center text-lg font-medium text-[#F5F5F5] hover:bg-red-400"
          >
            Delete
          </button>
        </div>
      )}
    </div>
  );
}

export default CourseCard;
