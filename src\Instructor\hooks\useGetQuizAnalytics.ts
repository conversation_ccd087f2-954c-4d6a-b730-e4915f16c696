import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { QuizResponse } from "../../types/Analytics";
import { AxiosInstance } from "axios";

// api call

const getAnalytics = async (
  axiosObject: AxiosInstance,
): Promise<QuizResponse> => {
  const data = await axiosObject.get<QuizResponse>(
    "/api/v1/analytics/instructor/quiz",
  );
  console.log(data);
  return data.data;
};

export function useGetQuizAnalytics() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<QuizResponse>({
    queryKey: ["quizAnalytics"],
    refetchOnWindowFocus: false,
    queryFn: () => getAnalytics(axiosObject),
  });
}
