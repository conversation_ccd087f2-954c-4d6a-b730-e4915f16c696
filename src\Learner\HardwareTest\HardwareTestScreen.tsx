import ContentBox from "../components/ContentBox";
import SideBar from "../components/SideBar";
import TopBar from "../components/TopBar";
import HardwareCard from "./HardwareCard";
import { useState } from "react";
import "../components/ResponsiveStyles.css";

function HardwareTestScreen() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuClose = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <div className="flex h-screen flex-col">
      <TopBar onMenuToggle={handleMenuToggle} />
      <div className="flex flex-col md:flex-row">
        <SideBar
          selectedItem={0}
          isMobileMenuOpen={isMobileMenuOpen}
          onMobileMenuClose={handleMenuClose}
        />
        <ContentBox heading="Hardware Test">
          <HardwareCard />
        </ContentBox>
      </div>
    </div>
  );
}

export default HardwareTestScreen;
