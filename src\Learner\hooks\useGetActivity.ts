import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { Activity } from "../../types/Activity";
import { AxiosInstance } from "axios";

// api call

const getActivity = async (
  axiosObject: AxiosInstance,
  activity_id: string,
): Promise<Activity> => {
  const response = await axiosObject.get<Activity>(
    "/api/v1/activity/get/" + activity_id,
  );

  const activity = response.data;
  console.log("Activity data received from API:", response.data);

  // Parse extra_fields if it's a string
  if (activity.extra_fields && typeof activity.extra_fields === "string") {
    try {
      activity.extra_fields = JSON.parse(activity.extra_fields);
      console.log("Parsed extra_fields:", activity.extra_fields);
    } catch (error) {
      console.error("Error parsing extra_fields:", error);
    }
  }

  // Log practice activity data for debugging
  if (activity.type === "practice_activity") {
    console.log("Practice activity data found:", {
      rootLevel: activity.practiceActivityData,
      extraFields: activity.extra_fields?.practiceActivityData,
    });
  }

  console.log("Final activity object:", activity);
  return activity;
};

export function useGetActivity(activity_id: string) {
  const axiosObject = useAxios();
  // run the query
  return useQuery<Activity>({
    queryKey: ["activity", activity_id],
    refetchOnWindowFocus: false,
    queryFn: () => getActivity(axiosObject, activity_id),
    enabled: !!activity_id,
    staleTime: 5 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
  });
}
