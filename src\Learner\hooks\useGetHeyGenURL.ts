import React from "react";
import useAxios from "../../hooks/axiosObject";

export default function useGetHeyGenURL() {
  const axiosObject = useAxios();

  const getHeyGenURL = async (videoId: string) => {
    try {
      const response = await axiosObject.get("api/v1/heygen-video/" + videoId);
      return response.data.data.video_url;
    } catch (error) {
      console.error("Error fetching Heygen URL:", error);
      throw error;
    }
  };

  return getHeyGenURL;
}
