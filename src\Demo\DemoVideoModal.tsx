interface Props {
  onClose: () => void;
}

const DemoVideoModal = ({ onClose }: Props) => {
  return (
    <div className="fixed inset-0 z-10 flex items-center justify-center overflow-hidden bg-black/50">
      <div className="relative flex h-[450px] w-[700px] flex-col items-center justify-center rounded-3xl bg-black px-14">
        <button
          className="absolute right-4 top-4 h-8 w-8 rounded-full bg-white text-black"
          onClick={onClose}
        >
          X
        </button>
        <video
          src="/demo_2_2.mp4"
          controls
          className="items-center rounded-3xl"
          autoPlay
          onEnded={onClose}
        />
      </div>
    </div>
  );
};

export default DemoVideoModal;
