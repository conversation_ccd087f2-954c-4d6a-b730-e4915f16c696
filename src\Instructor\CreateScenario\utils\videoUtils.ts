/**
 * Utility functions for handling video operations
 */

/**
 * Captures a screenshot from a video element
 * @param videoElement The HTML video element to capture from
 * @returns A Promise that resolves to a Blob containing the screenshot
 */
export const captureScreenshot = (
  videoElement: HTMLVideoElement,
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    if (!videoElement) {
      reject(new Error("No video element available"));
      return;
    }

    try {
      const canvas = document.createElement("canvas");

      // Set canvas dimensions to match video
      canvas.width = videoElement.videoWidth || 640;
      canvas.height = videoElement.videoHeight || 480;

      // Draw the current video frame to the canvas
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        reject(new Error("Could not get canvas context"));
        return;
      }

      ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

      // Convert canvas to blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error("Failed to create blob from canvas"));
          }
        },
        "image/jpeg",
        0.95,
      );
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Records a video from a MediaStream
 * @param stream The MediaStream to record
 * @param durationMs The duration to record in milliseconds
 * @returns A Promise that resolves to a Blob containing the recorded video
 */
export const recordStreamToVideo = (
  stream: MediaStream,
  durationMs: number = 30000, // Default to 30 seconds
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    try {
      const chunks: Blob[] = [];
      const options = { mimeType: "video/webm; codecs=vp9" };

      // Create a MediaRecorder to record the stream
      const mediaRecorder = new MediaRecorder(stream, options);

      // Collect data as it becomes available
      mediaRecorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      // When recording stops, create a video blob
      mediaRecorder.onstop = () => {
        const videoBlob = new Blob(chunks, { type: "video/webm" });
        if (videoBlob.size > 0) {
          resolve(videoBlob);
        } else {
          reject(new Error("Failed to create video blob"));
        }
      };

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms

      // Stop recording after the specified duration
      setTimeout(() => {
        if (mediaRecorder.state !== "inactive") {
          mediaRecorder.stop();
        }
      }, durationMs);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Records a video from a video element by capturing its stream
 * @param videoElement The HTML video element to record from
 * @param durationMs The duration to record in milliseconds
 * @returns A Promise that resolves to a Blob containing the recorded video
 */
export const recordVideoElement = (
  videoElement: HTMLVideoElement,
  durationMs: number = 30000, // Default to 30 seconds
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    try {
      if (!videoElement) {
        reject(new Error("No video element available"));
        return;
      }

      // Create a canvas element to capture the video frames
      const canvas = document.createElement("canvas");
      canvas.width = videoElement.videoWidth || 640;
      canvas.height = videoElement.videoHeight || 480;
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        reject(new Error("Could not get canvas context"));
        return;
      }

      // Create a stream from the canvas
      const canvasStream = canvas.captureStream(30); // 30 FPS

      // Add audio track if available
      if (videoElement.srcObject instanceof MediaStream) {
        const audioTracks = (
          videoElement.srcObject as MediaStream
        ).getAudioTracks();
        audioTracks.forEach((track) => canvasStream.addTrack(track));
      }

      // Create a MediaRecorder to record the stream
      const chunks: Blob[] = [];
      const mediaRecorder = new MediaRecorder(canvasStream, {
        mimeType: "video/webm; codecs=vp9",
      });

      // Collect data as it becomes available
      mediaRecorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      // When recording stops, create a video blob
      mediaRecorder.onstop = () => {
        const videoBlob = new Blob(chunks, { type: "video/webm" });
        if (videoBlob.size > 0) {
          resolve(videoBlob);
        } else {
          reject(new Error("Failed to create video blob"));
        }
      };

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms

      // Draw video frames to canvas at 30 FPS
      let frameId: number | null = null;
      const drawFrame = () => {
        if (videoElement.readyState >= 2) {
          ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        }
        frameId = requestAnimationFrame(drawFrame);
      };

      frameId = requestAnimationFrame(drawFrame);

      // Stop recording after the specified duration
      setTimeout(() => {
        if (frameId !== null) {
          cancelAnimationFrame(frameId);
        }

        if (mediaRecorder.state !== "inactive") {
          mediaRecorder.stop();
        }
      }, durationMs);
    } catch (error) {
      reject(error);
    }
  });
};
