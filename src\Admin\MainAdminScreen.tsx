import { Route, Routes, useLocation } from "react-router-dom";
import EnrollmentTable from "./CourseEnrollment/EnrollmentTable";
import CourseManagement from "./CourseManagement/CourseManagement";
import Dashboard from "./Dashboard/Dashboard";
import GroupManagement from "./GroupManagement/GroupManagement";
import Layout from "./Layout";
import Overview from "./Overview/Overview";
import Results from "./Results/Results";
import UserDetailedResults from "./Results/UserDetailedResults";
import SecurityManagement from "./SecurityManagement/SecurityManagement";
import SupportTicketManagement from "./SupportTicketManagement/SupportTicketManagement";
import RoleTable from "./UserManagement/RoleTable";
import ResultsUpdated from "./Results-Dashboard-Application/App";

function MainAdminScreen() {
  return (
    <Layout
      heading={HeadingText()}
      children={
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/overview" element={<Overview />} />
          <Route path="/user-upload" element={<GroupManagement />} />
          <Route path="/user-management" element={<RoleTable />} />
          <Route path="/course-management" element={<CourseManagement />} />
          <Route path="/course-enrollment" element={<EnrollmentTable />} />
          {/* <Route path="/results" element={<Results />} /> */}
          <Route path="/results" element={<ResultsUpdated />} />
          <Route
            path="/results/user/:userId/course/:courseId"
            element={<UserDetailedResults />}
          />
          <Route path="/security-management" element={<SecurityManagement />} />
          <Route
            path="/support-ticket-management"
            element={<SupportTicketManagement />}
          />
        </Routes>
      }
    ></Layout>
  );
}

function HeadingText() {
  const location = useLocation();

  let headerText = "";
  switch (location.pathname) {
    case "/admin":
      headerText = "Admin Dashboard";
      break;
    case "/admin/dashboard":
      headerText = "Admin Dashboard";
      break;
    case "/admin/user-management":
      headerText = "User Management";
      break;
    case "/admin/security-management":
      headerText = "Security Management";
      break;
    case "/admin/overview":
      headerText = "Overview";
      break;
    case "/admin/user-upload":
      headerText = "User Upload";
      break;
    case "/admin/course-management":
      headerText = "Course Management";
      break;
    case "/admin/course-enrollment":
      headerText = "Course Enrollment";
      break;
    case "/admin/results":
      headerText = "Results";
      break;
    case location.pathname.match(/\/admin\/results\/user\/\d+\/course\/\d+/)
      ?.input:
      headerText = "Detailed Results";
      break;
    case "/admin/support-ticket-management":
      headerText = "Support Ticket Management";
      break;
    default:
      headerText = "Overview";
  }

  return headerText;
}

export default MainAdminScreen;
