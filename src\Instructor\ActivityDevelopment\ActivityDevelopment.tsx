import { <PERSON>, <PERSON><PERSON>, Stack, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import React, { useEffect, useState, useRef } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { useLocation, useNavigate } from "react-router-dom";
import Spinner from "../../Learner/components/Spinner";
import { useGetActivity } from "../../Learner/hooks/useGetActivity";
import { Question } from "../../Learner/hooks/useQuestion";
import showError from "../../Learner/scripts/showErrorDialog";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import { PracticeActivityData } from "../../types/Activity";
import HeygenVideoSelector from "../components/HeygenVideoSelector";
import {
  HeygenVideoWithUrl,
  useGetHeygenVideo,
  useListHeygenVideos,
} from "../hooks/useHeygenVideos";
import { useUpdateActivity } from "../hooks/useUpdateActivity";
import EditFeedbackQuestionList from "./FeedbackDevelopment";
import PracticeActivityForm, {
  PracticeActivityFormRef,
} from "./PracticeActivityForm";
import EditQuiz from "./QuizDevelopment";
import EditSurveyQuestionList from "./SurveyDevelopment";

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

const modules = {
  toolbar: [
    [{ header: [1, 2, 3, false] }], // Headers
    ["bold", "italic", "underline", "strike"], // Formatting
    [
      { align: "" },
      { align: "center" },
      { align: "right" },
      { align: "justify" },
    ], // Alignment
    [{ list: "ordered" }, { list: "bullet" }], // Lists
    ["link", "image"], // Links and images
    ["clean"], // Clear formatting
  ],
};

const formats = [
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "align", // Include alignment formats
  "list",
  "bullet",
  "link",
  "image",
];

export default function ActivityDevelopment() {
  const query = useQueryParam();
  const activity_id = query.get("activity_id") ?? "";
  const course_id = query.get("course_id") ?? "";
  const returnedVideoId = query.get("videoId");
  const [file, setFile] = useState<File | null>(null);
  const [secondVideo, setSecondVideo] = useState<File | null>(null);
  const [pdfFile, setpdfFile] = useState<File | null>(null);
  const [activityName, setActvityName] = useState<string>("");
  const [fileLink, setFileLink] = useState<string>("");
  const [mandatory, setMandatory] = useState<boolean>(false);
  const [showCorrectOption, setShowCorrectOption] = useState<boolean>(false);
  const [videoTitle, setVideoTitle] = useState("");
  const [instructions, setInstructions] = useState("");
  const [instructionsBelow, setInstructionsBelow] = useState("");
  const [instructionsCP, setInstructionsCP] = useState("");
  const [instructionsCPBelow, setInstructionsCPBelow] = useState("");
  const [instructionsRec, setInstructionsRec] = useState("");
  const [instructionsRecBelow, setInstructionsRecBelow] = useState("");
  const [instructionsWr, setInstructionsWr] = useState("");
  const [instructionsWrBelow, setInstructionsWrBelow] = useState("");
  const [selectedHeygenVideoUrl, setSelectedHeygenVideoUrl] =
    useState<string>("");
  const [selectedHeygenVideoId, setSelectedHeygenVideoId] = useState<
    number | undefined
  >(undefined);
  const [initialHeygenVideoLoaded, setInitialHeygenVideoLoaded] =
    useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [practiceActivityData, setPracticeActivityData] = useState<
    PracticeActivityData | undefined
  >(undefined);
  const practiceActivityFormRef = useRef<PracticeActivityFormRef>(null);
  const { data: activity, isFetching } = useGetActivity(activity_id);
  const { data: heygenVideos } = useListHeygenVideos();
  const { data: returnedVideo } = useGetHeygenVideo(
    returnedVideoId ? parseInt(returnedVideoId) : 0,
  );
  const mutation = useUpdateActivity();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setFile(event.target.files[0]);
    }
  };

  const handlePDFFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setpdfFile(event.target.files[0]);
    }
  };

  const handleSecondVideoChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (event.target.files) {
      setSecondVideo(event.target.files[0]);
    }
  };

  // Handle navigation to Create Scenario screen
  const handleGenerateVideo = () => {
    // Navigate to the Create Scenario screen with return information
    // Include the activity type to determine which script generation method to use
    const extraFields = selectedHeygenVideoId
      ? JSON.stringify({
          heygen_video_id: selectedHeygenVideoId,
          heygen_video_url: selectedHeygenVideoUrl,
        })
      : "";

    console.log("Saving activity with Heygen video:", {
      selectedHeygenVideoId,
      selectedHeygenVideoUrl,
      extraFields,
    });

    // Create custom instructions with heygen video information embedded
    // We're storing the video info in both extra_fields and as a hidden div in instructions
    // for backward compatibility
    const instructionsWithHeygen = selectedHeygenVideoId
      ? `${instructions}<div data-heygen-video-id="${selectedHeygenVideoId}" data-heygen-video-url="${selectedHeygenVideoUrl}" style="display:none;"></div>`
      : instructions;

    mutation.mutate(
      {
        activityId: activity_id,
        name: activityName,
        instructions: instructionsWithHeygen,
        instructions_below: instructionsBelow,
        instructions_cp: instructionsCP,
        instructions_cp_below: instructionsCPBelow,
        instructions_rec: instructionsRec,
        instructions_rec_below: instructionsRecBelow,
        instructions_wr: instructionsWr,
        instructions_wr_below: instructionsWrBelow,
        videoTitle: videoTitle,
        videoFile: file,
        secondVideoFile: secondVideo,
        mandatory: String(mandatory),
        fileLink: fileLink,
        pdfFile: pdfFile,
        extra_fields: extraFields,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ["activity", activity_id],
          });
          navigate(
            `/instructor/create-scenario?returnTo=activity_development&activityId=${activity_id}&activityType=${activity?.type || ""}&courseId=${course_id}`,
          );
        },
        onError: () => {
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  // First, load the activity data
  useEffect(() => {
    if (!activity) return;

    setActvityName(activity.name || "");

    // Process instructions to extract Heygen video information if present
    if (activity.instructions) {
      // Create a temporary div to parse the HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = activity.instructions;

      // Find the div with Heygen video data
      const heygenDiv = tempDiv.querySelector("div[data-heygen-video-id]");

      if (heygenDiv) {
        // Remove the Heygen div from the instructions
        heygenDiv.remove();
        setInstructions(tempDiv.innerHTML);
      } else {
        setInstructions(activity.instructions);
      }
    } else {
      setInstructions("");
    }

    setInstructionsBelow(
      activity?.instructions_below ? activity.instructions_below : "",
    );
    setInstructionsCP(
      activity?.instructions_cp ? activity.instructions_cp : "",
    );
    setInstructionsCPBelow(
      activity?.instructions_cp_below ? activity.instructions_cp_below : "",
    );
    setInstructionsRec(
      activity?.instructions_rec ? activity.instructions_rec : "",
    );
    setInstructionsRecBelow(
      activity?.instructions_rec_below ? activity.instructions_rec_below : "",
    );
    setInstructionsWr(
      activity?.instructions_wr ? activity.instructions_wr : "",
    );
    setInstructionsWrBelow(
      activity?.instructions_wr_below ? activity.instructions_wr_below : "",
    );
    setMandatory(activity.mandatory || false);
    setShowCorrectOption(activity.show_correct_option || false);
    setFileLink(activity.file_link || "");

    // Load practice activity data if it exists
    if (activity.type === "practice_activity") {
      // Check for practice activity data in multiple locations for backward compatibility
      let practiceData = null;

      console.log("Loading practice activity data for editing:", {
        activityId: activity.id,
        rootLevel: activity.practiceActivityData,
        extraFields: activity.extra_fields?.practiceActivityData,
      });

      // First check at root level (new API response format)
      if (activity.practiceActivityData) {
        practiceData = activity.practiceActivityData;
        console.log("Using practice data from root level:", practiceData);
      }
      // Then check in extra_fields (legacy format)
      else if (activity.extra_fields?.practiceActivityData) {
        practiceData = activity.extra_fields.practiceActivityData;
        console.log("Using practice data from extra_fields:", practiceData);
      }

      if (practiceData) {
        console.log(
          "ActivityDevelopment: Setting practice activity data:",
          practiceData,
        );
        console.log(
          "ActivityDevelopment: Number of criteria:",
          practiceData.evaluationRubric?.criteria?.length || 0,
        );
        practiceData.evaluationRubric?.criteria?.forEach((criterion, index) => {
          console.log(
            `ActivityDevelopment: Criterion ${index + 1}:`,
            criterion.name,
            "Questions:",
            criterion.questions?.length || 0,
          );
        });
        setPracticeActivityData(practiceData);
        console.log(
          "Practice activity form will be pre-filled with:",
          practiceData,
        );
      } else {
        // Initialize with default structure if no data exists
        console.log(
          "No existing practice data found, initializing with defaults",
        );
        setPracticeActivityData({
          interlocutorProfile: "",
          scenarioDescription: "",
          evaluationRubric: {
            criteria: [
              {
                name: "",
                questions: [{ question: "" }],
              },
            ],
          },
        });
      }
    }
  }, [activity]);

  // Second, handle video selection with priority order:
  // 1. Returned video from URL parameter (highest priority)
  // 2. Explicitly selected video by user
  // 3. Video from activity data (lowest priority)
  useEffect(() => {
    // Skip if we don't have activity data yet
    if (!activity) return;

    // Case 1: If we have a returned video from URL parameter, use it (highest priority)
    if (returnedVideo && returnedVideoId) {
      console.log("Setting video from URL parameter:", returnedVideo);

      // Force a reset of the selection state to ensure UI updates
      setSelectedHeygenVideoId(undefined);
      setSelectedHeygenVideoUrl("");

      // Small delay to ensure the reset is processed before setting the new value
      setTimeout(() => {
        setSelectedHeygenVideoUrl(returnedVideo.video_url);
        setSelectedHeygenVideoId(returnedVideo.id);
        setInitialHeygenVideoLoaded(true);

        // Show success message
        showSuccess(
          "Video Selected",
          `The video "${returnedVideo.name}" has been selected for this activity.`,
        );
      }, 50);

      // Clear the URL parameter to prevent reselection on refresh
      // This uses the History API to update the URL without reloading the page
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete("videoId");
      window.history.replaceState({}, "", newUrl.toString());

      return;
    }

    // Case 2: If the user has already explicitly selected a video, don't change it
    if (initialHeygenVideoLoaded && selectedHeygenVideoId) {
      console.log("Video already explicitly selected:", selectedHeygenVideoId);
      return;
    }

    // Case 3: Try to get video from activity data (lowest priority)
    console.log("Looking for video in activity data");

    // First check in extra_fields (preferred method)
    if (activity.extra_fields) {
      if (activity.extra_fields.heygen_video_id) {
        const videoId = activity.extra_fields.heygen_video_id;
        console.log("Setting video from extra_fields:", videoId);

        // Force a reset of the selection state to ensure UI updates
        setSelectedHeygenVideoId(undefined);
        setSelectedHeygenVideoUrl("");

        // Small delay to ensure the reset is processed before setting the new value
        setTimeout(() => {
          setSelectedHeygenVideoId(videoId);
          if (activity.extra_fields && activity.extra_fields.heygen_video_url) {
            setSelectedHeygenVideoUrl(activity.extra_fields.heygen_video_url);
          }
          setInitialHeygenVideoLoaded(true);
        }, 50);

        return;
      }
    }

    // Then check in instructions (legacy method)
    if (activity.instructions) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = activity.instructions;
      const heygenDiv = tempDiv.querySelector("div[data-heygen-video-id]");

      if (heygenDiv) {
        const videoId = heygenDiv.getAttribute("data-heygen-video-id");
        const videoUrl = heygenDiv.getAttribute("data-heygen-video-url");

        if (videoId) {
          console.log("Setting video from instructions:", videoId);

          // Force a reset of the selection state to ensure UI updates
          setSelectedHeygenVideoId(undefined);
          setSelectedHeygenVideoUrl("");

          // Small delay to ensure the reset is processed before setting the new value
          setTimeout(() => {
            setSelectedHeygenVideoId(parseInt(videoId));
            if (videoUrl) {
              setSelectedHeygenVideoUrl(videoUrl);
            }
            setInitialHeygenVideoLoaded(true);
          }, 50);
        }
      }
    }
  }, [activity, returnedVideo, returnedVideoId]);

  const handleSave = async () => {
    // For practice activities, validate the practice activity form first
    if (
      activity?.type === "practice_activity" &&
      practiceActivityFormRef.current
    ) {
      const validationResult =
        practiceActivityFormRef.current.validateAndGetData();

      if (!validationResult.isValid) {
        showError(
          "Validation Error",
          "Please fill in all required fields in the Practice Activity Configuration.",
        );
        return;
      }

      // Update the practice activity data with the validated data
      if (validationResult.data) {
        setPracticeActivityData(validationResult.data);
      }
    }

    // Create extra_fields object with heygen_video_id, heygen_video_url, and practice activity data
    const extraFieldsObj: any = {};

    if (selectedHeygenVideoId) {
      extraFieldsObj.heygen_video_id = selectedHeygenVideoId;
      extraFieldsObj.heygen_video_url = selectedHeygenVideoUrl;
    }

    // Use the latest practice activity data (either from state or from validation)
    const currentPracticeActivityData =
      activity?.type === "practice_activity" && practiceActivityFormRef.current
        ? practiceActivityFormRef.current.validateAndGetData().data
        : practiceActivityData;

    console.log(
      "ActivityDevelopment: handleSave - currentPracticeActivityData:",
      currentPracticeActivityData,
    );

    if (activity?.type === "practice_activity" && currentPracticeActivityData) {
      console.log(
        "ActivityDevelopment: Adding practice activity data to extraFieldsObj",
      );
      console.log(
        "ActivityDevelopment: Number of criteria being saved:",
        currentPracticeActivityData.evaluationRubric?.criteria?.length || 0,
      );
      currentPracticeActivityData.evaluationRubric?.criteria?.forEach(
        (criterion, index) => {
          console.log(
            `ActivityDevelopment: Saving Criterion ${index + 1}:`,
            criterion.name,
            "Questions:",
            criterion.questions?.length || 0,
          );
        },
      );
      extraFieldsObj.practiceActivityData = currentPracticeActivityData;
    }

    const extraFields =
      Object.keys(extraFieldsObj).length > 0
        ? JSON.stringify(extraFieldsObj)
        : "";

    console.log("Saving activity with data:", {
      selectedHeygenVideoId,
      selectedHeygenVideoUrl,
      practiceActivityData: JSON.stringify(currentPracticeActivityData),
      extraFields,
    });

    // Create custom instructions with heygen video information embedded
    // We're storing the video info in both extra_fields and as a hidden div in instructions
    // for backward compatibility
    const instructionsWithHeygen = selectedHeygenVideoId
      ? `${instructions}<div data-heygen-video-id="${selectedHeygenVideoId}" data-heygen-video-url="${selectedHeygenVideoUrl}" style="display:none;"></div>`
      : instructions;

    mutation.mutate(
      {
        activityId: activity_id,
        name: activityName,
        instructions: instructionsWithHeygen,
        instructions_below: instructionsBelow,
        instructions_cp: instructionsCP,
        instructions_cp_below: instructionsCPBelow,
        instructions_rec: instructionsRec,
        instructions_rec_below: instructionsRecBelow,
        instructions_wr: instructionsWr,
        instructions_wr_below: instructionsWrBelow,
        videoTitle: videoTitle,
        videoFile: file,
        secondVideoFile: secondVideo,
        mandatory: String(mandatory),
        fileLink: fileLink,
        pdfFile: pdfFile,
        extra_fields: extraFields,
        practiceActivityData: JSON.stringify(currentPracticeActivityData),
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ["activity", activity_id],
          });
          navigate(`/instructor/course-development?course_id=${course_id}`);
          showSuccess("Success", "Activity Updated Successfully!");
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  const handleMCQSave = (updatedQuestions: Question[]) => {
    mutation.mutate(
      {
        activityId: activity_id,
        name: "",
        mcqs: JSON.stringify(updatedQuestions),
        instructions: instructions,
        mandatory: String(mandatory),
        showCorrectOption: String(showCorrectOption),
      },
      {
        onSuccess: () => {
          showSuccess("Success", "Activity Updated Successfully!");
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  const handleQuestionsSave = (
    updatedQuestions: { question: string; options: string[] }[],
  ) => {
    mutation.mutate(
      {
        activityId: activity_id,
        name: "",
        instructions: instructions,
        questions: JSON.stringify(updatedQuestions),
        mandatory: String(mandatory),
      },
      {
        onSuccess: () => {
          showSuccess("Success", "Activity Updated Successfully!");
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  const handleInstructionsChange = (value: string) => {
    setInstructions(value);
  };
  const handleInstructionsBelowChange = (value: string) => {
    setInstructionsBelow(value);
  };
  const handleInstructionsCPChange = (value: string) => {
    setInstructionsCP(value);
  };
  const handleInstructionsCPBelowChange = (value: string) => {
    setInstructionsCPBelow(value);
  };
  const handleInstructionsRecChange = (value: string) => {
    setInstructionsRec(value);
  };
  const handleInstructionsRecBelChange = (value: string) => {
    setInstructionsRecBelow(value);
  };
  const handleInstructionsWRChange = (value: string) => {
    setInstructionsWr(value);
  };
  const handleInstructionsWRBelChange = (value: string) => {
    setInstructionsWrBelow(value);
  };

  const handleSelectHeygenVideo = (videoUrl: string, videoId: number) => {
    console.log("Selected Heygen video:", { videoUrl, videoId });

    // First, clear the selection to force a UI update
    setSelectedHeygenVideoId(undefined);
    setSelectedHeygenVideoUrl("");

    // Small delay to ensure the reset is processed before setting the new value
    setTimeout(() => {
      // Set new values
      setSelectedHeygenVideoUrl(videoUrl);
      setSelectedHeygenVideoId(videoId);

      // We've now explicitly selected a video, so we don't want to load from activity anymore
      setInitialHeygenVideoLoaded(true);

      // Show a success message
      const videoName =
        heygenVideos?.find((v) => v.id === videoId)?.name ||
        `Video #${videoId}`;
      showSuccess(
        "Video Selected",
        `The video "${videoName}" has been selected for this activity.`,
      );
    }, 50);
  };

  const handleClearHeygenVideo = () => {
    console.log("Clearing Heygen video selection");

    // Clear the selection
    setSelectedHeygenVideoUrl("");
    setSelectedHeygenVideoId(undefined);

    // Mark as explicitly changed
    setInitialHeygenVideoLoaded(true);

    // Show a success message
    showSuccess("Video Cleared", "The video selection has been cleared.");
  };

  return (
    <>
      <Spinner loading={isFetching || mutation.isPending} />
      <div className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-1">
          <input
            type="checkbox"
            className="size-5 rounded border-4 border-black accent-white"
            checked={mandatory}
            onChange={() => setMandatory(!mandatory)}
          />
          <label>Mandatory</label>
        </div>
        <button
          className="h-10 w-48 rounded-lg bg-[#36537F] text-center text-white"
          onClick={() => navigate(-1)}
        >
          Back
        </button>
      </div>
      {activity?.type === "practice_activity" ? (
        <Box
          sx={{
            width: "100%",
            height: "100%",
            margin: "auto",
            padding: "20px",
            backgroundColor: "#FAF7F0",
            borderRadius: "8px",
          }}
        >
          <Typography variant="h6" gutterBottom>
            Activity Name
          </Typography>

          <input
            type="text"
            className="mb-4 h-8 w-full rounded-md border border-gray-300 px-2"
            value={activityName}
            placeholder="Activity Name"
            onChange={(e) => setActvityName(e.target.value)}
          />

          <Typography variant="h6" gutterBottom>
            Instructions Above the Video (If Needed)
          </Typography>

          {/* <ReactQuill
            value={instructions}
            onChange={handleInstructionsChange}
            modules={modules}
            formats={formats}
          />
           */}
          <textarea
            //remove paragraph tags
            value={instructions.replace(/<\/?p>/g, "")}
            onChange={(e) =>
              // add paragraph tags
              setInstructions(`<p>${e.target.value}</p>`)
            }
            className="mb-4 h-24 w-full resize-none rounded-md border border-gray-300 px-2"
            placeholder="Instructions"
          />

          <Typography variant="body1" gutterBottom>
            Video File: .mp4
          </Typography>

          <Stack direction="row" spacing={2} sx={{ marginBottom: "16px" }}>
            <Button
              variant="outlined"
              component="label"
              sx={{
                flex: 1,
                padding: "20px",
                border: "1px dashed #aaa",
              }}
            >
              Upload a File
              <input
                type="file"
                accept="video/mp4"
                hidden
                onChange={handleFileChange}
              />
            </Button>

            <Button
              variant="contained"
              color="primary"
              onClick={() => {
                handleGenerateVideo();
              }}
              sx={{
                flex: 1,
                padding: "20px",
                backgroundColor: "#145DA0",
                "&:hover": {
                  backgroundColor: "#0D4A8F",
                },
              }}
            >
              Create Scenario
            </Button>
          </Stack>

          {file && <Typography variant="body2">{file.name}</Typography>}

          {/* Second Video Upload for Practice Activity */}
          <Typography variant="body1" gutterBottom>
            Second Video File: .mp4
          </Typography>

          <Button
            variant="outlined"
            component="label"
            sx={{
              display: "block",
              width: "100%",
              padding: "20px",
              border: "1px dashed #aaa",
              marginBottom: "16px",
            }}
          >
            Upload a File
            <input
              type="file"
              accept="video/mp4"
              hidden
              onChange={handleSecondVideoChange}
            />
          </Button>

          {secondVideo && (
            <Typography variant="body2">{secondVideo.name}</Typography>
          )}

          {/* Generated Video Selection */}
          <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
            Or Select a Generated Video
          </Typography>
          <Typography variant="body2" gutterBottom>
            You can use a generated video or upload a file instead.
          </Typography>

          {/* Button to open the modal */}
          <button
            onClick={() => setShowVideoModal(true)}
            className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
          >
            Select Generated Video
          </button>

          {/* Modal for video selection */}
          {showVideoModal && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
              <div className="max-h-[90vh] w-[90vw] max-w-4xl overflow-auto rounded-lg bg-white p-6">
                <div className="mb-4 flex justify-between">
                  <h3 className="text-xl font-medium">
                    Select a Generated Video
                  </h3>
                  <button
                    onClick={() => setShowVideoModal(false)}
                    className="rounded-full p-1 hover:bg-gray-200"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <HeygenVideoSelector
                  onSelectVideo={(videoUrl, videoId) => {
                    handleSelectHeygenVideo(videoUrl, videoId);
                    setShowVideoModal(false);
                  }}
                  selectedVideoId={selectedHeygenVideoId}
                />
              </div>
            </div>
          )}

          {selectedHeygenVideoId && (
            <div className="mt-4">
              <div className="mb-2 rounded-md bg-blue-50 p-2">
                <p className="text-sm font-medium text-blue-800">
                  Currently selected:
                  <span className="ml-1 font-bold">
                    {heygenVideos?.find(
                      (v: HeygenVideoWithUrl) => v.id === selectedHeygenVideoId,
                    )?.name || "Video #" + selectedHeygenVideoId}
                  </span>
                </p>
              </div>
              <button
                onClick={handleClearHeygenVideo}
                className="rounded bg-red-100 px-3 py-1 text-sm text-red-700 hover:bg-red-200"
              >
                Clear Video Selection
              </button>
            </div>
          )}

          <Typography variant="h6" gutterBottom>
            Instructions Below the Video (If Needed)
          </Typography>

          {/* <ReactQuill
            value={instructionsBelow}
            onChange={handleInstructionsBelowChange}
            modules={modules}
            formats={formats}
          /> */}
          <textarea
            //remove paragraph tags
            value={instructionsBelow.replace(/<\/?p>/g, "")}
            onChange={(e) =>
              // add paragraph tags
              handleInstructionsBelowChange(`<p>${e.target.value}</p>`)
            }
            className="mb-4 h-24 w-full resize-none rounded-md border border-gray-300 px-2"
            placeholder="Instructions"
          />

          <Typography variant="h6" gutterBottom>
            Instructions Above Camera Preview (If Needed)
          </Typography>

          {/* <ReactQuill
            value={instructionsCP}
            onChange={handleInstructionsCPChange}
            modules={modules}
            formats={formats}
          /> */}
          <textarea
            //remove paragraph tags
            value={instructionsCP.replace(/<\/?p>/g, "")}
            onChange={(e) =>
              // add paragraph tags
              handleInstructionsCPChange(`<p>${e.target.value}</p>`)
            }
            className="mb-4 h-24 w-full resize-none rounded-md border border-gray-300 px-2"
            placeholder="Instructions"
          />

          <Typography variant="h6" gutterBottom>
            Instructions Below Camera Preview (If Needed)
          </Typography>

          {/* <ReactQuill
            value={instructionsCPBelow}
            onChange={handleInstructionsCPBelowChange}
            modules={modules}
            formats={formats}
          /> */}
          <textarea
            //remove paragraph tags
            value={instructionsCPBelow.replace(/<\/?p>/g, "")}
            onChange={(e) =>
              // add paragraph tags
              handleInstructionsCPBelowChange(`<p>${e.target.value}</p>`)
            }
            className="mb-4 h-24 w-full resize-none rounded-md border border-gray-300 px-2"
            placeholder="Instructions"
          />

          <Typography variant="h6" gutterBottom>
            Instructions Above Recording Response (If Needed)
          </Typography>

          {/* <ReactQuill
            value={instructionsRec}
            onChange={handleInstructionsRecChange}
            modules={modules}
            formats={formats}
          /> */}
          <textarea
            //remove paragraph tags
            value={instructionsRec.replace(/<\/?p>/g, "")}
            onChange={(e) =>
              // add paragraph tags
              handleInstructionsRecChange(`<p>${e.target.value}</p>`)
            }
            className="mb-4 h-24 w-full resize-none rounded-md border border-gray-300 px-2"
            placeholder="Instructions"
          />

          <Typography variant="h6" gutterBottom>
            Instructions Above Waiting Results (If Needed)
          </Typography>

          {/* <ReactQuill
            value={instructionsWr}
            onChange={handleInstructionsWRChange}
            modules={modules}
            formats={formats}
          /> */}
          <textarea
            //remove paragraph tags
            value={instructionsWr.replace(/<\/?p>/g, "")}
            onChange={(e) =>
              // add paragraph tags
              handleInstructionsWRChange(`<p>${e.target.value}</p>`)
            }
            className="mb-4 h-24 w-full resize-none rounded-md border border-gray-300 px-2"
            placeholder="Instructions"
          />

          {/* Practice Activity Structured Form */}
          <Typography variant="h4" gutterBottom sx={{ mt: 4, mb: 2 }}>
            Practice Activity Configuration
          </Typography>

          {practiceActivityData && (
            <PracticeActivityForm
              key={`practice-form-${activity_id}-${practiceActivityData.evaluationRubric?.criteria?.length || 0}-${practiceActivityData.evaluationRubric?.criteria?.map((c) => c.questions?.length || 0).join("-") || "0"}`}
              ref={practiceActivityFormRef}
              initialData={practiceActivityData}
              onSave={() => {}} // Dummy function since we're using ref-based validation
              isLoading={mutation.isPending}
            />
          )}

          <button
            className="ml-4 mt-6 w-[200px] rounded-md bg-[#EEC300] px-4 py-2 text-white"
            onClick={() => handleSave()}
          >
            Save
          </button>
        </Box>
      ) : activity &&
        ["recap", "tutorial", "plunge_activity", "boss_challenge"].includes(
          activity?.type,
        ) ? (
        <Box
          sx={{
            width: "100%",
            height: "100%",
            margin: "auto",
            padding: "20px",
            backgroundColor: "#FAF7F0",
            borderRadius: "8px",
          }}
        >
          <Typography variant="h6" gutterBottom>
            Activity Name
          </Typography>

          <input
            type="text"
            className="mb-4 h-8 w-full rounded-md border border-gray-300 px-2"
            value={activityName}
            placeholder="Activity Name"
            onChange={(e) => setActvityName(e.target.value)}
          />

          {!["boss_challenge"].includes(activity?.type) ? (
            <>
              <Typography variant="h6" gutterBottom>
                Video Title (If Needed)
              </Typography>

              <input
                type="text"
                className="mb-4 h-8 w-full rounded-md border border-gray-300 px-2"
                value={videoTitle}
                placeholder="Add Video Title (If required)"
                onChange={(e) => setVideoTitle(e.target.value)}
              />
            </>
          ) : null}

          {activity?.type === "quiz" ? (
            <Typography variant="h6" gutterBottom>
              Instructions (If Needed)
            </Typography>
          ) : (
            <Typography variant="h6" gutterBottom>
              Instructions Above the Video (If Needed)
            </Typography>
          )}

          <ReactQuill
            value={instructions}
            onChange={handleInstructionsChange}
            modules={modules}
            formats={formats}
          />

          <Typography variant="body1" gutterBottom>
            Video File: .mp4
          </Typography>

          <Stack direction="row" spacing={2} sx={{ marginBottom: "16px" }}>
            <Button
              variant="outlined"
              component="label"
              sx={{
                flex: 1,
                padding: "20px",
                border: "1px dashed #aaa",
              }}
            >
              Upload a File
              <input
                type="file"
                accept="video/mp4"
                hidden
                onChange={handleFileChange}
              />
            </Button>

            <Button
              variant="contained"
              color="primary"
              onClick={() => {
                handleGenerateVideo();
              }}
              sx={{
                flex: 1,
                padding: "20px",
                backgroundColor: "#145DA0",
                "&:hover": {
                  backgroundColor: "#0D4A8F",
                },
              }}
            >
              {activity.type === "tutorial"
                ? "Generate Video"
                : "Create Scenario"}
            </Button>
          </Stack>

          {file && <Typography variant="body2">{file.name}</Typography>}

          {["boss_challenge", "tutorial"].includes(activity?.type) && (
            <>
              {["boss_challenge"].includes(activity?.type) && (
                <>
                  <Typography variant="body1" gutterBottom>
                    Second Video File: .mp4
                  </Typography>

                  <Button
                    variant="outlined"
                    component="label"
                    sx={{
                      display: "block",
                      width: "100%",
                      padding: "20px",
                      border: "1px dashed #aaa",
                      marginBottom: "16px",
                    }}
                  >
                    Upload a File
                    <input
                      type="file"
                      accept="video/mp4"
                      hidden
                      onChange={handleSecondVideoChange}
                    />
                  </Button>

                  {file && <Typography variant="body2">{file.name}</Typography>}
                </>
              )}

              <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                {["boss_challenge"].includes(activity?.type)
                  ? "Or Select a Generated Video"
                  : "Select a Generated Video"}
              </Typography>
              <Typography variant="body2" gutterBottom>
                You can use a generated video or upload a file instead.
              </Typography>

              {/* Button to open the modal */}
              <button
                onClick={() => setShowVideoModal(true)}
                className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
              >
                Select Generated Video
              </button>

              {/* Modal for video selection */}
              {showVideoModal && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                  <div className="max-h-[90vh] w-[90vw] max-w-4xl overflow-auto rounded-lg bg-white p-6">
                    <div className="mb-4 flex justify-between">
                      <h3 className="text-xl font-medium">
                        Select a Generated Video
                      </h3>
                      <button
                        onClick={() => setShowVideoModal(false)}
                        className="rounded-full p-1 hover:bg-gray-200"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>

                    <HeygenVideoSelector
                      onSelectVideo={(videoUrl, videoId) => {
                        handleSelectHeygenVideo(videoUrl, videoId);
                        setShowVideoModal(false);
                      }}
                      selectedVideoId={selectedHeygenVideoId}
                    />
                  </div>
                </div>
              )}

              {selectedHeygenVideoId && (
                <div className="mt-4">
                  <div className="mb-2 rounded-md bg-blue-50 p-2">
                    <p className="text-sm font-medium text-blue-800">
                      Currently selected:
                      <span className="ml-1 font-bold">
                        {heygenVideos?.find(
                          (v: HeygenVideoWithUrl) =>
                            v.id === selectedHeygenVideoId,
                        )?.name || "Video #" + selectedHeygenVideoId}
                      </span>
                    </p>
                  </div>
                  <button
                    onClick={handleClearHeygenVideo}
                    className="rounded bg-red-100 px-3 py-1 text-sm text-red-700 hover:bg-red-200"
                  >
                    Clear Video Selection
                  </button>
                </div>
              )}
            </>
          )}

          {activity?.type !== "quiz" && (
            <>
              <Typography variant="h6" gutterBottom>
                Instructions Below the Video (If Needed)
              </Typography>

              <ReactQuill
                value={instructionsBelow}
                onChange={handleInstructionsBelowChange}
                modules={modules}
                formats={formats}
              />
            </>
          )}

          {["boss_challenge"].includes(activity?.type) && (
            <>
              <Typography variant="h6" gutterBottom>
                Instructions Above Camera Preview (If Needed)
              </Typography>

              <ReactQuill
                value={instructionsCP}
                onChange={handleInstructionsCPChange}
                modules={modules}
                formats={formats}
              />

              <Typography variant="h6" gutterBottom>
                Instructions Below Camera Preview (If Needed)
              </Typography>

              <ReactQuill
                value={instructionsCPBelow}
                onChange={handleInstructionsCPBelowChange}
                modules={modules}
                formats={formats}
              />

              <Typography variant="h6" gutterBottom>
                Instructions Above Recording Response (If Needed)
              </Typography>

              <ReactQuill
                value={instructionsRec}
                onChange={handleInstructionsRecChange}
                modules={modules}
                formats={formats}
              />

              {/* <Typography variant="h6" gutterBottom>
                Instructions Below Recording Response (If Needed)
              </Typography>

              <ReactQuill
                value={instructionsRecBelow}
                onChange={handleInstructionsRecBelChange}
                modules={modules}
                formats={formats}
              /> */}

              <Typography variant="h6" gutterBottom>
                Instructions Above Waiting Results (If Needed)
              </Typography>

              <ReactQuill
                value={instructionsWr}
                onChange={handleInstructionsWRChange}
                modules={modules}
                formats={formats}
              />
              {/*
              <Typography variant="h6" gutterBottom>
                Instructions Below Waiting Results (If Needed)
              </Typography>

              <ReactQuill
                value={instructionsWrBelow}
                onChange={handleInstructionsWRBelChange}
                modules={modules}
                formats={formats}
              /> */}
            </>
          )}

          <button
            className="ml-4 mt-6 w-[200px] rounded-md bg-[#EEC300] px-4 py-2 text-white"
            onClick={() => handleSave()}
          >
            Save
          </button>
        </Box>
      ) : activity?.type == "additional_resources" ? (
        <Box
          sx={{
            width: "100%",
            height: "100%",
            margin: "auto",
            padding: "20px",
            backgroundColor: "#FAF7F0",
            borderRadius: "8px",
          }}
        >
          <Typography variant="h6" gutterBottom>
            Activity Name
          </Typography>

          <input
            type="text"
            className="mb-4 h-8 w-full rounded-md border border-gray-300 px-2"
            value={activityName}
            placeholder="Activity Name"
            onChange={(e) => setActvityName(e.target.value)}
          />

          <Typography variant="h6" gutterBottom>
            File Link
          </Typography>

          <input
            type="text"
            className="mb-4 h-8 w-full rounded-md border border-gray-300 px-2"
            value={fileLink}
            placeholder="Add File Link"
            onChange={(e) => setFileLink(e.target.value)}
          />

          <Typography variant="body1" gutterBottom>
            PDF File: .pdf
          </Typography>

          <Button
            variant="outlined"
            component="label"
            sx={{
              display: "block",
              width: "100%",
              padding: "20px",
              border: "1px dashed #aaa",
              marginBottom: "16px",
            }}
          >
            Upload a File
            <input
              type="file"
              accept="application/pdf"
              hidden
              onChange={handlePDFFileChange}
            />
          </Button>

          {pdfFile && (
            <Typography variant="body2" sx={{ marginBottom: "16px" }}>
              {pdfFile.name}
            </Typography>
          )}
          {!pdfFile && activity?.file_path && (
            <Typography variant="body2" sx={{ marginBottom: "16px" }}>
              Current file: {activity.file_path.split("/").pop()}
            </Typography>
          )}

          {/* <TextField
            label="Instructions (if needed)"
            multiline
            rows={4}
            fullWidth
            variant="outlined"
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            sx={{ marginBottom: '16px' }}
          /> */}
          <button
            className="ml-4 mt-6 w-[200px] rounded-md bg-[#EEC300] px-4 py-2 text-white"
            onClick={() => handleSave()}
          >
            Save
          </button>
        </Box>
      ) : activity?.type == "quiz" ? (
        <>
          <div className="flex items-center gap-1">
            <input
              type="checkbox"
              className="size-5 rounded border-4 border-black accent-white"
              checked={showCorrectOption}
              onChange={() => setShowCorrectOption(!showCorrectOption)}
            />
            <label>Show Correct Option</label>
          </div>

          <div className="mx-auto w-full rounded-lg bg-gray-100 p-6">
            <Typography variant="h6" gutterBottom>
              Instructions to be added before the activity
            </Typography>
            <ReactQuill
              value={instructions}
              onChange={handleInstructionsChange}
              modules={modules}
              formats={formats}
              theme="snow"
            />
          </div>
          <EditQuiz initialQuestions={activity.mcqs} onSave={handleMCQSave} />
        </>
      ) : activity?.type == "pre_course_survey" ? (
        <>
          <div className="mx-auto w-full rounded-lg bg-gray-100 p-6">
            <Typography variant="h6" gutterBottom>
              Instructions to be added before the activity
            </Typography>
            <ReactQuill
              value={instructions}
              onChange={handleInstructionsChange}
              style={{ marginBottom: "16px" }}
              modules={modules}
              formats={formats}
            />
          </div>
          <EditSurveyQuestionList
            surveyType={activity.type}
            initialQuestions={activity.questions}
            onSave={handleQuestionsSave}
          />
        </>
      ) : activity?.type == "feedback" ? (
        <>
          <div className="mx-auto w-full rounded-lg bg-gray-100 p-6">
            <ReactQuill
              value={instructions}
              onChange={handleInstructionsChange}
              style={{ marginBottom: "16px" }}
              modules={modules}
              formats={formats}
            />
          </div>
          <EditFeedbackQuestionList
            surveyType={activity.type}
            initialQuestions={activity.questions}
            onSave={handleQuestionsSave}
          />
        </>
      ) : null}
    </>
  );
}
