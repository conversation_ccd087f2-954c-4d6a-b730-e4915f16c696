import React from "react";

interface Props {
  selected?: boolean;
}

function AnalyticsIcon({ selected }: Props) {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16 0H0V16H16V0ZM2 14V2H14V14H2ZM4 8H6V12H4V8ZM10 4H8V12H10V4ZM12 6H14V12H12V6Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default AnalyticsIcon;
