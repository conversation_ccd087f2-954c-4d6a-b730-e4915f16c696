import { useState } from "react";
import { Card } from "./ui/card";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import {
  Search,
  User,
  TrendingUp,
  Clock,
  Award,
  GraduationCap,
} from "lucide-react";
import useCohortSummaries from "../hooks/useCohortSummaries";
import useActivityResults from "../hooks/useActivityResults";

interface IndividualViewProps {
  selectedCourse: string;
  onCourseChange: (course: string) => void;
  selectedTimePeriod: string;
}

export function IndividualView({
  selectedCourse,
  onCourseChange,
  selectedTimePeriod,
}: IndividualViewProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCohort, setSelectedCohort] = useState("all");
  const [selectedActivity, setSelectedActivity] = useState("all");
  const { data: mockCohortSummaries, isLoading } = useCohortSummaries();
  const { data: mockActivityResults, isLoading: isResultsLoading } =
    useActivityResults();

  if (isLoading || isResultsLoading) {
    return <div>Loading...</div>;
  }

  // Helper function to ensure valid numeric values
  const ensureValidNumber = (value: any): number => {
    const num = Number(value);
    return isNaN(num) || !isFinite(num) ? 0 : num;
  };

  // Helper function to get initials from name
  const getInitials = (name: string): string => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Helper function to get progress bar color
  const getProgressBarColor = (percentage: number): string => {
    if (percentage >= 70) return "bg-green-500";
    if (percentage >= 50) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Filter data based on selected course
  const courseFilteredResults = mockActivityResults.filter(
    (result) => result.course === selectedCourse,
  );

  // Get unique values for filters from course-filtered data
  const courses = [...new Set(mockCohortSummaries.map((c) => c.course))];
  const cohorts = [...new Set(courseFilteredResults.map((r) => r.cohort))];
  const activities = [
    ...new Set(
      courseFilteredResults.map(
        (r) =>
          `${r.activityType}${r.activityNumber > 1 ? ` ${r.activityNumber}` : ""}`,
      ),
    ),
  ];

  // Filter results based on search and filters
  const filteredResults = courseFilteredResults.filter((result) => {
    const activityName = `${result.activityType}${result.activityNumber > 1 ? ` ${result.activityNumber}` : ""}`;
    const matchesSearch =
      result.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      result.cohort.toLowerCase().includes(searchTerm.toLowerCase()) ||
      result.course.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCohort =
      selectedCohort === "all" || result.cohort === selectedCohort;
    const matchesActivity =
      selectedActivity === "all" || activityName === selectedActivity;

    return matchesSearch && matchesCohort && matchesActivity;
  });

  // Get student summary data
  const getStudentSummary = () => {
    const studentMap = new Map();

    courseFilteredResults.forEach((result) => {
      if (!studentMap.has(result.studentId)) {
        studentMap.set(result.studentId, {
          id: result.studentId,
          name: result.studentName,
          cohort: result.cohort,
          course: result.course,
          activities: {},
          totalScore: 0,
          completedActivities: 0,
          averageScore: 0,
          totalTimeSpent: 0,
        });
      }

      const student = studentMap.get(result.studentId);
      const activityKey = `${result.activityType}${result.activityNumber > 1 ? ` ${result.activityNumber}` : ""}`;
      student.activities[activityKey] = {
        score: ensureValidNumber(result.score),
        maxScore: ensureValidNumber(result.maxScore),
        timeSpent: ensureValidNumber(result.timeSpent),
        completedAt: result.completedAt,
      };
      student.totalScore += ensureValidNumber(result.score);
      student.completedActivities++;
      student.totalTimeSpent += ensureValidNumber(result.timeSpent);
    });

    // Calculate averages
    studentMap.forEach((student) => {
      student.averageScore =
        student.completedActivities > 0
          ? student.totalScore / student.completedActivities
          : 0;
    });

    return Array.from(studentMap.values()).sort(
      (a, b) => b.averageScore - a.averageScore,
    );
  };

  const studentSummaries = getStudentSummary();

  // Get students who need attention (below 70% in AI activities)
  const getStudentsNeedingAttention = () => {
    const studentMap = new Map();

    // Filter for AI activities only
    const aiResults = courseFilteredResults.filter(
      (result) => result.activityType === "AI Activity",
    );

    aiResults.forEach((result) => {
      if (!studentMap.has(result.studentId)) {
        studentMap.set(result.studentId, {
          id: result.studentId,
          name: result.studentName,
          cohort: result.cohort,
          aiScores: [],
          aiAverage: 0,
        });
      }

      const student = studentMap.get(result.studentId);
      const percentage = (result.score / result.maxScore) * 100;
      student.aiScores.push(percentage);
    });

    // Calculate AI averages and filter those below 70%
    const studentsNeedingAttention = [];
    studentMap.forEach((student) => {
      if (student.aiScores.length > 0) {
        student.aiAverage =
          student.aiScores.reduce((sum, score) => sum + score, 0) /
          student.aiScores.length;
        if (student.aiAverage < 70) {
          studentsNeedingAttention.push(student);
        }
      }
    });

    return studentsNeedingAttention.sort((a, b) => a.aiAverage - b.aiAverage);
  };

  const studentsNeedingAttention = getStudentsNeedingAttention();

  // Calculate safe summary statistics
  const totalStudents = studentSummaries.length;
  const averagePerformance =
    totalStudents > 0
      ? studentSummaries.reduce((sum, s) => sum + s.averageScore, 0) /
        totalStudents
      : 0;
  const topPerformer = studentSummaries[0];
  const averageTimeSpent =
    totalStudents > 0
      ? studentSummaries.reduce((sum, s) => sum + s.totalTimeSpent, 0) /
        totalStudents
      : 0;

  return (
    <div className="space-y-6">
      {/* Course Selection */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <GraduationCap className="text-primary h-5 w-5" />
            <h3>Select Course</h3>
          </div>
          <select
            value={selectedCourse}
            onChange={(e) => onCourseChange(e.target.value)}
            className="bg-background min-w-[200px] rounded-md border px-4 py-2"
          >
            {courses.map((course) => (
              <option key={course} value={course}>
                {course}
              </option>
            ))}
          </select>
        </div>
      </Card>

      {/* Search and Filters */}
      <Card className="p-6">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center space-x-2">
            <Search className="text-muted-foreground h-4 w-4" />
            <h3>Search & Filter Students</h3>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="relative">
              <Search className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform" />
              <Input
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <select
              value={selectedCohort}
              onChange={(e) => setSelectedCohort(e.target.value)}
              className="bg-background rounded-md border px-3 py-2"
            >
              <option value="all">All Cohorts</option>
              {cohorts.map((cohort) => (
                <option key={cohort} value={cohort}>
                  {cohort}
                </option>
              ))}
            </select>

            <select
              value={selectedActivity}
              onChange={(e) => setSelectedActivity(e.target.value)}
              className="bg-background rounded-md border px-3 py-2"
            >
              <option value="all">All Activities</option>
              {activities.map((activity) => (
                <option key={activity} value={activity}>
                  {activity}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <User className="text-primary h-4 w-4" />
            <div className="flex flex-col">
              <span className="text-muted-foreground text-sm">
                Total Students
              </span>
              <span className="text-2xl font-bold">{totalStudents}</span>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <Award className="text-primary h-4 w-4" />
            <div className="flex flex-col">
              <span className="text-muted-foreground text-sm">
                Avg Performance
              </span>
              <span className="text-2xl font-bold">
                {averagePerformance.toFixed(1)}%
              </span>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <TrendingUp className="text-primary h-4 w-4" />
            <div className="flex flex-col">
              <span className="text-muted-foreground text-sm">
                Top Performer
              </span>
              <span className="text-lg font-bold">
                {topPerformer?.name || "N/A"}
              </span>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <Clock className="text-primary h-4 w-4" />
            <div className="flex flex-col">
              <span className="text-muted-foreground text-sm">
                Avg Time Spent
              </span>
              <span className="text-2xl font-bold">
                {averageTimeSpent.toFixed(0)}m
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Students Who Need Attention */}
      {studentsNeedingAttention.length > 0 && (
        <Card className="p-6">
          <h3 className="mb-4">Students Who Need Attention</h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {studentsNeedingAttention.map((student, index) => (
              <div
                key={`${student.id}-attention-${index}`}
                className="flex items-center space-x-3 rounded-lg border p-3"
              >
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                  <span className="text-sm font-medium text-blue-700">
                    {getInitials(student.name)}
                  </span>
                </div>
                <div className="flex-1">
                  <div className="font-medium">{student.name}</div>
                  <div className="text-muted-foreground text-sm">
                    {student.cohort}
                  </div>
                  <div className="mt-1">
                    <div className="mb-1 flex items-center justify-between text-xs">
                      <span>AI Activity Performance</span>
                      <span>{student.aiAverage.toFixed(1)}%</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-gray-200">
                      <div
                        className={`h-2 rounded-full ${getProgressBarColor(student.aiAverage)}`}
                        style={{ width: `${Math.max(student.aiAverage, 5)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Individual Student Performance */}
      <Card className="p-6">
        <h3 className="mb-4">Individual Student Performance</h3>
        <div className="space-y-4">
          {studentSummaries.length > 0 ? (
            studentSummaries.slice(0, 10).map((student, index) => (
              <div
                key={`${student.id}-${index}`}
                className="rounded-lg border p-4"
              >
                <div className="mb-3 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                      <User className="text-primary h-5 w-5" />
                    </div>
                    <div>
                      <h4>{student.name}</h4>
                      <div className="text-muted-foreground text-sm">
                        {student.cohort} • {student.course}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge
                      variant={
                        student.averageScore >= 90
                          ? "default"
                          : student.averageScore >= 80
                            ? "secondary"
                            : "outline"
                      }
                    >
                      {student.averageScore.toFixed(1)}% avg
                    </Badge>
                    <div className="text-muted-foreground mt-1 text-sm">
                      {student.totalTimeSpent}m total
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  {Object.entries(student.activities).map(
                    ([activityType, activity]: [string, any]) => (
                      <div
                        key={activityType}
                        className="bg-secondary/50 rounded p-3"
                      >
                        <div className="text-sm font-medium">
                          {activityType}
                        </div>
                        <div className="mt-1 flex items-center justify-between">
                          <span className="text-lg font-bold">
                            {activity.score}/{activity.maxScore}
                          </span>
                          <span className="text-muted-foreground text-sm">
                            {activity.timeSpent}m
                          </span>
                        </div>
                        <div className="text-muted-foreground mt-1 text-xs">
                          Completed: {activity.completedAt}
                        </div>
                      </div>
                    ),
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-muted-foreground py-8 text-center">
              No student data available for {selectedCourse}
            </div>
          )}
        </div>
      </Card>

      {/* Detailed Results Table */}
      <Card className="p-6">
        <h3 className="mb-4">
          Detailed Individual Results ({filteredResults.length} results)
        </h3>
        <div className="overflow-x-auto">
          {filteredResults.length > 0 ? (
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="p-2 text-left">Student</th>
                  <th className="p-2 text-left">Cohort</th>
                  <th className="p-2 text-left">Course</th>
                  <th className="p-2 text-left">Activity</th>
                  <th className="p-2 text-left">Score</th>
                  <th className="p-2 text-left">Percentage</th>
                  <th className="p-2 text-left">Time</th>
                  <th className="p-2 text-left">Date</th>
                </tr>
              </thead>
              <tbody>
                {filteredResults.map((result, index) => (
                  <tr
                    key={`${result.studentId}-${result.activityType}-${result.activityNumber}-${index}`}
                    className="hover:bg-secondary/20 border-b"
                  >
                    <td className="p-2">{result.studentName}</td>
                    <td className="p-2">
                      <Badge variant="outline">{result.cohort}</Badge>
                    </td>
                    <td className="p-2">
                      <Badge variant="outline">{result.course}</Badge>
                    </td>
                    <td className="p-2">
                      <Badge variant="secondary">
                        {result.activityType}
                        {result.activityNumber > 1 &&
                          ` ${result.activityNumber}`}
                      </Badge>
                    </td>
                    <td className="p-2">
                      {result.score}/{result.maxScore}
                    </td>
                    <td className="p-2">
                      <Badge
                        variant={
                          result.score >= 90
                            ? "default"
                            : result.score >= 80
                              ? "secondary"
                              : "outline"
                        }
                      >
                        {((result.score / result.maxScore) * 100).toFixed(1)}%
                      </Badge>
                    </td>
                    <td className="p-2">{result.timeSpent}m</td>
                    <td className="p-2">{result.completedAt}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-muted-foreground py-8 text-center">
              No results match your current filters
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
