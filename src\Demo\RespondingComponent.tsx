import React from "react";

interface FacialExpressions {
  calmAndApproachableExpression: string;
  engagedListening: string;
  noSignsOfFrustrationOrAnnoyance: string;
  supportiveGestures: string;
  openAndRelaxedFacialFeatures: string;
}

interface SoundToneOfVoice {
  calmAndSteadyTone: string;
  empatheticTone: string;
  clearArticulation: string;
  nonDefensiveTone: string;
  appropriateVolume: string;
}

interface TextChoiceOfWords {
  acknowledgmentOfTheIssue: string;
  useOfPoliteLanguage: string;
  solutionOrientedWords: string;
  apologeticAndResponsibleLanguage: string;
  avoidanceOfBlamingLanguage: string;
}

interface Feedback {
  positiveAreas: string;
  improvementSuggestions: string;
}

interface RespondingData {
  facialExpressions: FacialExpressions;
  soundToneOfVoice: SoundToneOfVoice;
  textChoiceOfWords: TextChoiceOfWords;
  overallScore: number;
  feedback: Feedback;
}

interface RespondingComponentProps {
  respondingData: RespondingData;
}

const RespondingComponent: React.FC<RespondingComponentProps> = ({
  respondingData,
}) => {
  const renderYesNo = (label: string, value: string) => (
    <li>
      <strong>{label}:</strong> {value}
    </li>
  );

  const renderStars = (rating: number) => {
    const fullStar = "★";
    const emptyStar = "☆";
    return (
      <>
        {[...Array(5)].map((_, i) => (
          <span key={i} className="text-white">
            {i < rating ? fullStar : emptyStar}
          </span>
        ))}
      </>
    );
  };

  return (
    <div className="mx-2 rounded-[44px] bg-[#22409A] p-6 text-white shadow-lg">
      {/* Title */}
      <h2 className="mb-4 text-center text-xl font-semibold">Responding</h2>

      {/* Facial Expressions */}
      <div className="mb-4 text-left">
        <h3 className="mb-2 text-lg font-semibold">Facial Expressions</h3>
        <ul className="list-none space-y-1">
          {renderYesNo(
            "Calm and Approachable Expression",
            respondingData.facialExpressions.calmAndApproachableExpression,
          )}
          {renderYesNo(
            "Engaged Listening",
            respondingData.facialExpressions.engagedListening,
          )}
          {renderYesNo(
            "No Signs of Frustration or Annoyance",
            respondingData.facialExpressions.noSignsOfFrustrationOrAnnoyance,
          )}
          {renderYesNo(
            "Supportive Gestures",
            respondingData.facialExpressions.supportiveGestures,
          )}
          {renderYesNo(
            "Open and Relaxed Facial Features",
            respondingData.facialExpressions.openAndRelaxedFacialFeatures,
          )}
        </ul>
      </div>

      {/* Sound Tone of Voice */}
      <div className="mb-4 text-left">
        <h3 className="mb-2 text-lg font-semibold">Sound Tone of Voice</h3>
        <ul className="list-none space-y-1">
          {renderYesNo(
            "Calm and Steady Tone",
            respondingData.soundToneOfVoice.calmAndSteadyTone,
          )}
          {renderYesNo(
            "Empathetic Tone",
            respondingData.soundToneOfVoice.empatheticTone,
          )}
          {renderYesNo(
            "Clear Articulation",
            respondingData.soundToneOfVoice.clearArticulation,
          )}
          {renderYesNo(
            "Non-Defensive Tone",
            respondingData.soundToneOfVoice.nonDefensiveTone,
          )}
          {renderYesNo(
            "Appropriate Volume",
            respondingData.soundToneOfVoice.appropriateVolume,
          )}
        </ul>
      </div>

      {/* Text Choice of Words */}
      <div className="mb-4 text-left">
        <h3 className="mb-2 text-lg font-semibold">Choice of Words</h3>
        <ul className="list-none space-y-1">
          {renderYesNo(
            "Acknowledgment of the Issue",
            respondingData.textChoiceOfWords.acknowledgmentOfTheIssue,
          )}
          {renderYesNo(
            "Use of Polite Language",
            respondingData.textChoiceOfWords.useOfPoliteLanguage,
          )}
          {renderYesNo(
            "Solution-Oriented Words",
            respondingData.textChoiceOfWords.solutionOrientedWords,
          )}
          {renderYesNo(
            "Apologetic and Responsible Language",
            respondingData.textChoiceOfWords.apologeticAndResponsibleLanguage,
          )}
          {renderYesNo(
            "Avoidance of Blaming Language",
            respondingData.textChoiceOfWords.avoidanceOfBlamingLanguage,
          )}
        </ul>
      </div>

      {/* Overall Score */}
      <div className="mb-4 text-left text-lg">
        <strong>Overall Score:</strong>{" "}
        {renderStars(respondingData.overallScore)}
      </div>

      {/* Feedback */}
      <div className="text-left">
        {/* <h3 className="text-lg font-semibold mb-2">Feedback</h3> */}
        <p>
          <strong>You did well at:</strong>{" "}
          {respondingData.feedback.positiveAreas
            .toLowerCase()
            .replace("the trainee", "You")}
        </p>
        <p>
          <strong>You can improve by focusing on:</strong>{" "}
          {respondingData.feedback.improvementSuggestions
            .toLowerCase()
            .replace("the trainee", "You")}
        </p>
      </div>
    </div>
  );
};

export default RespondingComponent;
