import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { CourseUpdateResponse } from "../../types/Course";
import { AxiosInstance } from "axios";

// api call

const updateCourse = async (
  axiosObject: AxiosInstance,
  courseId: string,
  name: string,
  activities: number[],
): Promise<CourseUpdateResponse> => {
  const data = {
    course_id: courseId,
    name: name,
    activities: activities,
  }
  const course = await axiosObject.patch<CourseUpdateResponse>(
    "/api/v1/course/update/" + courseId, data
  );
  console.log(course);
  return course.data;
};

export function useUpdateCourse() {
  const axiosInstance = useAxios();
  // run the query
  return useMutation({
    mutationFn: ({ courseId, name = "", activities = [] }: {
      courseId: string, name: string, activities: number[]
    }) => {
      return updateCourse(axiosInstance, courseId, name, activities);
    },
  })
}
