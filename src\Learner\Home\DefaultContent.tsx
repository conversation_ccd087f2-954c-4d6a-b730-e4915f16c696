import { useGetCourseProgress } from "../hooks/useGetCourseProgress";
import { useGetUser } from "../hooks/useGetUser";
import { useGetAllCourses } from "../../Instructor/hooks/useGetAllCourses";
// import { useQueryClient } from "@tanstack/react-query";
import Spinner from "../components/Spinner";
import "../components/ResponsiveStyles.css";
import { useNavigate } from "react-router-dom";
import { Course } from "../../types/CourseProgress";

// Helper function to get a default icon for courses
const getCourseIcon = (courseName: string) => {
  const name = courseName.toLowerCase();
  if (name.includes("negotiation")) return "👥";
  if (name.includes("conversation") || name.includes("communication"))
    return "💬";
  if (name.includes("customer") || name.includes("service")) return "🎧";
  if (name.includes("sales")) return "💼";
  if (name.includes("leadership")) return "👑";
  if (name.includes("training")) return "📚";
  return "📖"; // Default icon
};

// Component for explore course cards
const ExploreCourseCard = ({
  title,
  icon,
}: {
  title: string;
  icon: string;
}) => {
  return (
    <div className="flex items-center space-x-4 rounded-lg bg-white p-4 shadow-sm">
      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100 text-2xl">
        {icon}
      </div>
      <h3 className="text-lg font-medium text-gray-800">{title}</h3>
    </div>
  );
};

// Component for continue learning cards with progress
const ContinueLearningProgressCard = ({
  title,
  icon,
  progress,
  course_id,
  course,
}: {
  title: string;
  icon: string;
  progress: number;
  course_id: number;
  course: Course;
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (course.activities && course.activities.length > 0) {
      navigate(
        `/my-learning/${course.activities[0].type}?course_id=${course_id}&activity_id=${course.activities[0].id}`,
      );
    }
  };

  return (
    <button
      onClick={handleClick}
      className="flex w-full cursor-pointer items-center space-x-4 rounded-lg bg-white p-4 text-left shadow-sm transition-shadow hover:shadow-md"
    >
      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100 text-2xl">
        {icon}
      </div>
      <div className="flex-1">
        <h3 className="text-lg font-medium text-gray-800">{title}</h3>
        <div className="mt-2 flex items-center space-x-2">
          <div className="h-2 flex-1 rounded-full bg-gray-200">
            <div
              className="h-2 rounded-full bg-blue-500"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <span className="text-sm font-medium text-gray-600">{progress}%</span>
        </div>
      </div>
    </button>
  );
};

export default function DefaultContent() {
  // const queryClient = useQueryClient();
  const {
    data: courses_progress,
    isError,
    isFetching,
  } = useGetCourseProgress();

  const { data: user } = useGetUser();
  const { data: allCourses, isFetching: isFetchingCourses } =
    useGetAllCourses();

  // Filter out courses that the user is already enrolled in
  const availableCourses =
    allCourses?.filter(
      (course) => !courses_progress?.some((cp) => cp.course_id === course.id),
    ) || [];

  // useEffect(() => {
  //   queryClient.invalidateQueries({ queryKey: ["courses_progress"] });
  // }, [courses_progress]);

  return (
    <>
      <Spinner loading={(isFetching || isFetchingCourses) && !isError} />
      <div className="flex flex-col space-y-6">
        {/* Welcome Message */}
        <h1 className="text-2xl font-bold text-white md:text-3xl">
          Welcome Back {user?.name || "User"}!
        </h1>

        {/* Explore Courses Section */}
        <div className="rounded-lg bg-blue-600 p-6">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white md:text-2xl">
              Explore Courses
            </h2>
            <button className="text-yellow-300 hover:text-yellow-200">
              See All
            </button>
          </div>
          <div className="space-y-3">
            {availableCourses.length > 0 ? (
              availableCourses
                .slice(0, 3)
                .map((course) => (
                  <ExploreCourseCard
                    key={course.id}
                    title={course.name || "Untitled Course"}
                    icon={getCourseIcon(course.name || "")}
                  />
                ))
            ) : (
              <p className="text-white">No new courses available to explore.</p>
            )}
          </div>
        </div>

        {/* Continue Learning Section */}
        <div className="rounded-lg bg-blue-600 p-6">
          <h2 className="mb-4 text-xl font-semibold text-white md:text-2xl">
            Continue Learning
          </h2>
          {courses_progress?.length === 0 ? (
            <p className="text-white">No enrolled courses were found.</p>
          ) : (
            <div className="space-y-3">
              {courses_progress?.map((courseProgress) => (
                <ContinueLearningProgressCard
                  key={courseProgress.course_id}
                  title={courseProgress.course.name}
                  icon={getCourseIcon(courseProgress.course.name)}
                  progress={Math.round(courseProgress.progress_percentage)}
                  course_id={courseProgress.course_id}
                  course={courseProgress.course}
                />
              ))}
            </div>
          )}
        </div>

        {/* Course Selection (only show if multiple courses) */}
      </div>
    </>
  );
}
