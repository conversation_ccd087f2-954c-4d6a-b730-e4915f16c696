import React from "react";
import CourseCard from "./CourseCard";

const courses = [
  {
    title: "Course 1",
    description:
      "In the rapidly evolving field of artificial intelligence, it's crucial to prioritize ethical considerations and respect for human Dignity.",
    buttonText: "Enroll Now",
  },
  {
    title: "Course 2",
    description:
      "In the rapidly evolving field of artificial intelligence, it's crucial to prioritize ethical considerations and respect for human Dignity.",
    buttonText: "Enroll Now",
  },
  {
    title: "Course 3",
    description:
      "In the rapidly evolving field of artificial intelligence, it's crucial to prioritize ethical considerations and respect for human Dignity.",
    buttonText: "Enroll Now",
  },
  {
    title: "Course 1",
    description:
      "In the rapidly evolving field of artificial intelligence, it's crucial to prioritize ethical considerations and respect for human Dignity.",
    buttonText: "Enroll Now",
  },
  {
    title: "Course 2",
    description:
      "In the rapidly evolving field of artificial intelligence, it's crucial to prioritize ethical considerations and respect for human Dignity.",
    buttonText: "Enroll Now",
  },
  {
    title: "Course 3",
    description:
      "In the rapidly evolving field of artificial intelligence, it's crucial to prioritize ethical considerations and respect for human Dignity.",
    buttonText: "Enroll Now",
  },
  {
    title: "Course 1",
    description:
      "In the rapidly evolving field of artificial intelligence, it's crucial to prioritize ethical considerations and respect for human Dignity.",
    buttonText: "Enroll Now",
  },
  {
    title: "Course 2",
    description:
      "In the rapidly evolving field of artificial intelligence, it's crucial to prioritize ethical considerations and respect for human Dignity.",
    buttonText: "Enroll Now",
  },
  {
    title: "Course 3",
    description:
      "In the rapidly evolving field of artificial intelligence, it's crucial to prioritize ethical considerations and respect for human Dignity.",
    buttonText: "Enroll Now",
  },
];

const CourseGrid: React.FC = () => {
  return (
    <div className="grid grid-cols-1 gap-6 p-6 md:grid-cols-2 lg:grid-cols-3">
      {courses.map((course, index) => (
        <CourseCard
          key={index}
          title={course.title}
          description={course.description}
          buttonText={course.buttonText}
        />
      ))}
    </div>
  );
};

export default CourseGrid;
