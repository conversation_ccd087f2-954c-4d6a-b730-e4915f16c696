import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import styled from "styled-components";

const getColor = (props: any) => {
  if (props.isDragAccept) {
    return "#00e676";
  }
  if (props.isDragReject) {
    return "#ff1744";
  }
  if (props.isFocused) {
    return "#2196f3";
  }
  return "#000000";
};

const Container = styled.div<{
  isFocused: boolean;
  isDragAccept: boolean;
  isDragReject: boolean;
}>`
  border: 2px dashed ${(props) => getColor(props)};
  border-radius: 5px;
  padding: 20px;
  text-align: center;
  transition: border 0.24s ease-in-out;
  outline: none;
`;

interface VideoDropzoneProps {
  onDrop: (acceptedFiles: File[]) => void;
}

const VideoDropzone: React.FC<VideoDropzoneProps> = ({ onDrop }) => {
  const [fileName, setFileName] = useState<string | null>(null);

  const onDropCallback = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        setFileName(acceptedFiles[0].name);
      }
      onDrop(acceptedFiles);
    },
    [onDrop],
  );

  const { getRootProps, getInputProps, isFocused, isDragAccept, isDragReject } =
    useDropzone({
      accept: {
        "video/*": [".mp4", ".mov"],
      },
      onDrop: onDropCallback,
    });

  return (
    <Container {...getRootProps({ isFocused, isDragAccept, isDragReject })}>
      <input {...getInputProps()} />
      <p>Drag 'n' drop some video files here, or click to select files</p>
      {fileName && <p>File selected: {fileName}</p>}
    </Container>
  );
};

export default VideoDropzone;
