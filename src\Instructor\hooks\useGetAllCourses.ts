import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { Course } from "../../types/Course";
import { AxiosInstance } from "axios";

// api call

const getCourses = async (
  axiosObject: AxiosInstance,
): Promise<Course[]> => {
  const courses = await axiosObject.get<Course[]>(
    "/api/v1/course/get/all",
  );
  console.log(courses);
  return courses.data;
};

export function useGetAllCourses() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<Course[]>({
    queryKey: ["courses"],
    refetchOnWindowFocus: false,
    queryFn: () => getCourses(axiosObject),
  });
}
