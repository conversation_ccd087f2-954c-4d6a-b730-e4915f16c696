import { ReactNode, useState } from "react";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import { useAuth } from "../../Learner/context/AuthProvider";
import DashboardIcon from "../Icons/DashboardIcon";
import SystemManagementIcon from "../Icons/SystemManagementIcon";
import UserManagementIcon from "../Icons/UserManagementIcon";
import LogoutIcon from "../Icons/LogoutIcon";
import AnalyticsIcon from "../Icons/AnalyticsIcon";

interface sideBarItem {
  name: string;
  icon: ReactNode;
  children?: string[];
  selected?: boolean;
}

export const sideBarItems: sideBarItem[] = [
  {
    name: "Dashboard",
    icon: <DashboardIcon />,
  },
  {
    name: "System-Management",
    icon: <SystemManagementIcon />,
  },
  {
    name: "User-Management",
    icon: <UserManagementIcon />,
    children: ["Admin-Management", "Instructor-Management", "Learner-Management"],
  },
  {
    name: "Analy<PERSON>",
    icon: <AnalyticsIcon />,
  },
  {
    name: "Logout",
    icon: <LogoutIcon />,
  },
];

function SideBarChild({
  child,
  index,
  selectedIndex,
  onClickChild,
}: {
  child: string;
  index: number;
  selectedIndex: number;
  onClickChild: (childName: string) => void;
}) {
  return (
    <div
      className={`my-2 flex w-[80%] cursor-pointer items-center justify-start rounded-lg p-2 ${
        index === selectedIndex ? "bg-[#145DA0]" : ""
      }`}
      onClick={() => onClickChild(child)}
    >
      <h1
        className={`text-base font-normal ${
          index === selectedIndex ? "text-white" : "text-[#A3A3A3]"
        }`}
      >
        {child.replace(/-/g, " ")}
      </h1>
    </div>
  );
}

function SideBarItem({
  item,
  index,
  selectedIndex,
  collapseChild,
  onClick,
}: {
  item: sideBarItem;
  index: number;
  selectedIndex: number;
  collapseChild: boolean;
  onClick: () => void;
}) {
  const [selectedChild, setSelectedChild] = useState<number>(-1);
  const navigate = useNavigate();

  const handleChildClick = (childName: string) => {
    // Find the index of the child that was clicked
    const childIndex =
      item.children?.findIndex((child) => child === childName) ?? -1;
    setSelectedChild(childIndex);
    navigate("/superadmin/" + childName.toLowerCase());
  };

  return (
    <div className="w-full">
      <div
        className={`my-4 flex max-h-fit min-h-10 w-full cursor-pointer flex-col justify-center rounded-lg p-5 ${index === selectedIndex ? "bg-[#145DA0]" : ""}`}
        onClick={onClick}
      >
        <div className="flex flex-row items-center gap-4">
          {item.name === "Dashboard" ? (
            <DashboardIcon selected={index === selectedIndex} />
          ) : item.name === "System-Management" ? (
            <SystemManagementIcon selected={index === selectedIndex} />
          ) : item.name === "User-Management" ? (
            <UserManagementIcon selected={index === selectedIndex} />
          ) : item.name === "Analytics" ? (
            <AnalyticsIcon selected={index === selectedIndex} />
          ) : item.name === "Logout" ? (
            <LogoutIcon selected={index === selectedIndex} />
          ) : (
            item.icon
          )}
          <h1
            className={`text-lg font-normal ${index === selectedIndex ? "text-white" : "text-[#A3A3A3]"}`}
          >
            {item.name.replace(/-/g, " ")}
          </h1>
          {collapseChild && item.children && index === selectedIndex ? (
            <svg
              width="11"
              height="9"
              viewBox="0 0 11 9"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M5.5 9L10.2631 0.75H0.73686L5.5 9Z" fill="white" />
            </svg>
          ) : (
            <></>
          )}
        </div>
      </div>
      {collapseChild && index === selectedIndex ? (
        <div className="flex w-full flex-col items-center justify-start">
          {item.children?.map((child, index) => (
            <SideBarChild
              key={index}
              child={child}
              index={index}
              selectedIndex={selectedChild}
              onClickChild={handleChildClick}
            />
          ))}
        </div>
      ) : (
        <></>
      )}
    </div>
  );
}

function handleLogout(logout: () => void) {
  Swal.fire({
    title: "Are you sure?",
    text: "You will be logged out of the system.",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Yes, log me out!",
  }).then((result) => {
    if (result.isConfirmed) {
      logout();
    }
  });
}

function SideBar() {
  const [selectedItem, setSelectedItem] = useState<number>(0);
  const [collapse, setCollapse] = useState<boolean>(true);
  const navigate = useNavigate();
  const { logout } = useAuth();

  return (
    <ul className="w-[20%] flex-shrink-0 divide-y-2 divide-[#E6DDB3] px-4">
      {sideBarItems.map((sideBarItem, index) => (
        <SideBarItem
          key={sideBarItem.name}
          item={sideBarItem}
          index={index}
          selectedIndex={selectedItem}
          onClick={() => {
            if (sideBarItem.children && selectedItem === index) {
              setCollapse(!collapse);
            } else {
              setCollapse(true);
              setSelectedItem(index);
            }
            if (sideBarItem.name !== "Logout") {
              if (sideBarItem.children) {
                // If it has children, just toggle collapse
              } else {
                navigate(
                  sideBarItem.name.includes("Portal")
                    ? "/superadmin"
                    : "/superadmin/" + sideBarItem.name.toLowerCase(),
                );
              }
            } else {
              handleLogout(logout);
            }
          }}
          collapseChild={collapse}
        />
      ))}
    </ul>
  );
}

export default SideBar;
