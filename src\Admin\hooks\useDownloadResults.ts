import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { AxiosInstance } from "axios";

// Simulated API call with delay
const downloadResults = async (
  axiosObject: AxiosInstance,
  courseId?: string
): Promise<Blob> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // In the future, this would be a real API call:
  // const response = await axiosObject.get<Blob>(
  //   "/api/v1/results/download", {
  //     params: { course_id: courseId },
  //     responseType: 'blob'
  //   }
  // );
  // return response.data;
  
  // For now, create a mock CSV file
  const csvContent = 
    "ID,Name,Course,Activities Completed,Result\n" +
    "17566,User 1,Course,3/6,66%\n" +
    "17566,User 2,Sales,3/6,45%\n" +
    "17566,User 3,HR,3/6,96%\n" +
    "17566,User 4,HR,3/6,96%\n" +
    "17566,User 1,Marketing,3/6,100%\n" +
    "17566,User 2,Sales,3/6,75%\n";
  
  return new Blob([csvContent], { type: 'text/csv' });
};

export function useDownloadResults() {
  const axiosObject = useAxios();
  
  return useMutation({
    mutationFn: (courseId?: string) => downloadResults(axiosObject, courseId),
  });
}
