import React, { createContext, useState, useContext, ReactNode, useEffect } from "react";
import { CourseProgress } from "../../types/CourseProgress";
import { useGetCourseProgress } from "../hooks/useGetCourseProgress";

interface CourseContextType {
  selectedCourseId: number | null;
  setSelectedCourseId: (courseId: number | null) => void;
  courseProgressList: CourseProgress[] | undefined;
  selectedCourseProgress: CourseProgress | undefined;
}

const CourseContext = createContext<CourseContextType | undefined>(undefined);

interface CourseProviderProps {
  children: ReactNode;
}

export const CourseProvider: React.FC<CourseProviderProps> = ({ children }) => {
  const [selectedCourseId, setSelectedCourseId] = useState<number | null>(null);
  const { data: courseProgressList } = useGetCourseProgress();
  
  // Find the selected course progress
  const selectedCourseProgress = courseProgressList?.find(
    (cp) => cp.course_id === selectedCourseId
  );

  // Initialize with the first course when data is loaded
  useEffect(() => {
    if (courseProgressList && courseProgressList.length > 0 && !selectedCourseId) {
      setSelectedCourseId(courseProgressList[0].course_id);
    }
  }, [courseProgressList, selectedCourseId]);

  return (
    <CourseContext.Provider 
      value={{ 
        selectedCourseId, 
        setSelectedCourseId, 
        courseProgressList,
        selectedCourseProgress
      }}
    >
      {children}
    </CourseContext.Provider>
  );
};

export const useCourse = (): CourseContextType => {
  const context = useContext(CourseContext);
  if (context === undefined) {
    throw new Error("useCourse must be used within a CourseProvider");
  }
  return context;
};
