import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { AxiosInstance } from "axios";

// Simulated API call with delay
const downloadUserResults = async (
  axiosObject: AxiosInstance,
  userId: string,
  courseId: string
): Promise<Blob> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // In the future, this would be a real API call:
  // const response = await axiosObject.get<Blob>(
  //   "/api/v1/results/download-user", {
  //     params: { user_id: userId, course_id: courseId },
  //     responseType: 'blob'
  //   }
  // );
  // return response.data;
  
  // For now, create a mock CSV file
  const csvContent = 
    "User ID,User Name,Activity ID,Activity Name,Activity Type,Result\n" +
    `${userId},User ${courseId},1,Activity 1,Course,66%\n` +
    `${userId},User ${courseId},2,Activity 2,Sales,45%\n` +
    `${userId},User ${courseId},3,Activity 3,HR,96%\n` +
    `${userId},User ${courseId},4,Activity 4,HR,96%\n` +
    `${userId},User ${courseId},5,Activity 5,Marketing,100%\n` +
    `${userId},User ${courseId},6,Activity 6,Sales,75%\n`;
  
  return new Blob([csvContent], { type: 'text/csv' });
};

export function useDownloadUserResults() {
  const axiosObject = useAxios();
  
  return useMutation({
    mutationFn: ({ userId, courseId }: { userId: string, courseId: string }) => 
      downloadUserResults(axiosObject, userId, courseId),
  });
}
