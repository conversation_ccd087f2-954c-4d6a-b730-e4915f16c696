import React, { ReactNode } from "react";
import TSVIcon from "../Icons/TSVIcon";
import ToggleButton from "./ToggleButton";
import MyActivityIcon from "../Icons/MyActivityIcon";

const TwoStepVerification: React.FC = () => (
  <div className="flex flex-col items-start gap-10">
    <div className="flex gap-12">
      <h1 className="text-2xl font-medium text-[#6F6F6F]">
        Two-step verification
      </h1>
      <TSVIcon />
    </div>
    <ToggleButton />
  </div>
);

const MyActivitySection: React.FC = () => (
  <div className="flex cursor-pointer flex-col gap-7">
    <div className="flex items-center gap-8 text-2xl font-medium text-[#6F6F6F]">
      <h1>My Activity</h1>
      <MyActivityIcon />
    </div>
    <div className="flex gap-16">
      <h1 className="text-base font-medium text-black">Signed in via</h1>
      <h1 className="text-base font-normal text-[#7199D6]">Google</h1>
    </div>
    <div className="flex gap-16">
      <h1 className="text-base font-medium text-black">18 seconds ago</h1>
      <h1 className="text-base font-normal text-[#7199D6] underline">
        View all activities
      </h1>
    </div>
  </div>
);

function MidSection() {
  return (
    <div className="flex cursor-pointer justify-around">
      <TwoStepVerification />
      <div className="w-[1px] bg-[#D3D3D3]"></div>
      <MyActivitySection />
    </div>
  );
}

export default MidSection;
