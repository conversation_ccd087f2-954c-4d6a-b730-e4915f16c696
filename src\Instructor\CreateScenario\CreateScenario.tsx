import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import AvatarGallery from "./AvatarGallery";
import ScenarioForm from "./ScenarioForm";
import ScenarioSelection from "./ScenarioSelection";
import ScenarioPreview from "./ScenarioPreview";
import ProcessScenario from "./ProcessScenario";
import { useGetStreamingToken } from "./hooks/useGetStreamingToken";
import showError from "../../Learner/scripts/showErrorDialog";
import { useQueryClient } from "@tanstack/react-query";

type FormData = {
  tutorialNature: string;
  targetAudience: string;
  learningOutcome: string;
  instructions?: string;
};

type FormDataTutorial = {
  targetAudience: string;
  learningOutcome: string;
  instructions?: string;
};
type Step = "avatar" | "selection" | "preview" | "process";

const CreateScenario = () => {
  const [step, setStep] = useState<Step>("avatar");
  const [avatarId, setAvatarId] = useState<string>("");
  const [formData, setFormData] = useState<FormData | FormDataTutorial | null>(
    null,
  );
  const [selectedScenario, setSelectedScenario] = useState<string>("");
  const [savedVideoId, setSavedVideoId] = useState<number | null>(null);
  const queryClient = useQueryClient();

  // For navigation
  const location = useLocation();
  const navigate = useNavigate();
  const query = new URLSearchParams(location.search);
  const returnTo = query.get("returnTo");
  const activityId = query.get("activityId");
  const activityType = query.get("activityType");

  // Get streaming token
  const { data: streamingTokenData, isLoading: isTokenLoading } =
    useGetStreamingToken();

  const handleAvatarSelect = (avatarId: string) => {
    setAvatarId(avatarId);
  };

  const handleFormSubmit = (data: FormData) => {
    setFormData(data);
    if (avatarId === "") {
      showError("Error", "Please select an avatar.");
      return;
    }
    setStep("selection");
  };

  const handleScenarioSelect = (scenario: string) => {
    setSelectedScenario(scenario);
    setStep("preview");
  };

  const handleRestart = () => {
    queryClient.invalidateQueries({ queryKey: ["streaming-token"] });
    setStep("avatar");
    setAvatarId("");
    setFormData(null);
    setSelectedScenario("");
  };

  if (isTokenLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const renderStep = () => {
    switch (step) {
      case "avatar":
        return (
          <div>
            <AvatarGallery
              selectedAvatarId={avatarId}
              onSelect={handleAvatarSelect}
            />
            <ScenarioForm
              onNext={handleFormSubmit}
              activityType={activityType || ""}
            />
          </div>
        );

      case "selection":
        return formData ? (
          <ScenarioSelection
            formData={{ avatarId, ...formData }}
            activityType={activityType || ""}
            onBack={() => setStep("avatar")}
            onNext={handleScenarioSelect}
          />
        ) : null;

      case "preview":
        return (
          <ScenarioPreview
            avatarId={avatarId}
            selectedScenario={selectedScenario}
            activityType={activityType || ""}
            onBack={() => setStep("selection")}
            onProcess={() => setStep("process")}
            setSelectedScenario={setSelectedScenario}
          />
        );

      case "process":
        return (
          <ProcessScenario
            avatarId={avatarId}
            selectedScenario={selectedScenario}
            onRestart={handleRestart}
            streamingToken={streamingTokenData?.token}
          />
        );

      default:
        return null;
    }
  };

  return <div className="w-full">{renderStep()}</div>;
};

export default CreateScenario;
