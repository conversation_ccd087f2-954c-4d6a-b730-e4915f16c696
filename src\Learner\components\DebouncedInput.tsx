import React from "react";

interface DebouncedInputProps {
  value: string | number;
  onChange: (value: string | number) => void;
  debounce?: number;
  onPressEnter: () => void;
}

/**
 *
 * @param param0
 * Debounced Input, should handle debouncing in order to avoid
 * too many state changes
 */

const DebouncedInput = ({
  value: initialValue,
  onChange,
  debounce = 500,
  onPressEnter,
  ...props
}: DebouncedInputProps &
  Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange">) => {
  const [value, setValue] = React.useState(initialValue);

  React.useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  React.useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value);
    }, debounce);

    return () => clearTimeout(timeout);
  }, [debounce, onChange, value]);
  return (
    <>
      <div className="relative w-full">
        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"></div>
        <input
          type="text"
          className="block w-full rounded-lg border border-gray-300 bg-white p-1.5 pl-2 text-sm text-gray-900 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
          id="message-box"
          {...props}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              onPressEnter();
            }
          }}
        />
      </div>
    </>
  );
};

export default DebouncedInput;
