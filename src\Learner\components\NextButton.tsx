import React from "react";
import GradientButton from "./GradientButton";

interface NextButtonProps {
  display?: boolean;
  onClick?: () => void; // Optional onClick handler
}

const NextButton: React.FC<NextButtonProps> = ({ display = true, onClick }) => {
  return (
    <>
      {display ? (
        <div className="w-full text-end">
          <GradientButton
            text="Continue"
            color1="#EEC300"
            color2="#EEC300"
            type="button"
            width="200px"
            textColor="black"
            onClick={onClick}
          />
        </div>
      ) : null}
    </>
  );
};

export default NextButton;
