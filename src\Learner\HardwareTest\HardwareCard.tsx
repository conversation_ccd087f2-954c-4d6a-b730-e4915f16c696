import { useState, useEffect, useRef } from "react";
import { useReactMediaRecorder } from "react-media-recorder";
import RedButton from "../components/RedButton";
import HardwareIcon from "./HardwareIcon";
import { useLocation } from "react-router-dom";
import { usePostActivityProgress } from "../hooks/usePostActivityProgress";
// import GradientButton from "../components/GradientButton";
import { useNavigate } from "react-router-dom";
import NextButton from "../components/NextButton";

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

const VideoPreview = ({ stream }: { stream: MediaStream | null }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;
    }
  }, [stream]);
  if (!stream) {
    return null;
  }
  return (
    <video ref={videoRef} className="h-auto w-full max-w-[640px]" autoPlay />
  );
};

const VideoPreviewControl = ({ stream }: { stream: MediaStream | null }) => {
  const videoRefC = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRefC.current && stream) {
      videoRefC.current.srcObject = stream;
    }
  }, [stream]);
  if (!stream) {
    return null;
  }
  return (
    <video
      ref={videoRefC}
      className="h-auto w-full max-w-[640px]"
      autoPlay
      controls
    />
  );
};

export default function HardwareCard() {
  const [recording, setRecording] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isWorking, setIsWorking] = useState(false);
  const [cameraStatus, setcameraStatus] = useState(false);

  const query = useQueryParam();
  const course_id = query.get("course_id") ?? "";
  const activity_id = query.get("activity_id") ?? "";
  const mutation = usePostActivityProgress();
  const navigate = useNavigate();

  const { startRecording, stopRecording, status, error, previewStream } =
    useReactMediaRecorder({
      video: true,
      audio: true,
      onStop: async (blobUrl, blob) => {
        console.log("Recording stopped. Checking blob...");
        const hasVideo = await checkIfBlobHasVideo(blob);
        const hasAudio = await checkIfBlobHasAudio(blob);
        console.log("Video:", hasVideo, "Audio:", hasAudio);
        if (hasVideo && hasAudio) {
          setIsWorking(true);
          setErrorMessage(null);
        } else {
          setIsWorking(false);
          setErrorMessage("Error: Video or audio is not working.");
        }
      },
    });

  const submitAndRedirectToLink = async () => {
    mutation.mutate(
      { courseId: course_id, activityId: activity_id, userInput: "" },
      {
        onSuccess: (data) => {
          if (data && data.next_activity_link) {
            navigate("/my-learning" + data.next_activity_link);
          }
        },
        onError: () => {
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  const turnOnCamera = () => {
    setcameraStatus(true);
    setIsWorking(false);
    setErrorMessage(null);

    startRecording();
  };

  const handleRecord = () => {
    setcameraStatus(false);
    stopRecording();
    setRecording(true);
    setIsWorking(false);
    setErrorMessage(null);

    startRecording();

    setTimeout(() => {
      stopRecording();
      setRecording(false);
    }, 5000); // Stop recording after 10 seconds
  };

  const checkIfBlobHasVideo = (blob: Blob): Promise<boolean> => {
    return new Promise((resolve) => {
      const video = document.createElement("video");
      video.src = URL.createObjectURL(blob);
      video.onloadedmetadata = () => {
        console.log(
          "Video metadata loaded:",
          video.videoWidth,
          video.videoHeight,
        );
        resolve(video.videoWidth > 0 && video.videoHeight > 0);
      };
      video.onerror = () => {
        console.log("Video metadata error");
        resolve(false);
      };
    });
  };

  const checkIfBlobHasAudio = (blob: Blob): Promise<boolean> => {
    return new Promise((resolve) => {
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      const reader = new FileReader();
      reader.readAsArrayBuffer(blob);
      reader.onloadend = () => {
        audioContext.decodeAudioData(
          reader.result as ArrayBuffer,
          (buffer) => {
            console.log("Audio buffer loaded:", buffer.numberOfChannels);
            resolve(buffer.numberOfChannels > 0);
          },
          (error) => {
            console.log("Audio decoding error:", error);
            resolve(false);
          },
        );
      };
      reader.onerror = () => {
        console.log("File reader error");
        resolve(false);
      };
    });
  };

  useEffect(() => {
    return () => {
      if (status === "recording") {
        stopRecording();
      }
    };
  }, [status, stopRecording]);

  function buttonText() {
    if (!cameraStatus) {
      return "View Camera Preview";
    } else if (recording) {
      if (status === "recording") {
        return "Testing";
      } else {
        return "Test";
      }
    } else {
      return "Test";
    }
  }

  return (
    <div
      key={activity_id}
      className="flex min-w-full flex-col items-center justify-center gap-8 rounded-lg bg-[#CCCCCC] bg-opacity-20 px-4 pb-8 pt-6 md:gap-24 md:px-6 md:pb-12 md:pt-10"
    >
      <p className="mb-4 text-sm md:text-base">
        This application requires the use of our device's camera and microphone.
        This short hardware test is simply to check if they are functioning
        properly and will only take a few seconds. After you have clicked on
        "Camera Preview" button, followed by "Test", please read out the words
        in this short line below. We may not need to complete the sentence as
        any sounds and visuals picked up will work, even if in the background.
        Thank you.
      </p>
      <div className="flex w-full flex-col items-center justify-center gap-6 md:flex-row md:gap-16">
        {cameraStatus ? (
          <VideoPreview stream={previewStream} />
        ) : recording ? (
          <VideoPreviewControl stream={previewStream} />
        ) : (
          <div className="flex w-full flex-row justify-center gap-4 md:gap-16">
            <HardwareIcon iconType="Camera" />
            <HardwareIcon iconType="Microphone" />
          </div>
        )}
      </div>
      {/* <p className="text-center text-base font-medium text-[#858585]">
        This learning experience involves analyzing our responses to
        stakeholders in an interaction. As such, the system will prompt you to
        record your response during certain segments of the course. As such,
        let’s test your hardware now to ensure that it is working well.
        <br />
        <br />
        Please press the “Test” button and read the line below when the recording
        starts
      </p> */}
      <div className="w-full justify-center rounded bg-[#36537F] bg-opacity-20 px-4 py-4 md:px-52 md:py-7">
        <p className="text-center text-base font-medium text-[#454545] md:text-xl">
          Hi, I am Kit and I’m here to learn about Dignity
        </p>
      </div>

      <RedButton
        onClick={!cameraStatus ? turnOnCamera : handleRecord}
        disabled={recording}
      >
        {buttonText()}
      </RedButton>

      {isWorking && (
        <p className="mt-4 text-base font-semibold text-green-700 md:text-lg">
          Video and audio are working.
        </p>
      )}
      {(error || errorMessage) && (
        <p className="mt-4 text-sm text-red-500 md:text-base">
          {"Error while starting recording: " + error}
        </p>
      )}
      <NextButton
        onClick={submitAndRedirectToLink}
        display={isWorking == true}
      />
    </div>
  );
}
