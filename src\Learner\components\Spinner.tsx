// src/components/Spinner.tsx
import React from "react";
import ClipLoader from "react-spinners/ClipLoader";
import styled from "@emotion/styled";

interface SpinnerProps {
  loading: boolean;
}

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(245, 219, 128, 0.3);
  backdrop-filter: blur(2px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Ensure it covers everything */
`;

const Spinner: React.FC<SpinnerProps> = ({ loading }) => {
  if (!loading) return null;

  return (
    <Overlay>
      <ClipLoader color={"#123abc"} loading={loading} size={150} />
    </Overlay>
  );
};

export default Spinner;
