interface changePasswordResponse {
  success: boolean;
  message: string;
}

interface User {
  id?: number;
  name: string;
  last_name: string | null;
  email: string;
  ethnicity: "chinese" | "malay" | "indian" | "others" | null;
  IC?: string | null;
  organization_name: string | null;
  role_in_organization: "Frontline" | "Internal-facing" | "Hybrid" | null;
  organization_type:
    | "Corporate"
    | "Social Sector"
    | "Government"
    | "School"
    | null;
  date_of_birth: string | null;
  role?: "learner" | "instructor" | "admin" | null | number;
  role_id?: number; // Added for the enrolled users API
}

interface UserPaginated {
  items: User[];
  total: number;
  page: number;
  size: number;
}

interface UserSearchResponse {
  id: number;
  email: string;
}

export type { changePasswordResponse, User, UserPaginated, UserSearchResponse };
