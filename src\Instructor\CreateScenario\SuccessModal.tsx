import React from "react";
import { useNavigate, useLocation } from "react-router-dom";

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoId?: number | null;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  videoId,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const returnTo = query.get("returnTo");
  const activityId = query.get("activityId");
  const courseId = query.get("courseId");

  if (!isOpen) return null;

  const handleContinue = () => {
    // If we have return information and a video ID, navigate back to the activity development screen
    if (returnTo === "activity_development" && activityId && videoId) {
      console.log(
        "Navigating back to activity development with video ID:",
        videoId,
      );
      // Force a full navigation to ensure the component is remounted with the new URL parameters
      window.location.href = `/instructor/activity-development?activity_id=${activityId}&videoId=${videoId}&course_id=${courseId}`;
    } else {
      // Otherwise, just close the modal
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="rounded-lg bg-white p-8 shadow-xl">
        <h3 className="mb-4 text-xl font-semibold text-green-600">Success!</h3>
        <p className="mb-6 text-gray-600">
          Your video has been saved successfully.
        </p>
        <button
          onClick={handleContinue}
          className="rounded-md bg-green-600 px-4 py-2 text-white hover:bg-green-700"
        >
          Continue
        </button>
      </div>
    </div>
  );
};

export default SuccessModal;
