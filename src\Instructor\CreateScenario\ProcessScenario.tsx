import React, { useState, useRef, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import SuccessModal from "./SuccessModal";
import StreamingAvatar, {
  AvatarQuality,
  StreamingEvents,
  TaskMode,
  TaskType,
  VoiceEmotion,
} from "@heygen/streaming-avatar";
import showError from "../../Learner/scripts/showErrorDialog";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import { useSaveHeygenVideoFromStream } from "../hooks/useSaveHeygenVideoFromStream";

interface ProcessScenarioProps {
  avatarId: string;
  selectedScenario: string;
  onRestart: () => void;
  streamingToken?: string;
}

const ProcessScenario: React.FC<ProcessScenarioProps> = ({
  avatarId,
  selectedScenario,
  onRestart,
  streamingToken,
}) => {
  const [videoName, setVideoName] = useState("");
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [stream, setStream] = useState<MediaStream>();
  const [isRecording, setIsRecording] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [savedVideoId, setSavedVideoId] = useState<number | null>(null);
  const mediaStream = useRef<HTMLVideoElement>(null);
  const avatar = useRef<StreamingAvatar | null>(null);
  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const recordedChunks = useRef<Blob[]>([]);

  // For navigation
  const navigate = useNavigate();
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const returnTo = query.get("returnTo");
  const activityId = query.get("activityId");

  // Mutation for saving Heygen video
  const saveHeygenVideoMutation = useSaveHeygenVideoFromStream();

  const voiceIDMapping = {
    Wayne_20240711: "9e5b3af39e0c448c82a2743fe7639599",
    Alessandra_Chair_Sitting_public: "f4d56fd8ec174703992aec0a1e42bfce",
  };
  const wayneRateMapping = {
    rate: 0.85,
  };

  const startAvatarSession = async () => {
    if (avatar.current) return;
    if (!streamingToken) return;
    console.log("Streaming token:", streamingToken);
    console.log("Avatar ID:", avatarId);
    avatar.current = new StreamingAvatar({
      token: streamingToken,
    });

    avatar.current.on(StreamingEvents.STREAM_READY, (event) => {
      setStream(event.detail);
    });

    avatar.current.on(StreamingEvents.AVATAR_START_TALKING, () => {
      startRecording();
    });

    avatar.current.on(StreamingEvents.AVATAR_STOP_TALKING, () => {
      stopRecording();
    });

    try {
      await avatar.current.createStartAvatar({
        quality: AvatarQuality.Low,
        avatarName: avatarId,
        voice: {
          voiceId: voiceIDMapping[avatarId],
          rate: voiceIDMapping[avatarId] ? wayneRateMapping.rate : 1,
          emotion: VoiceEmotion.SERIOUS,
        },
        language: "en",
      });

      // Speak the scenario
      await avatar.current.speak({
        text: selectedScenario,
        taskType: TaskType.REPEAT,
        taskMode: TaskMode.SYNC,
      });
    } catch (error) {
      console.error("Error in avatar session:", error);
    }
  };

  const startRecording = React.useCallback(() => {
    if (!stream || !mediaStream.current) return;

    try {
      // Clear any previously recorded chunks
      recordedChunks.current = [];

      // Request data every second to ensure we capture the stream
      const recorder = new MediaRecorder(stream, { mimeType: "video/webm" });
      mediaRecorder.current = recorder;

      recorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          console.log("Data available, size:", event.data.size);
          // Store the chunks for later use
          recordedChunks.current.push(event.data);
        }
      };

      recorder.onstop = () => {
        console.log(
          "Recorder stopped, total chunks:",
          recordedChunks.current.length,
        );
      };

      // Request data every 1 second
      recorder.start(1000);
      setIsRecording(true);
      console.log("Recording started");
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  }, [stream]);

  const stopRecording = () => {
    if (mediaRecorder.current && isRecording) {
      try {
        // Request data one last time before stopping
        mediaRecorder.current.requestData();

        // Small delay to ensure data is processed
        setTimeout(() => {
          if (mediaRecorder.current) {
            console.log("Stopping recorder");
            mediaRecorder.current.stop();
          }
          setIsRecording(false);
        }, 300);
      } catch (error) {
        console.error("Error stopping recording:", error);
        setIsRecording(false);
      }
    }
  };

  // Create a video blob from the recorded chunks
  const createVideoBlob = (): Blob | null => {
    if (recordedChunks.current.length === 0) {
      console.warn("No recorded chunks available");
      return null;
    }

    try {
      return new Blob(recordedChunks.current, { type: "video/webm" });
    } catch (error) {
      console.error("Error creating video blob:", error);
      return null;
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const memoizedStartAvatarSession = React.useCallback(startAvatarSession, [
    streamingToken,
    avatarId,
    selectedScenario,
  ]);

  useEffect(() => {
    memoizedStartAvatarSession();
    return () => {};
  }, [memoizedStartAvatarSession]);

  useEffect(() => {
    if (stream && mediaStream.current) {
      mediaStream.current.srcObject = stream;
      mediaStream.current.onloadedmetadata = () => {
        mediaStream.current!.play();
        startRecording();
      };
    }
  }, [stream, startRecording]);

  const handleSaveVideo = async () => {
    if (!videoName.trim()) {
      showError("Missing Information", "Please enter a name for your video");
      return;
    }

    try {
      setIsSaving(true);

      // Check if we have recorded chunks
      const videoBlob = createVideoBlob();
      if (!videoBlob) {
        showError("Error", "No recorded video available. Please try again.");
        setIsSaving(false);
        return;
      }

      console.log("Uploading recorded video, size:", videoBlob.size);

      // Create a FormData object to send to the backend
      const formData = new FormData();
      formData.append("name", videoName);
      formData.append("video_file", videoBlob, `${videoName}.webm`);

      // Send the request to the backend using the mutation
      const savedVideo = await saveHeygenVideoMutation.mutateAsync({
        name: videoName,
        videoBlob: videoBlob,
      });

      // Save the video ID for return navigation
      setSavedVideoId(savedVideo.id);

      // Stop the avatar after uploading
      if (avatar.current) {
        avatar.current.stopAvatar();
      }

      // Stop recording if it's running
      if (mediaRecorder.current && isRecording) {
        stopRecording();
      }

      showSuccess("Success", "Your video has been saved successfully");
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error saving video:", error);
      showError(
        "Save Failed",
        "There was an error saving your video. Please try again.",
      );
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="mx-auto max-w-4xl p-6">
      <h2 className="mb-6 text-2xl font-semibold">Generated Video</h2>

      <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-black">
        {!stream ? (
          <div className="flex h-64 items-center justify-center">
            <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <video ref={mediaStream} className="w-full" autoPlay playsInline>
            <track kind="captions" />
          </video>
        )}
      </div>

      <div className="mb-8">
        <label
          htmlFor="videoName"
          className="mb-2 block text-sm font-medium text-gray-700"
        >
          Video Name
        </label>
        <input
          type="text"
          id="videoName"
          value={videoName}
          onChange={(e) => setVideoName(e.target.value)}
          className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-blue-500 focus:outline-none"
          placeholder="Enter a name for your video"
        />
      </div>

      <div className="mt-8 flex justify-between">
        <button
          onClick={onRestart}
          className="rounded-md border border-gray-300 px-6 py-2 text-gray-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Restart Process
        </button>
        <button
          onClick={handleSaveVideo}
          disabled={isSaving}
          className={`rounded-md px-6 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            isSaving
              ? "cursor-not-allowed bg-blue-400"
              : "bg-blue-600 hover:bg-blue-700"
          }`}
        >
          {isSaving ? "Saving..." : "Save Video"}
        </button>
      </div>

      <SuccessModal
        isOpen={showSuccessModal}
        videoId={savedVideoId}
        onClose={() => {
          setShowSuccessModal(false);
          onRestart();
        }}
      />
    </div>
  );
};

export default ProcessScenario;
