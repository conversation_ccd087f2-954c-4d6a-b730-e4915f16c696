import React from "react";

interface CourseCardProps {
  title: string;
  description: string;
  buttonText: string;
}

const CourseCard: React.FC<CourseCardProps> = ({
  title,
  description,
  buttonText,
}) => {
  return (
    <div className="max-w-sm rounded-lg bg-gray-100 p-6 shadow-md">
      <h2 className="mb-2 text-2xl font-bold">{title}</h2>
      <p className="mb-4 text-gray-700">{description}</p>
      <a
        href="#"
        className="mb-4 block text-blue-500 underline hover:text-blue-700"
      >
        Certificate on Completion
      </a>
      <button className="w-full rounded-lg bg-yellow-500 px-3 py-2 text-white hover:bg-yellow-600 focus:outline-none focus:ring focus:ring-yellow-300">
        {buttonText}
      </button>
    </div>
  );
};

export default CourseCard;
