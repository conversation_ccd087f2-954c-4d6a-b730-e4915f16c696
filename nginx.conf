# events {}

# http {
#     server {
#         listen 80;
#         server_name localhost;

#         root /usr/share/nginx/html;
#         index index.html;

#         location / {
#             try_files $uri $uri/ /index.html;
#         }

#         # Optional: serve static files directly
#         location /static/ {
#             expires 1y;
#             add_header Cache-Control "public, immutable";
#         }

#         error_page 500 502 503 504 /50x.html;
#         location = /50x.html {
#             root /usr/share/nginx/html;
#         }
#     }
# }
user nginx;

worker_processes    auto;

events { worker_connections 1024; }

http {
    server {
        server_tokens off;

        listen  3000;
        root    /usr/share/nginx/html;
        include /etc/nginx/mime.types;

        location / {
            try_files $uri $uri/ /index.html;
        }

        gzip            on;
        gzip_vary       on;
        gzip_http_version  1.0;
        gzip_comp_level 5;
        gzip_types
                        application/atom+xml
                        application/javascript
                        application/json
                        application/rss+xml
                        application/vnd.ms-fontobject
                        application/x-font-ttf
                        application/x-web-app-manifest+json
                        application/xhtml+xml
                        application/xml
                        font/opentype
                        image/svg+xml
                        image/x-icon
                        text/css
                        text/plain
                        text/x-component;
        gzip_proxied    no-cache no-store private expired auth;
        gzip_min_length 256;
        gunzip          on;
    }
}