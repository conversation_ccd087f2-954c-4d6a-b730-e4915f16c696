// Mock data for the educational dashboard

import useActivityRubrics from "../hooks/useActivityRubrics";

export interface CohortSummary {
  cohort: string;
  course: string;
  totalStudents: number;
  averageScore: number;
  completionRate: number;
  activities: {
    [key: string]: {
      avg: number;
      completed: number;
    };
  };
}

export interface ActivitySummary {
  activityType: string;
  activityNumber: number;
  course: string;
  totalAttempts: number;
  averageScore: number;
  averageTimeSpent: number;
  completionRate: number;
}

export interface ParameterScore {
  parameterId: string;
  parameterName: string;
  score: number;
  maxScore: number;
}

export interface ActivityResult {
  studentId: string;
  studentName: string;
  cohort: string;
  course: string;
  activityType: string;
  activityNumber: number;
  score: number;
  maxScore: number;
  timeSpent: number;
  completedAt: string;
  organization?: string;
  parameters?: ParameterScore[];
}

export interface CourseSummary {
  course: string;
  totalActivities: number;
  activityTypes: string[];
  totalStudents: number;
  totalCohorts: number;
}

export interface OrganizationSummary {
  organization: string;
  totalStudents: number;
  totalCohorts: number;
  averageScore: number;
  completionRate: number;
  topPerformingCohort: string;
}

export interface ActivityParameter {
  parameterId: string;
  parameterName: string;
  maxScore: number;
  weight: number;
  description?: string;
  performanceLevel?: "high" | "medium" | "low";
}

export interface ActivityRubric {
  activityType: string;
  activityNumber: number;
  course: string;
  parameters: ActivityParameter[];
}

export interface ParameterPerformanceData {
  parameterId: string;
  parameterName: string;
  averageScore: number;
  minScore: number;
  maxScore: number;
  standardDeviation: number;
  studentCount: number;
  performanceLevel: "excellent" | "good" | "satisfactory" | "needs-improvement";
  cohortBreakdown: {
    cohort: string;
    averageScore: number;
    studentCount: number;
  }[];
}

// Enhanced rubric parameters with the new standardized criteria
export const mockActivityRubrics: ActivityRubric[] = [
  // Course 1 - AI Activity
  {
    activityType: "AI Activity",
    activityNumber: 1,
    course: "Course 1",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 25,
        weight: 0.25,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "high",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "medium",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 25,
        weight: 0.25,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "high",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 25,
        weight: 0.25,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "medium",
      },
    ],
  },
  // Course 1 - Quiz
  {
    activityType: "Quiz",
    activityNumber: 1,
    course: "Course 1",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 30,
        weight: 0.3,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "high",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "medium",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 25,
        weight: 0.25,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "high",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 20,
        weight: 0.2,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "medium",
      },
    ],
  },
  // Course 1 - Vira Activity
  {
    activityType: "Vira Activity",
    activityNumber: 1,
    course: "Course 1",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 30,
        weight: 0.3,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "high",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "high",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 25,
        weight: 0.25,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "medium",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 20,
        weight: 0.2,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "medium",
      },
    ],
  },
  // Course 2 - AI Activity
  {
    activityType: "AI Activity",
    activityNumber: 1,
    course: "Course 2",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 30,
        weight: 0.3,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "medium",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "low",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 25,
        weight: 0.25,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "medium",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 20,
        weight: 0.2,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "low",
      },
    ],
  },
  // Course 2 - Quiz
  {
    activityType: "Quiz",
    activityNumber: 1,
    course: "Course 2",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 25,
        weight: 0.25,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "medium",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "low",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 30,
        weight: 0.3,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "medium",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 20,
        weight: 0.2,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "low",
      },
    ],
  },
  // Course 3 - AI Activity 1
  {
    activityType: "AI Activity",
    activityNumber: 1,
    course: "Course 3",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 25,
        weight: 0.25,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "medium",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "low",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 25,
        weight: 0.25,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "medium",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 25,
        weight: 0.25,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "medium",
      },
    ],
  },
  // Course 3 - AI Activity 2
  {
    activityType: "AI Activity",
    activityNumber: 2,
    course: "Course 3",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 25,
        weight: 0.25,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "low",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "low",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 30,
        weight: 0.3,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "medium",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 20,
        weight: 0.2,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "medium",
      },
    ],
  },
  // Course 3 - Quiz 1
  {
    activityType: "Quiz",
    activityNumber: 1,
    course: "Course 3",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 25,
        weight: 0.25,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "high",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "medium",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 30,
        weight: 0.3,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "medium",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 20,
        weight: 0.2,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "low",
      },
    ],
  },
  // Course 3 - Quiz 2
  {
    activityType: "Quiz",
    activityNumber: 2,
    course: "Course 3",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 25,
        weight: 0.25,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "low",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "medium",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 30,
        weight: 0.3,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "medium",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 20,
        weight: 0.2,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "low",
      },
    ],
  },
  // Course 3 - Vira Activity
  {
    activityType: "Vira Activity",
    activityNumber: 1,
    course: "Course 3",
    parameters: [
      {
        parameterId: "param1",
        parameterName: "Communication Clarity",
        maxScore: 30,
        weight: 0.3,
        description:
          "Clarity and effectiveness of verbal and written communication",
        performanceLevel: "medium",
      },
      {
        parameterId: "param2",
        parameterName: "Confidence",
        maxScore: 25,
        weight: 0.25,
        description: "Level of confidence and self-assurance demonstrated",
        performanceLevel: "medium",
      },
      {
        parameterId: "param3",
        parameterName: "Response Relevance",
        maxScore: 25,
        weight: 0.25,
        description:
          "Relevance and appropriateness of responses to questions/tasks",
        performanceLevel: "high",
      },
      {
        parameterId: "param4",
        parameterName: "Key Traits Demo",
        maxScore: 20,
        weight: 0.2,
        description:
          "Demonstration of key learning traits and professional skills",
        performanceLevel: "low",
      },
    ],
  },
];

// Enhanced parameter score generation with more realistic distributions
const generateParameterScores = (
  activityType: string,
  activityNumber: number,
  course: string,
  totalScore: number,
  maxScore: number,
): ParameterScore[] => {
  const rubric = mockActivityRubrics.find(
    (r) =>
      r.activityType === activityType &&
      r.activityNumber === activityNumber &&
      r.course === course,
  );

  if (!rubric) return [];

  const scoreRatio = totalScore / maxScore;

  return rubric.parameters.map((param) => {
    let parameterRatio = scoreRatio;

    // Adjust based on performance level indicators
    switch (param.performanceLevel) {
      case "high":
        parameterRatio *= 1.1 + Math.random() * 0.15; // 110-125% of base ratio
        break;
      case "medium":
        parameterRatio *= 0.95 + Math.random() * 0.2; // 95-115% of base ratio
        break;
      case "low":
        parameterRatio *= 0.75 + Math.random() * 0.2; // 75-95% of base ratio
        break;
    }

    // Add parameter-specific adjustments
    if (param.parameterName.includes("Communication Clarity")) {
      parameterRatio *= 1.05; // Communication tends to score higher
    } else if (param.parameterName.includes("Confidence")) {
      parameterRatio *= 0.9; // Confidence can vary more
    } else if (param.parameterName.includes("Response Relevance")) {
      parameterRatio *= 1.02; // Generally good performance
    } else if (param.parameterName.includes("Key Traits Demo")) {
      parameterRatio *= 0.88; // Skills demonstration often challenges students
    }

    // Ensure realistic bounds
    parameterRatio = Math.max(0.25, Math.min(1.0, parameterRatio));

    const parameterScore = Math.round(param.maxScore * parameterRatio);

    return {
      parameterId: param.parameterId,
      parameterName: param.parameterName,
      score: Math.max(0, Math.min(param.maxScore, parameterScore)),
      maxScore: param.maxScore,
    };
  });
};

// Course 1: AI activity, Quiz, Vira activity (3 total)
// Course 2: AI activity, Quiz (2 total) - WITH LOW PERFORMERS
// Course 3: 2 AI Activities, 2 Quizzes, 1 Vira Activity (5 total) - WITH LOW PERFORMERS

export const mockCohortSummaries: CohortSummary[] = [
  // Course 1 cohorts - Mixed performance for completion requirements
  {
    cohort: "Cohort A",
    course: "Course 1",
    totalStudents: 25,
    averageScore: 87.5,
    completionRate: 85.2, // Above 80%
    activities: {
      "AI Activity": { avg: 89.2, completed: 21 },
      Quiz: { avg: 85.1, completed: 22 },
      "Vira Activity": { avg: 88.3, completed: 21 },
    },
  },
  {
    cohort: "Cohort B",
    course: "Course 1",
    totalStudents: 28,
    averageScore: 82.3,
    completionRate: 67.5, // Below 80% but above 50%
    activities: {
      "AI Activity": { avg: 84.7, completed: 19 },
      Quiz: { avg: 79.8, completed: 20 },
      "Vira Activity": { avg: 82.4, completed: 18 },
    },
  },
  {
    cohort: "Cohort C",
    course: "Course 1",
    totalStudents: 30,
    averageScore: 91.2,
    completionRate: 92.1, // Above 80%
    activities: {
      "AI Activity": { avg: 93.1, completed: 28 },
      Quiz: { avg: 88.9, completed: 29 },
      "Vira Activity": { avg: 91.6, completed: 27 },
    },
  },
  // Course 2 cohorts - Include very low performers
  {
    cohort: "Cohort D",
    course: "Course 2",
    totalStudents: 22,
    averageScore: 68.4,
    completionRate: 41.3, // Below 50%
    activities: {
      "AI Activity": { avg: 64.2, completed: 9 },
      Quiz: { avg: 72.6, completed: 10 },
    },
  },
  {
    cohort: "Cohort E",
    course: "Course 2",
    totalStudents: 26,
    averageScore: 58.8,
    completionRate: 35.7, // Below 50%
    activities: {
      "AI Activity": { avg: 55.3, completed: 8 },
      Quiz: { avg: 62.2, completed: 11 },
    },
  },
  {
    cohort: "Cohort F",
    course: "Course 2",
    totalStudents: 24,
    averageScore: 74.7,
    completionRate: 73.4, // Below 80% but above 50%
    activities: {
      "AI Activity": { avg: 71.1, completed: 17 },
      Quiz: { avg: 78.3, completed: 19 },
    },
  },
  // Course 3 cohorts - Include one very low performer and one above 80%
  {
    cohort: "Cohort G",
    course: "Course 3",
    totalStudents: 32,
    averageScore: 66.6,
    completionRate: 48.1, // Below 50%
    activities: {
      "AI Activity 1": { avg: 62.2, completed: 20 },
      "AI Activity 2": { avg: 58.8, completed: 18 },
      "Quiz 1": { avg: 72.1, completed: 22 },
      "Quiz 2": { avg: 69.9, completed: 16 },
      "Vira Activity": { avg: 70.4, completed: 14 },
    },
  },
  {
    cohort: "Cohort H",
    course: "Course 3",
    totalStudents: 29,
    averageScore: 72.3,
    completionRate: 84.7, // Above 80%
    activities: {
      "AI Activity 1": { avg: 68.1, completed: 25 },
      "AI Activity 2": { avg: 65.7, completed: 24 },
      "Quiz 1": { avg: 76.3, completed: 27 },
      "Quiz 2": { avg: 74.9, completed: 25 },
      "Vira Activity": { avg: 76.5, completed: 22 },
    },
  },
];

export const mockActivitySummaries: ActivitySummary[] = [
  // Course 1 activities (keeping good performance)
  {
    activityType: "AI Activity",
    activityNumber: 1,
    course: "Course 1",
    totalAttempts: 83,
    averageScore: 88.9,
    averageTimeSpent: 45.2,
    completionRate: 81.9,
  },
  {
    activityType: "Quiz",
    activityNumber: 1,
    course: "Course 1",
    totalAttempts: 79,
    averageScore: 84.6,
    averageTimeSpent: 25.3,
    completionRate: 85.5,
  },
  {
    activityType: "Vira Activity",
    activityNumber: 1,
    course: "Course 1",
    totalAttempts: 74,
    averageScore: 87.4,
    averageTimeSpent: 38.7,
    completionRate: 79.5,
  },
  // Course 2 activities (with low performers)
  {
    activityType: "AI Activity",
    activityNumber: 1,
    course: "Course 2",
    totalAttempts: 68,
    averageScore: 63.5,
    averageTimeSpent: 52.1,
    completionRate: 47.2,
  },
  {
    activityType: "Quiz",
    activityNumber: 1,
    course: "Course 2",
    totalAttempts: 62,
    averageScore: 71.0,
    averageTimeSpent: 28.8,
    completionRate: 55.6,
  },
  // Course 3 activities (with low performers)
  {
    activityType: "AI Activity",
    activityNumber: 1,
    course: "Course 3",
    totalAttempts: 58,
    averageScore: 65.2,
    averageTimeSpent: 55.3,
    completionRate: 65.6,
  },
  {
    activityType: "AI Activity",
    activityNumber: 2,
    course: "Course 3",
    totalAttempts: 55,
    averageScore: 62.3,
    averageTimeSpent: 58.1,
    completionRate: 62.3,
  },
  {
    activityType: "Quiz",
    activityNumber: 1,
    course: "Course 3",
    totalAttempts: 58,
    averageScore: 74.2,
    averageTimeSpent: 26.5,
    completionRate: 73.8,
  },
  {
    activityType: "Quiz",
    activityNumber: 2,
    course: "Course 3",
    totalAttempts: 53,
    averageScore: 72.4,
    averageTimeSpent: 28.2,
    completionRate: 67.2,
  },
  {
    activityType: "Vira Activity",
    activityNumber: 1,
    course: "Course 3",
    totalAttempts: 51,
    averageScore: 73.5,
    averageTimeSpent: 43.6,
    completionRate: 59.0,
  },
];

// Generate comprehensive mock activity results with enhanced parameter data
const generateMockActivityResults = (): ActivityResult[] => {
  const results: ActivityResult[] = [];
  let studentCounter = 1;

  // Generate results for Course 1
  const course1Cohorts = ["Cohort A", "Cohort B", "Cohort C"];
  const course1Activities = [
    { type: "AI Activity", number: 1 },
    { type: "Quiz", number: 1 },
    { type: "Vira Activity", number: 1 },
  ];

  course1Cohorts.forEach((cohort) => {
    const studentsPerCohort =
      cohort === "Cohort A" ? 25 : cohort === "Cohort B" ? 28 : 30;
    const organizations = ["TechCorp", "EduInc", "LearnLab"];

    for (let i = 1; i <= studentsPerCohort; i++) {
      const studentId = `C1${cohort.charAt(7)}${String(i).padStart(3, "0")}`;
      const studentName = `Student ${studentCounter++}`;
      const organization = organizations[i % 3];

      course1Activities.forEach((activity) => {
        // Higher completion rates for Course 1
        const completionChance =
          cohort === "Cohort C" ? 0.92 : cohort === "Cohort A" ? 0.85 : 0.67;

        if (Math.random() < completionChance) {
          const baseScore =
            cohort === "Cohort C" ? 91 : cohort === "Cohort A" ? 87 : 82;
          const variance = 8 + Math.random() * 12;
          const score = Math.max(
            45,
            Math.min(
              100,
              Math.round(baseScore + (Math.random() - 0.5) * variance),
            ),
          );

          results.push({
            studentId,
            studentName,
            cohort,
            course: "Course 1",
            activityType: activity.type,
            activityNumber: activity.number,
            score,
            maxScore: 100,
            timeSpent: Math.round(20 + Math.random() * 40),
            completedAt: "2024-12-10",
            organization,
            parameters: generateParameterScores(
              activity.type,
              activity.number,
              "Course 1",
              score,
              100,
            ),
          });
        }
      });
    }
  });

  // Generate results for Course 2
  const course2Cohorts = ["Cohort D", "Cohort E", "Cohort F"];
  const course2Activities = [
    { type: "AI Activity", number: 1 },
    { type: "Quiz", number: 1 },
  ];

  course2Cohorts.forEach((cohort) => {
    const studentsPerCohort =
      cohort === "Cohort D" ? 22 : cohort === "Cohort E" ? 26 : 24;
    const organizations = ["TechCorp", "EduInc", "LearnLab"];

    for (let i = 1; i <= studentsPerCohort; i++) {
      const studentId = `C2${cohort.charAt(7)}${String(i).padStart(3, "0")}`;
      const studentName = `Student ${studentCounter++}`;
      const organization = organizations[i % 3];

      course2Activities.forEach((activity) => {
        // Lower completion rates for Course 2
        const completionChance =
          cohort === "Cohort F" ? 0.73 : cohort === "Cohort D" ? 0.41 : 0.36;

        if (Math.random() < completionChance) {
          const baseScore =
            cohort === "Cohort F" ? 74 : cohort === "Cohort D" ? 68 : 58;
          const variance = 12 + Math.random() * 18;
          const score = Math.max(
            30,
            Math.min(
              95,
              Math.round(baseScore + (Math.random() - 0.5) * variance),
            ),
          );

          results.push({
            studentId,
            studentName,
            cohort,
            course: "Course 2",
            activityType: activity.type,
            activityNumber: activity.number,
            score,
            maxScore: 100,
            timeSpent: Math.round(25 + Math.random() * 50),
            completedAt: "2024-12-10",
            organization,
            parameters: generateParameterScores(
              activity.type,
              activity.number,
              "Course 2",
              score,
              100,
            ),
          });
        }
      });
    }
  });

  // Generate results for Course 3
  const course3Cohorts = ["Cohort G", "Cohort H"];
  const course3Activities = [
    { type: "AI Activity", number: 1 },
    { type: "AI Activity", number: 2 },
    { type: "Quiz", number: 1 },
    { type: "Quiz", number: 2 },
    { type: "Vira Activity", number: 1 },
  ];

  course3Cohorts.forEach((cohort) => {
    const studentsPerCohort = cohort === "Cohort G" ? 32 : 29;
    const organizations = ["TechCorp", "EduInc", "LearnLab"];

    for (let i = 1; i <= studentsPerCohort; i++) {
      const studentId = `C3${cohort.charAt(7)}${String(i).padStart(3, "0")}`;
      const studentName = `Student ${studentCounter++}`;
      const organization = organizations[i % 3];

      course3Activities.forEach((activity, actIndex) => {
        // Decreasing completion rates for later activities
        const baseCompletionRate = cohort === "Cohort H" ? 0.85 : 0.48;
        const activityPenalty = actIndex * 0.04; // Each later activity has 4% lower completion
        const completionChance = Math.max(
          0.2,
          baseCompletionRate - activityPenalty,
        );

        if (Math.random() < completionChance) {
          const baseScore = cohort === "Cohort H" ? 72 : 66;
          const variance = 10 + Math.random() * 16;
          const score = Math.max(
            35,
            Math.min(
              95,
              Math.round(baseScore + (Math.random() - 0.5) * variance),
            ),
          );

          results.push({
            studentId,
            studentName,
            cohort,
            course: "Course 3",
            activityType: activity.type,
            activityNumber: activity.number,
            score,
            maxScore: 100,
            timeSpent: Math.round(30 + Math.random() * 60),
            completedAt: "2024-12-10",
            organization,
            parameters: generateParameterScores(
              activity.type,
              activity.number,
              "Course 3",
              score,
              100,
            ),
          });
        }
      });
    }
  });

  return results;
};

export const mockActivityResults: ActivityResult[] =
  generateMockActivityResults();

export const mockCourseSummaries: CourseSummary[] = [
  {
    course: "Course 1",
    totalActivities: 3,
    activityTypes: ["AI Activity", "Quiz", "Vira Activity"],
    totalStudents: 83,
    totalCohorts: 3,
  },
  {
    course: "Course 2",
    totalActivities: 2,
    activityTypes: ["AI Activity", "Quiz"],
    totalStudents: 72,
    totalCohorts: 3,
  },
  {
    course: "Course 3",
    totalActivities: 5,
    activityTypes: ["AI Activity", "Quiz", "Vira Activity"],
    totalStudents: 61,
    totalCohorts: 2,
  },
];

export const mockOrganizationSummaries: OrganizationSummary[] = [
  {
    organization: "TechCorp",
    totalStudents: 78,
    totalCohorts: 4,
    averageScore: 73.3,
    completionRate: 69.8,
    topPerformingCohort: "Cohort C",
  },
  {
    organization: "EduInc",
    totalStudents: 65,
    totalCohorts: 3,
    averageScore: 69.7,
    completionRate: 65.2,
    topPerformingCohort: "Cohort H",
  },
  {
    organization: "LearnLab",
    totalStudents: 73,
    totalCohorts: 3,
    averageScore: 71.1,
    completionRate: 72.1,
    topPerformingCohort: "Cohort H",
  },
];

// Helper function to generate parameter performance data for visualization
export const generateParameterPerformanceData = (
  course: string,
  activityType: string,
  activityNumber: number,
  activityRubrics: ActivityRubric[],
  mActivityResults: ActivityResult[],
): ParameterPerformanceData[] => {
  const rubric = activityRubrics.find(
    (r) =>
      r.course === course &&
      r.activityType === activityType &&
      r.activityNumber === activityNumber,
  );

  if (!rubric) return [];

  const activityResults = mActivityResults.filter(
    (result) =>
      result.course === course &&
      result.activityType === activityType &&
      result.activityNumber === activityNumber &&
      result.parameters &&
      result.parameters.length > 0,
  );

  return rubric.parameters.map((param) => {
    const parameterScores = activityResults
      .map((r) =>
        r.parameters!.find((p) => p.parameterName === param.parameterName),
      )
      .filter(Boolean)
      .map((p) => (p!.score / p!.maxScore) * 100);

    if (parameterScores.length === 0) {
      // Generate realistic fallback data
      const baseScore =
        param.performanceLevel === "high"
          ? 78
          : param.performanceLevel === "medium"
            ? 68
            : 58;
      const variance = 12;
      const mockScores = Array.from({ length: 25 }, () =>
        Math.max(
          30,
          Math.min(95, baseScore + (Math.random() - 0.5) * variance),
        ),
      );

      const avg =
        mockScores.reduce((sum, score) => sum + score, 0) / mockScores.length;
      const variance2 =
        mockScores.reduce((sum, score) => sum + Math.pow(score - avg, 2), 0) /
        mockScores.length;

      return {
        parameterId: param.parameterId,
        parameterName: param.parameterName,
        averageScore: Math.round(avg * 10) / 10,
        minScore: Math.min(...mockScores),
        maxScore: Math.max(...mockScores),
        standardDeviation: Math.sqrt(variance2),
        studentCount: mockScores.length,
        performanceLevel:
          avg >= 80
            ? ("excellent" as const)
            : avg >= 70
              ? ("good" as const)
              : avg >= 60
                ? ("satisfactory" as const)
                : ("needs-improvement" as const),
        cohortBreakdown: [],
      };
    }

    const avgScore =
      parameterScores.reduce((sum, score) => sum + score, 0) /
      parameterScores.length;
    const variance =
      parameterScores.reduce(
        (sum, score) => sum + Math.pow(score - avgScore, 2),
        0,
      ) / parameterScores.length;

    // Group by cohort for breakdown
    const cohortBreakdown = [
      ...new Set(activityResults.map((r) => r.cohort)),
    ].map((cohort) => {
      const cohortResults = activityResults.filter((r) => r.cohort === cohort);
      const cohortScores = cohortResults
        .map((r) =>
          r.parameters!.find((p) => p.parameterName === param.parameterName),
        )
        .filter(Boolean)
        .map((p) => (p!.score / p!.maxScore) * 100);

      return {
        cohort,
        averageScore:
          cohortScores.length > 0
            ? Math.round(
                (cohortScores.reduce((sum, score) => sum + score, 0) /
                  cohortScores.length) *
                  10,
              ) / 10
            : 0,
        studentCount: cohortScores.length,
      };
    });

    return {
      parameterId: param.parameterId,
      parameterName: param.parameterName,
      averageScore: Math.round(avgScore * 10) / 10,
      minScore: Math.min(...parameterScores),
      maxScore: Math.max(...parameterScores),
      standardDeviation: Math.sqrt(variance),
      studentCount: parameterScores.length,
      performanceLevel:
        avgScore >= 80
          ? ("excellent" as const)
          : avgScore >= 70
            ? ("good" as const)
            : avgScore >= 60
              ? ("satisfactory" as const)
              : ("needs-improvement" as const),
      cohortBreakdown,
    };
  });
};
