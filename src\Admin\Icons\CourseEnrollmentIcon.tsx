import React from "react";

interface CourseEnrollmentIconProps {
  selected?: boolean;
}

function CourseEnrollmentIcon({ selected = false }: CourseEnrollmentIconProps) {
  return (
    <svg 
      width="14" 
      height="22" 
      viewBox="0 0 14 22" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <path 
        d="M0.777778 12.1693H5.44444C5.87222 12.1693 6.22222 11.6216 6.22222 10.9523V1.21693C6.22222 0.547617 5.87222 0 5.44444 0H0.777778C0.35 0 0 0.547617 0 1.21693V10.9523C0 11.6216 0.35 12.1693 0.777778 12.1693ZM0.777778 21.9047H5.44444C5.87222 21.9047 6.22222 21.357 6.22222 20.6877V15.82C6.22222 15.1507 5.87222 14.6031 5.44444 14.6031H0.777778C0.35 14.6031 0 15.1507 0 15.82V20.6877C0 21.357 0.35 21.9047 0.777778 21.9047ZM8.55556 21.9047H13.2222C13.65 21.9047 14 21.357 14 20.6877V10.9523C14 10.283 13.65 9.7354 13.2222 9.7354H8.55556C8.12778 9.7354 7.77778 10.283 7.77778 10.9523V20.6877C7.77778 21.357 8.12778 21.9047 8.55556 21.9047ZM7.77778 1.21693V6.08463C7.77778 6.75394 8.12778 7.30155 8.55556 7.30155H13.2222C13.65 7.30155 14 6.75394 14 6.08463V1.21693C14 0.547617 13.65 0 13.2222 0H8.55556C8.12778 0 7.77778 0.547617 7.77778 1.21693Z" 
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default CourseEnrollmentIcon;
