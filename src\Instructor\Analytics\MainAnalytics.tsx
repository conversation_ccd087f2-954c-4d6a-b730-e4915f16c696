import { Routes, Route, useLocation } from "react-router-dom";
import Overview from "./Overview";
import LoginUsersTable from "./LoginUsers";
import HardwareTestTable from "./HardwareTest";
import PlungeActivityTable from "./PlungeActivity";
import QuizTable from "./Quiz";
import AIDashboard from "./AIAssessment";
import PreCourseSurveyAnalytics from "./PreCourseSurvey";
import FeedbackAnalytics from "./Feedback";

function Analytics() {
    return (
        <div>
            <h2 className="text-lg font-semibold mb-4 bg-gray-200 p-2 text-blue-800">{HeadingText()}</h2>
            <Routes>
                <Route path="/" element={<Overview />} />
                <Route path="/overview" element={<Overview />} />
                <Route path="/login-users" element={<LoginUsersTable />} />
                <Route path="/hardware-test" element={<HardwareTestTable />} />
                <Route path="/plunge-activity" element={<PlungeActivityTable />} />
                <Route path="/quiz" element={<QuizTable />} />
                <Route path="/ai-practice" element={<AIDashboard />} />
                <Route path="/pre-survey" element={<PreCourseSurveyAnalytics />} />
                <Route path="/feedback" element={<FeedbackAnalytics />} />
            </Routes>
        </div>
    );
}

function HeadingText() {
    const location = useLocation();

    let headerText = "";
    switch (location.pathname) {
        case "/instructor/analytics/overview":
            headerText = "Overview";
            break;
        case "/instructor/analytics/login-users":
            headerText = "Login Users";
            break;
        case "/instructor/analytics/hardware-test":
            headerText = "Hardware Test";
            break;
        case "/instructor/analytics/plunge-activity":
            headerText = "Plunge Activity";
            break;
        case "/instructor/analytics/quiz":
            headerText = "Quiz";
            break;
        case "/instructor/analytics/ai-practice":
            headerText = "AI Assessment";
            break;
        case "/instructor/analytics/pre-survey":
            headerText = "Pre Course Survey";
            break;
        case "/instructor/analytics/feedback":
            headerText = "Feedback";
            break;
        case "/instructor/tutorial":
            headerText = "Tutorial";
            break;
        case "/instructor/video_example":
            headerText = "Video Example";
            break;
        case "/instructor/pre_course_survey":
            headerText = "Pre Course Survey";
            break;
        case "/instructor/recap":
            headerText = "Recap";
            break;
        case "/instructor/hardware_test":
            headerText = "Hardware Test";
            break;
        case "/instructor/forum":
            headerText = "Forum";
            break;
        case "/instructor/boss_challenge":
            headerText = "Boss Challenge";
            break;
        default:
            headerText = "Analytics";
    }

    return headerText;
}

export default Analytics;
