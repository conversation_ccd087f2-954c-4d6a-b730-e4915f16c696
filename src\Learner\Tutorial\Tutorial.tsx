import React, { useEffect, useState } from "react";
import NextButton from "../components/NextButton";
import Spinner from "../components/Spinner";
import { useLocation } from "react-router-dom";
import { usePostActivityProgress } from "../hooks/usePostActivityProgress";
import { useGetActivity } from "../hooks/useGetActivity";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import "../components/VideoStyle.css";
import useGetHeyGenURL from "../hooks/useGetHeyGenURL";

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

const Tutorial: React.FC = () => {
  const queryClient = useQueryClient();
  const query = useQueryParam();
  const course_id = query.get("course_id") ?? "";
  const activity_id = query.get("activity_id") ?? "";
  const mutation = usePostActivityProgress();
  const [heygenVideoUrl, setHeygenVideoUrl] = useState<string | null>(null);
  const getHeyGenURL = useGetHeyGenURL();
  const {
    data: activity,
    isLoading,
    isFetching,
    isError,
  } = useGetActivity(activity_id);
  const navigate = useNavigate();

  useEffect(() => {
    queryClient.invalidateQueries({ queryKey: ["activity"] });
  }, [activity_id]);

  // Extract Heygen video URL from activity data
  useEffect(() => {
    if (!activity) return;

    // First check in extra_fields (preferred method)
    if (activity.extra_fields && activity.extra_fields.heygen_video_url) {
      console.log(
        "Setting Heygen video from extra_fields:",
        activity.extra_fields.heygen_video_url,
      );
      setHeygenVideoUrl(activity.extra_fields.heygen_video_url);
      return;
    }

    // Then check in instructions (legacy method)
    if (activity.instructions) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = activity.instructions;
      const heygenDiv = tempDiv.querySelector("div[data-heygen-video-url]");

      if (heygenDiv) {
        const videoId = heygenDiv.getAttribute("data-heygen-video-id");
        if (videoId) {
          getHeyGenURL(videoId).then((url) => {
            setHeygenVideoUrl(url);
          });
          console.log("Setting Heygen video from instructions:", videoId);
          return;
        }
      }
    }

    // If no Heygen video found, reset the state
    setHeygenVideoUrl(null);
  }, [activity]);

  const submitAndRedirectToLink = async () => {
    mutation.mutate(
      { courseId: course_id, activityId: activity_id, userInput: "" },
      {
        onSuccess: (data) => {
          if (data && data.next_activity_link) {
            navigate("/my-learning" + data.next_activity_link);
          }
        },
        onError: () => {
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  const handleContextMenu = (event: React.MouseEvent<HTMLVideoElement>) => {
    event.preventDefault(); // Prevents the right-click menu from opening
  };

  return (
    <div>
      <Spinner loading={(isLoading || isFetching) && !isError} />
      <div>
        <div className="flex flex-col items-center justify-center space-y-14 py-10 text-white">
          {activity?.instructions && (
            <p
              className="mb-4"
              dangerouslySetInnerHTML={{ __html: activity.instructions }}
            />
          )}
          {(activity?.video || heygenVideoUrl) && (
            <div className="flex bg-slate-300 p-3">
              <video
                key={heygenVideoUrl || activity?.video}
                width="607"
                height="362"
                controls
                controlsList="nodownload"
                onContextMenu={handleContextMenu}
                className="hide-seekbar-tutorial"
              >
                <source
                  src={heygenVideoUrl || activity?.video || ""}
                  type="video/mp4"
                />
              </video>
            </div>
          )}
          {activity?.title_videos?.map((each) => (
            <div key={each.video} className="bg-slate-300 p-3">
              <h1 className="text-xl">{each.title}</h1>
              <video
                key={each.video}
                width="607"
                height="362"
                controls
                controlsList="nodownload"
                onContextMenu={handleContextMenu}
                className="hide-seekbar-tutorial"
              >
                <source src={each.video} type="video/mp4" />
              </video>
            </div>
          ))}
          {activity?.instructions_below && (
            <p
              className="mb-4"
              dangerouslySetInnerHTML={{ __html: activity.instructions_below }}
            />
          )}
          <NextButton onClick={submitAndRedirectToLink} />
        </div>
      </div>
    </div>
  );
};

export default Tutorial;
