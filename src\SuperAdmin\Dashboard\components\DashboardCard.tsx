import { GoalIcon } from "lucide-react";
import { useState } from "react";

type DashboardCardProps = {
  title: string;
  icon: React.ReactNode;
  onClick: () => void;
};

function DashboardCard({ title, icon, onClick }: DashboardCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const handleHover = () => {
    setIsHovered(!isHovered);
  };
  return (
    <div
      className="flex w-fit cursor-pointer flex-col"
      onMouseEnter={handleHover}
      onMouseLeave={handleHover}
      onClick={onClick}
    >
      <h1 className="mb-3 text-sm font-medium text-[#020202]">{title}</h1>
      <div className="relative flex h-40 w-72 flex-row items-center justify-around rounded-lg bg-[#9A9A9A] bg-opacity-20 p-7">
        {isHovered ? (
          <div className="absolute right-3 top-3">
            <HoverIcon />
          </div>
        ) : null}
        {icon}
        <h1 className="text-lg font-semibold text-[#020202]">{title}</h1>
      </div>
    </div>
  );
}

function HoverIcon() {
  return (
    <svg
      width="16"
      height="20"
      viewBox="0 0 16 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.7412 0.897583C13.3156 0.897583 15.3963 1.91427 15.4248 4.49817V18.9181C15.4248 19.0796 15.3865 19.2414 15.3105 19.3839C15.187 19.6118 14.9781 19.7825 14.7217 19.8585C14.4748 19.9344 14.1996 19.8967 13.9717 19.7638L9.60301 17.5795C8.47762 17.0168 7.15305 17.0164 6.02734 17.5784L1.65039 19.7638C1.50887 19.8388 1.34603 19.8878 1.18457 19.8878C0.652813 19.8876 0.225586 19.4499 0.225586 18.9181V4.49817C0.225586 1.91427 2.31596 0.897583 4.88086 0.897583H10.7412Z"
        fill="#E6635A"
      />
      <rect
        x="7.19897"
        y="5.45496"
        width="1.61066"
        height="6.44266"
        rx="0.805332"
        fill="white"
      />
      <rect
        x="11.2256"
        y="7.87096"
        width="1.61066"
        height="6.44266"
        rx="0.805332"
        transform="rotate(90 11.2256 7.87096)"
        fill="white"
      />
    </svg>
  );
}

export default DashboardCard;
