import React, { useEffect, useState } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import Spinner from "../../Learner/components/Spinner";
import { useGetUserDetailedResults } from "../hooks/useGetUserDetailedResults";
import { useDownloadUserResults } from "../hooks/useDownloadUserResults";
import showError from "../../Learner/scripts/showErrorDialog";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import QuizResult from "../../Instructor/Analytics/UserQuizResult";
import { useGetActivityResult } from "../../Instructor/hooks/useGetActivityResult";
import ListeningComponent from "../../Learner/AIActivity/ListeningComponent";
import RespondingComponent from "../../Learner/AIActivity/RespondingComponent";
import Markdown from "react-markdown";

const UserDetailedResults: React.FC = () => {
  const { userId = "", courseId = "" } = useParams<{
    userId: string;
    courseId: string;
  }>();
  const location = useLocation();
  const navigate = useNavigate();
  const [selectedQuizId, setSelectedQuizId] = useState<string | null>(null);
  const [selectedAIActivityId, setSelectedAIActivityId] = useState<
    string | null
  >(null);

  const { data: userResults, isFetching } = useGetUserDetailedResults(
    userId,
    courseId,
  );
  const downloadMutation = useDownloadUserResults();
  const { data: quizResult, isFetching: isFetchingQuiz } = useGetActivityResult(
    userId,
    "quiz",
  );
  const { data: aiActivityResult, isFetching: isFetchingAI } =
    useGetActivityResult(userId, "practice_activity");

  // Add console log to check AI activity results
  console.log("AI Activity Result:", aiActivityResult);

  // Add console log to check activities
  console.log("Activities:", userResults?.activities);

  const handleDownload = async () => {
    try {
      const blob = await downloadMutation.mutateAsync({ userId, courseId });

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `user-${userId}-course-${courseId}-results.csv`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      showSuccess("Success", "Results downloaded successfully!");
    } catch (error) {
      console.error("Download failed:", error);
      showError(
        "Download Failed",
        "There was an error downloading the results. Please try again.",
      );
    }
  };

  const handleBack = () => {
    navigate("/admin/results", {
      state: {
        page: location.state?.page || 0,
        rowsPerPage: location.state?.rowsPerPage || 10,
        selectedCourse: location.state?.selectedCourse || "1",
      },
    });
  };

  const toggleQuizResults = (quizId: string) => {
    setSelectedQuizId(selectedQuizId === quizId ? null : quizId);
  };

  const toggleAIActivityResults = (activityId: string) => {
    setSelectedAIActivityId(
      selectedAIActivityId === activityId ? null : activityId,
    );
  };

  return (
    <>
      <Spinner
        loading={
          isFetching ||
          downloadMutation.isPending ||
          isFetchingQuiz ||
          isFetchingAI
        }
      />
      <div className="rounded-lg bg-gray-100 p-6 shadow">
        {userResults && (
          <>
            <div className="mb-6">
              <div className="mb-4 rounded-lg bg-[#36537F] p-3 text-center text-lg font-semibold text-white">
                {userResults.userName}
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full bg-white">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-4 py-2 text-left">Name</th>
                      <th className="px-4 py-2 text-left">Id</th>
                      <th className="px-4 py-2 text-left">Course</th>
                      <th className="px-4 py-2 text-left">Result</th>
                      <th className="px-4 py-2 text-left">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {userResults.activities.map((activity, index) => {
                      // Add debug log for each activity
                      console.log(
                        "Activity type:",
                        activity.type,
                        "Lowercase:",
                        activity.type.toLowerCase(),
                      );

                      return (
                        <React.Fragment key={index}>
                          <tr className="border-b hover:bg-gray-50">
                            <td className="px-4 py-2">{activity.name}</td>
                            <td className="px-4 py-2">{activity.id}</td>
                            <td className="px-4 py-2">{activity.type}</td>
                            <td className="px-4 py-2">{activity.result}</td>
                            <td className="px-4 py-2">
                              {activity.type.toLowerCase() === "quiz" && (
                                <button
                                  onClick={() => toggleQuizResults(activity.id)}
                                  className="rounded bg-[#EEC300] px-4 py-2 text-white hover:bg-[#d4af00]"
                                >
                                  {selectedQuizId === activity.id
                                    ? "Hide Quiz"
                                    : "Show Quiz"}
                                </button>
                              )}
                              {/* Add debug comment to show exact comparison */}
                              {/* Debug: {activity.type.toLowerCase()} === "practice_activity" */}
                              {(activity.type.toLowerCase() ===
                                "practice_activity" ||
                                activity.type.toLowerCase() === "ai activity" ||
                                activity.type.toLowerCase() ===
                                  "practiceactivity") && (
                                <button
                                  onClick={() =>
                                    toggleAIActivityResults(activity.id)
                                  }
                                  className="rounded bg-[#36537F] px-4 py-2 text-white hover:bg-[#2b4266]"
                                >
                                  {selectedAIActivityId === activity.id
                                    ? "Hide AI Results"
                                    : "Show AI Results"}
                                </button>
                              )}
                            </td>
                          </tr>
                          {selectedQuizId === activity.id &&
                            quizResult &&
                            quizResult.length > 0 && (
                              <tr>
                                <td colSpan={5} className="px-4 py-4">
                                  <QuizResult
                                    user_input={
                                      quizResult[
                                        aiActivityResult
                                          ? index - aiActivityResult.length
                                          : 0
                                      ].user_input
                                    }
                                    result={
                                      quizResult[
                                        aiActivityResult
                                          ? index - aiActivityResult.length
                                          : 0
                                      ].result
                                    }
                                  />
                                </td>
                              </tr>
                            )}
                          {selectedAIActivityId === activity.id &&
                            aiActivityResult &&
                            aiActivityResult.length > 0 && (
                              <tr>
                                <td colSpan={5} className="px-4 py-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    {/* Add console log to check the specific result data */}

                                    {aiActivityResult[0].result && (
                                      <>
                                        <div className="rounded-[24px] bg-yellow-300 p-4">
                                          <ListeningComponent
                                            listeningData={{
                                              facial_expressions:
                                                aiActivityResult[index].result
                                                  .listening.listening
                                                  ?.facial_expressions || 0,
                                              eye_contact:
                                                aiActivityResult[index].result
                                                  .listening.listening
                                                  ?.eye_contact || 0,
                                              body_movement_and_posture:
                                                aiActivityResult[index].result
                                                  .listening.listening
                                                  ?.body_movement_and_posture ||
                                                0,
                                              gestures:
                                                aiActivityResult[index].result
                                                  .listening.listening
                                                  ?.gestures || 0,
                                              tone_and_manner_of_speech:
                                                aiActivityResult[index].result
                                                  .listening.listening
                                                  ?.tone_and_manner_of_speech ||
                                                0,
                                              choice_of_words:
                                                aiActivityResult[index].result
                                                  .listening.listening
                                                  ?.choice_of_words || 0,
                                            }}
                                            youDidWellAt={
                                              aiActivityResult[index].result
                                                .listening
                                                .you_did_well_at_the_following ||
                                              []
                                            }
                                            youCanImproveOn={
                                              aiActivityResult[index].result
                                                .listening
                                                .you_can_improve_by_focusing_on_the_following ||
                                              []
                                            }
                                          />
                                        </div>
                                        <div className="rounded-[44px] bg-[#22409A] p-4">
                                          <div className="mx-auto w-full max-w-md rounded-[24px] bg-yellow-300 p-4 text-left text-black shadow-lg md:rounded-[44px]">
                                            <h2 className="mb-4 text-center text-xl font-semibold">
                                              Responding
                                            </h2>
                                            <Markdown>
                                              {String(
                                                aiActivityResult[index].result
                                                  .responding as any,
                                              )}
                                            </Markdown>
                                          </div>
                                        </div>
                                      </>
                                    )}
                                  </div>
                                </td>
                              </tr>
                            )}
                        </React.Fragment>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="mt-6 flex justify-between">
              <button
                onClick={handleBack}
                className="rounded bg-green-600 px-4 py-2 text-white hover:bg-green-700"
              >
                Back
              </button>

              <button
                onClick={handleDownload}
                className="rounded bg-[#36537F] px-4 py-2 text-white hover:bg-blue-700"
                disabled={downloadMutation.isPending}
              >
                Download Individual Results
              </button>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default UserDetailedResults;
