import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts";

// Sample data
const data = [
  { month: "Jan", enrolled: 5000, notEnrolled: 12000 },
  { month: "Feb", enrolled: 10000, notEnrolled: 18000 },
  { month: "Mar", enrolled: 20000, notEnrolled: 22000 },
  { month: "Apr", enrolled: 15000, notEnrolled: 25000 },
  { month: "May", enrolled: 25000, notEnrolled: 30000 },
  { month: "Jun", enrolled: 30000, notEnrolled: 40000 },
  { month: "Jul", enrolled: 20000, notEnrolled: 35000 },
  { month: "Aug", enrolled: 53000, notEnrolled: 207 },
  { month: "Sep", enrolled: 40000, notEnrolled: 45000 },
  { month: "Oct", enrolled: 45000, notEnrolled: 50000 },
  { month: "Nov", enrolled: 55000, notEnrolled: 60000 },
  { month: "Dec", enrolled: 60000, notEnrolled: 70000 },
];

const LearnerProgressChart: React.FC = () => {
  return (
    <div className="flex flex-col space-y-8">
      <h1 className="text-2xl font-medium text-[#6f6f6f]">Learner Progress</h1>
      <div className="rounded-lg bg-gray-800 p-6 shadow-lg">
        <ResponsiveContainer width="100%" height={400}>
          <AreaChart
            data={data}
            margin={{ top: 20, right: 30, left: 0, bottom: 0 }}
          >
            <defs>
              <linearGradient id="colorEnrolled" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#2563eb" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#2563eb" stopOpacity={0} />
              </linearGradient>
              <linearGradient id="colorNotEnrolled" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#f87171" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#f87171" stopOpacity={0} />
              </linearGradient>
            </defs>
            <XAxis
              dataKey="month"
              tick={{ fill: "#e5e7eb" }}
              axisLine={{ stroke: "#e5e7eb" }}
              tickLine={{ stroke: "#e5e7eb" }}
            />
            <YAxis
              tick={{ fill: "#e5e7eb" }}
              axisLine={{ stroke: "#e5e7eb" }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "#1f2937",
                borderColor: "#374151",
              }}
              labelStyle={{ color: "#e5e7eb" }}
              itemStyle={{ color: "#e5e7eb" }}
            />
            <Area
              type="monotone"
              dataKey="enrolled"
              stroke="#2563eb"
              fillOpacity={1}
              fill="url(#colorEnrolled)"
            />
            <Area
              type="monotone"
              dataKey="notEnrolled"
              stroke="#f87171"
              fillOpacity={1}
              fill="url(#colorNotEnrolled)"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default LearnerProgressChart;
