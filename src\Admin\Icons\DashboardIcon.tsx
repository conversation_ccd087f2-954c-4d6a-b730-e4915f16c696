import React from "react";

interface DashboardIconProps {
  selected?: boolean;
}

function DashboardIcon({ selected = false }: DashboardIconProps) {
  return (
    <svg
      width="14"
      height="15"
      viewBox="0 0 14 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.19658 0.525002C5.22762 0.248436 6.32328 0 6.97896 0C7.63464 0 8.7303 0.249374 9.76134 0.524063C10.6823 0.774124 11.5971 1.04607 12.5048 1.33969C12.7663 1.42503 12.9981 1.5814 13.1735 1.79084C13.349 2.00029 13.4611 2.25431 13.4968 2.52375C14.0632 6.72093 12.749 9.83156 11.1544 11.8894C10.4783 12.7697 9.672 13.5449 8.76261 14.1891C8.44816 14.412 8.11499 14.608 7.76673 14.775C7.4997 14.8987 7.21462 15 6.97896 15C6.74329 15 6.45726 14.8987 6.19119 14.775C5.90231 14.6409 5.56211 14.445 5.19531 14.1891C4.28593 13.5448 3.47968 12.7697 2.80349 11.8894C1.20894 9.83156 -0.105278 6.72093 0.46108 2.52375C0.496893 2.25444 0.608984 2.00056 0.784472 1.79128C0.959961 1.582 1.19173 1.4258 1.45316 1.34062C2.07843 1.13906 3.14178 0.806252 4.19658 0.525002Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default DashboardIcon;
