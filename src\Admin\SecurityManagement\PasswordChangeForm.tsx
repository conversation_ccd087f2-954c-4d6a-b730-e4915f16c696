import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined';
import ChangePasswordIcon from "../Icons/ChangePasswordIcon";

// Zod validation schema
const schema = z
  .object({
    oldPassword: z.string().min(1, "Old password is required"),
    newPassword: z
      .string()
      .min(6, "New password must be at least 6 characters"),
    confirmPassword: z.string().min(1, "Please confirm your new password"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

const onSubmit = <TData,>(data: TData) => {
  // Handle form submission
  console.log("Password changed:", data);
};

const PasswordChangeForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(schema),
  });

  // State for toggling password visibility
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
      <div className="flex items-center space-x-8">
        <h2 className="text-2xl font-medium text-[#6F6F6F]">Change Password</h2>
        <ChangePasswordIcon />
      </div>

      {/* Old Password */}
      <div className="flex w-full items-center justify-between">
        <div className="mb-4 flex flex-col relative">
          <label className="text-lg font-medium text-[#484848]">Old Password</label>
          <input
            type={showOldPassword ? "text" : "password"} // Toggle between text and password
            {...register("oldPassword")}
            className="h-[37px] w-[434px] rounded-md border border-[#5B5B5B] p-2"
          />
          {/* Eye icon to toggle visibility */}
          <span
            className="absolute right-4 top-8 cursor-pointer"
            onClick={() => setShowOldPassword(!showOldPassword)}
          >
            {showOldPassword ? <VisibilityOffOutlinedIcon style={{ color: 'grey' }} /> : <RemoveRedEyeOutlinedIcon style={{ color: 'grey' }} />}
          </span>
          {errors.oldPassword && (
            <p className="mt-1 text-sm text-red-500">
              {errors.oldPassword?.message as string}
            </p>
          )}
        </div>
        <div className="mb-4 flex flex-shrink flex-col relative"></div>
      </div>

      <div className="flex w-full items-center justify-between">
        {/* New Password */}
        <div className="mb-4 flex flex-shrink flex-col relative">
          <label className="text-lg font-medium text-[#484848]">New Password</label>
          <input
            type={showNewPassword ? "text" : "password"} // Toggle between text and password
            {...register("newPassword")}
            className="h-[37px] w-[434px] rounded-md border border-[#5B5B5B] p-2"
          />
          {/* Eye icon to toggle visibility */}
          <span
            className="absolute right-4 top-8 cursor-pointer"
            onClick={() => setShowNewPassword(!showNewPassword)}
          >
            {showNewPassword ? <VisibilityOffOutlinedIcon style={{ color: 'grey' }} /> : <RemoveRedEyeOutlinedIcon style={{ color: 'grey' }} />}
          </span>
          {errors.newPassword && (
            <p className="mt-1 text-sm text-red-500">
              {errors.newPassword?.message as string}
            </p>
          )}
        </div>

        {/* Confirm New Password */}
        <div className="mb-4 flex flex-shrink flex-col relative">
          <label className="text-lg font-medium text-[#484848]">Retype New Password</label>
          <input
            type={showConfirmPassword ? "text" : "password"} // Toggle between text and password
            {...register("confirmPassword")}
            className="h-[37px] w-[434px] rounded-md border border-[#5B5B5B] p-2"
          />
          {/* Eye icon to toggle visibility */}
          <span
            className="absolute right-4 top-8 cursor-pointer"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
          >
            {showConfirmPassword ? <VisibilityOffOutlinedIcon style={{ color: 'grey' }} /> : <RemoveRedEyeOutlinedIcon style={{ color: 'grey' }} />}
          </span>
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-red-500">
              {errors.confirmPassword?.message as string}
            </p>
          )}
        </div>
      </div>

      {/* Buttons */}
      <div className="flex space-x-4">
        <button
          type="submit"
          className="h-[41px] w-[215px] rounded bg-gradient-to-tr from-[#2B3D59] to-[#375685] text-center text-xl font-medium text-white hover:from-[#375685] hover:to-[#2B3D59]"
        >
          Save Changes
        </button>
        <button
          type="button"
          className="h-[41px] w-[215px] rounded bg-[#E6E6E6] text-center text-xl font-medium text-[#696969]"
        >
          Discard
        </button>
      </div>
    </form>
  );
};

export default PasswordChangeForm;
