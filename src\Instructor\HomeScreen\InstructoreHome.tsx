import React from 'react';
import Card from './HomeCard';

const Dashboard: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
      <Card 
        title="Course Management" 
        buttonText="Start" 
        imageSrc="/course-management-cartoon.png"
        link="/course-management" 
      />
      <Card 
        title="Learner Progress" 
        buttonText="Learning" 
        imageSrc="/learner-progress-cartoon.png"
        link="/learner-progress"
      />
      <Card 
        title="Feedback & Questions" 
        buttonText="Go" 
        imageSrc="/feedback-cartoon.png"
        link="/feedback-&-questions"
      />
      <Card 
        title="Analytics" 
        buttonText="Analytics" 
        imageSrc="/analytics-cartoon.png"
        link="/analytics/overview"
      />
    </div>
  );
};

export default Dashboard;
