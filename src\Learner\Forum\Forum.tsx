import React from 'react';

const Forum: React.FC = () => {
  return (
    <div className="bg-yellow-50 p-6 rounded-lg shadow-md space-y-6">
      <div className="bg-white p-4 rounded-lg shadow">
        <ul className="flex space-x-4 mb-4">
          <li className="font-semibold border-b-2 border-gray-900 pb-1">Post</li>
        </ul>
        <div className="mb-4">
          <div className="border rounded-lg p-2 bg-gray-100">
            <div contentEditable className="w-full h-24 bg-white p-2 border border-gray-300 rounded"></div>
          </div>
        </div>
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2">Topic</label>
          <input type="text" className="w-full p-2 border border-gray-300 rounded mb-2" placeholder="Add Topic" />
          <p className="text-gray-700">To My Followers</p>
        </div>
        <button className="bg-yellow-500 text-white py-2 px-4 rounded-md">Share</button>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <ul className="flex space-x-4 mb-4">
          <li className="font-semibold">Featured</li>
          <li className="font-semibold border-b-2 border-gray-900 pb-1">Discussions</li>
          <li className="font-semibold">My Feed</li>
        </ul>
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2">Sort By:</label>
          <select className="w-full p-2 border border-gray-300 rounded">
            <option>Latest Posts</option>
          </select>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg shadow mb-4">
          <p className="text-blue-600 font-semibold mb-2">Hi, I need help to create a cybersecurity portfolio please.</p>
          <p className="text-gray-600">New - Mhfila - 42m ago</p>
        </div>
        <div className="flex justify-between text-gray-600">
          <div className="flex space-x-2">
            <span>👍 7.7k</span>
            <span>💬 144</span>
            <span>🔄 Share</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Forum;
