export interface Student {
  id: string;
  name: string;
  cohort: string;
  course: string;
  organization: string;
}

export interface Activity {
  id: string;
  type: string;
  number: number;
  course: string;
  maxScore: number;
}

export interface ActivityResult {
  studentId: string;
  studentName: string;
  cohort: string;
  course: string;
  activityType: string;
  activityNumber: number;
  score: number;
  maxScore: number;
  timeSpent: number;
  completedAt: string;
  organization?: string;
}

export interface CohortSummary {
  cohort: string;
  course: string;
  totalStudents: number;
  averageScore: number;
  completionRate: number;
  activities: {
    [key: string]: {
      avg: number;
      completed: number;
    };
  };
}

export interface ActivitySummary {
  activityType: string;
  activityNumber: number;
  course: string;
  totalAttempts: number;
  averageScore: number;
  averageTimeSpent: number;
  completionRate: number;
}

export interface CourseSummary {
  course: string;
  totalActivities: number;
  activityTypes: string[];
  totalStudents: number;
  totalCohorts: number;
}

export interface OrganizationSummary {
  organization: string;
  totalStudents: number;
  totalCohorts: number;
  averageScore: number;
  completionRate: number;
  topPerformingCohort: string;
}

export interface ScoreDistribution {
  range: string;
  count: number;
  percentage: number;
}

export interface TimeTrendData {
  date: string;
  [activityType: string]: string | number;
}

export interface BellCurvePoint {
  score: number;
  frequency: number;
  actualCount: number;
}

export interface ChartDataPoint {
  name: string;
  value: number;
  [key: string]: any;
}