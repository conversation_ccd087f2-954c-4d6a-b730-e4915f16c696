import {
  BarChart<PERSON>,
  Calendar,
  ClipboardList,
  Target,
  User<PERSON><PERSON><PERSON>,
  Users,
} from "lucide-react";
import { useState } from "react";
import { ActivityView } from "./components/ActivityView";
import { CohortSpecificView } from "./components/CohortSpecificView";
import { IndividualView } from "./components/IndividualView";
import { OverallView } from "./components/OverallView";
import { RubricView } from "./components/RubricView";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./components/ui/tabs";

export default function App() {
  const [activeTab, setActiveTab] = useState("overall");
  const [selectedCourse, setSelectedCourse] = useState("Hospitality");
  const [selectedTimePeriod, setSelectedTimePeriod] = useState("last-3-months");

  const timePeriods = [
    { value: "last-3-months", label: "Last 3 Months" },
    { value: "3-6-months", label: "3-6 Months Ago" },
    { value: "6-9-months", label: "6-9 Months Ago" },
    { value: "9-12-months", label: "9-12 Months Ago" },
    { value: "all-time", label: "All Time" },
  ];

  return (
    <div className="bg-background min-h-screen">
      {/* Header */}
      <div className="bg-card border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <BarChart3 className="text-primary h-8 w-8" />
              <div>
                <h1 className="text-2xl font-bold">Results Dashboard</h1>
                <p className="text-muted-foreground text-sm">
                  View and analyze performance across cohorts, activities, and
                  individual students by course
                </p>
              </div>
            </div>

            {/* Time Period Selector */}
            <div className="flex items-center space-x-2">
              <Calendar className="text-primary h-5 w-5" />
              <div className="flex flex-col">
                <span className="text-muted-foreground text-sm">
                  Time Period
                </span>
                <select
                  value={selectedTimePeriod}
                  onChange={(e) => setSelectedTimePeriod(e.target.value)}
                  className="bg-background min-w-[180px] rounded-md border px-3 py-2 text-sm"
                >
                  {timePeriods.map((period) => (
                    <option key={period.value} value={period.value}>
                      {period.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-6">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger
              value="overall"
              className="flex items-center space-x-2"
            >
              <Users className="h-4 w-4" />
              <span>Overall View</span>
            </TabsTrigger>
            <TabsTrigger value="cohort" className="flex items-center space-x-2">
              <Target className="h-4 w-4" />
              <span>Cohort View</span>
            </TabsTrigger>
            <TabsTrigger
              value="activity"
              className="flex items-center space-x-2"
            >
              <BarChart3 className="h-4 w-4" />
              <span>Activity View</span>
            </TabsTrigger>
            <TabsTrigger value="rubric" className="flex items-center space-x-2">
              <ClipboardList className="h-4 w-4" />
              <span>Rubric View</span>
            </TabsTrigger>
            <TabsTrigger
              value="individual"
              className="flex items-center space-x-2"
            >
              <UserCheck className="h-4 w-4" />
              <span>Individual Results</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overall" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Overall Analysis</h2>
                <p className="text-muted-foreground text-sm">
                  Compare performance across different cohorts and get an
                  overall view of course progress
                </p>
              </div>
            </div>
            <OverallView
              selectedCourse={selectedCourse}
              onCourseChange={setSelectedCourse}
              selectedTimePeriod={selectedTimePeriod}
            />
          </TabsContent>

          <TabsContent value="cohort" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Cohort Analysis</h2>
                <p className="text-muted-foreground text-sm">
                  Deep dive into specific cohort performance with activity-level
                  statistical analysis
                </p>
              </div>
            </div>
            <CohortSpecificView
              selectedCourse={selectedCourse}
              onCourseChange={setSelectedCourse}
              selectedTimePeriod={selectedTimePeriod}
            />
          </TabsContent>

          <TabsContent value="activity" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Activity Analysis</h2>
                <p className="text-muted-foreground text-sm">
                  Analyze performance across course activities with statistical
                  distributions
                </p>
              </div>
            </div>
            <ActivityView
              selectedCourse={selectedCourse}
              onCourseChange={setSelectedCourse}
              selectedTimePeriod={selectedTimePeriod}
            />
          </TabsContent>

          <TabsContent value="rubric" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Rubric Analysis</h2>
                <p className="text-muted-foreground text-sm">
                  Analyze performance by criteria and parameters across
                  activities and cohorts
                </p>
              </div>
            </div>
            <RubricView
              selectedCourse={selectedCourse}
              onCourseChange={setSelectedCourse}
              selectedTimePeriod={selectedTimePeriod}
            />
          </TabsContent>

          <TabsContent value="individual" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Individual Results</h2>
                <p className="text-muted-foreground text-sm">
                  Detailed view of individual student performance with
                  statistical analysis
                </p>
              </div>
            </div>
            <IndividualView
              selectedCourse={selectedCourse}
              onCourseChange={setSelectedCourse}
              selectedTimePeriod={selectedTimePeriod}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Footer */}
      <div className="bg-card mt-12 border-t">
        <div className="container mx-auto px-6 py-4">
          <div className="text-muted-foreground flex items-center justify-between text-sm">
            <p>
              Results Dashboard - Track and analyze learning outcomes by course
            </p>
            <p>Last updated: June 23, 2025</p>
          </div>
        </div>
      </div>
    </div>
  );
}
