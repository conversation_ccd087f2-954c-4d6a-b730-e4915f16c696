import { MenuItem, Select, SelectChangeEvent } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useGetCohorts } from "../hooks/useGetCohorts";
import { useGetAllCourses } from "../hooks/useGetAllCourses";
// import { useGetCohortUsers } from '../hooks/useGetCohortUsers';
import { useQueryClient } from "@tanstack/react-query";
import UserTable from "../../Admin/GroupManagement/PaginatedUsersTable";
import Spinner from "../../Learner/components/Spinner";
import showError from "../../Learner/scripts/showErrorDialog";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import { useEnrollUsers } from "../hooks/useEnrollUsers";

const EnrollmentTable: React.FC = () => {
  const queryClient = useQueryClient();
  const { data: cohorts, isFetching: isFetchingCohorts } = useGetCohorts();
  const { data: courses, isFetching: isFetchingCourses } = useGetAllCourses();
  const [cohort, setCohort] = useState<string>("");
  const [course, setCourse] = useState<string>("");
  // const { data: userData, isFetching: isFetchingUsers } = useGetCohortUsers(cohort);
  const mutation = useEnrollUsers();

  const handleCohortSelect = (event: SelectChangeEvent) => {
    console.log(event.target.value);
    setCohort(event.target.value);
  };

  const handleCourseSelect = (event: SelectChangeEvent) => {
    console.log(event.target.value);
    setCourse(event.target.value);
  };

  useEffect(() => {
    if (cohort) {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  }, [cohort, queryClient]);

  useEffect(() => {
    if (cohorts && cohorts.length > 0) {
      setCohort(cohorts[0].id.toLocaleString());
    }
  }, [cohorts]);

  useEffect(() => {
    if (courses && courses.length > 0) {
      setCourse(courses[0].id.toLocaleString());
    }
  }, [courses]);

  const handleEnrollUsers = () => {
    console.log(cohort, course);
    if (cohort && course) {
      mutation.mutate(
        {
          cohortId: cohort,
          courseId: course,
        },
        {
          onSuccess: () => {
            showSuccess(
              "Success",
              "All users of selected cohort are enrolled successfully!",
            );
          },
          onError: () => {
            showError("Submission failed", "Please try again.");
            console.log("Submission failed. Please try again.");
          },
        },
      );
    }
  };

  return (
    <>
      <Spinner
        loading={isFetchingCohorts || isFetchingCourses || mutation.isPending}
      />
      <div className="rounded-lg bg-white p-4 shadow-md">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center">
            <div className="mr-6">
              <label htmlFor="cohort-select" className="mr-2">
                Select Cohort:
              </label>
              {cohorts && cohorts.length > 0 ? (
                <Select
                  id="cohort-select"
                  value={cohort}
                  onChange={handleCohortSelect}
                >
                  {cohorts.map((c) => (
                    <MenuItem key={c.id} value={c.id}>
                      {c.name}
                    </MenuItem>
                  ))}
                </Select>
              ) : null}
            </div>
            <div>
              <label htmlFor="course-select" className="mr-2">
                Select Course:
              </label>
              {courses && courses.length > 0 ? (
                <Select
                  id="course-select"
                  value={course}
                  onChange={handleCourseSelect}
                >
                  {courses.map((c) => (
                    <MenuItem key={c.id} value={c.id}>
                      {c.name}
                    </MenuItem>
                  ))}
                </Select>
              ) : null}
            </div>
          </div>
        </div>
        {cohort ? <UserTable cohortId={parseInt(cohort)} /> : null}
        <div className="mt-4 flex justify-center">
          <button
            onClick={handleEnrollUsers}
            className="mx-2 h-[40px] w-[195px] rounded-lg bg-[#36537F] px-5 text-center text-lg font-medium text-white hover:bg-blue-400"
          >
            Enroll
          </button>
        </div>
      </div>
    </>
  );
};

export default EnrollmentTable;
