import React from "react";
import styled from "styled-components";

interface GradientButtonProps {
  text: string;
  type?: "button" | "submit" | "reset";
  color1: string;
  color2: string;
  width?: string;
  textColor?: string;
  disabled?: boolean;
  onClick?: () => void; // Optional onClick handler
}

const defaultWidth = 'None';
const defaultTextColor = 'white';
const Button = styled.button<{ color1: string; color2: string, width: string; textcolor: string; }>`
  background: linear-gradient(
    45deg,
    ${(props) => props.color1},
    ${(props) => props.color2}
  );
  width: ${props => props.width || defaultWidth};
  border: none;
  border-radius: 5px;
  color: ${props => props.textcolor || defaultTextColor};;
  margin-top: 10px;
  margin-bottom: 10px;
  margin-right: 10px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;

  &:hover {
    background: linear-gradient(
      45deg,
      ${(props) => props.color2},
      ${(props) => props.color1}
    );
  }
  
  &:disabled {
    background: linear-gradient(
      45deg,
      ${(props) => props.color1},
      ${(props) => props.color2}
    );
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const GradientButton: React.FC<GradientButtonProps> = ({
  text,
  type = 'button',
  color1,
  color2,
  width = defaultWidth,
  textColor = defaultTextColor,
  disabled = false,
  onClick,
}) => {
  return (
    <Button color1={color1} color2={color2} type={type} width={width} disabled={disabled} textcolor={textColor} onClick={onClick}>
      {text}
    </Button>
  );
};

export default GradientButton;
