import React from 'react';
import GradientButton from '../../Learner/components/GradientButton';

interface NameVerificationProps {
  name: string
}
const NameVerification: React.FC<NameVerificationProps> = ({name}) => {
  return (
    <div className="p-6 bg-yellow-50">
      <h2 className="text-lg font-bold text-blue-800 mb-4">Name Verification</h2>
      <p className="mb-4">Your name {name} is verified. This is the name that will appear on your certificates.</p>
      <GradientButton text="Request Name Change" color1="#2B3D59" color2="#375685" width='250px' />
    </div>
  );
};

export default NameVerification;
