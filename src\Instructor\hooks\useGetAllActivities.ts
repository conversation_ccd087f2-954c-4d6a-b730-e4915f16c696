import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { ActivityName } from "../../types/Activity";
import { AxiosInstance } from "axios";

// api call

const getActivity = async (
  axiosObject: AxiosInstance,
): Promise<ActivityName[]> => {
  const activities = await axiosObject.get<ActivityName[]>(
    "/api/v1/activity/get/all",
  );
  console.log(activities);
  return activities.data;
};

export function useGetAllActivities() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<ActivityName[]>({
    queryKey: ["activities"],
    refetchOnWindowFocus: false,
    queryFn: () => getActivity(axiosObject),
  });
}
