# # Stage 1: Build
# FROM node:18 AS build

# # Set the working directory
# WORKDIR /app

# # Copy package.json and package-lock.json
# COPY package*.json ./

# # Install dependencies
# RUN npm install

# # Copy the rest of the application code
# COPY . .

# # Build the application
# RUN npm run build

# # Stage 2: Serve
# FROM nginx:stable-alpine

# # Remove default Nginx website
# RUN rm -rf /usr/share/nginx/html/*

# # Copy build output to Nginx's HTML directory
# COPY --from=build /app/dist /usr/share/nginx/html

# # Copy custom Nginx configuration
# # COPY nginx.conf /etc/nginx/nginx.conf

# # Expose port 80
# EXPOSE 80

# # Start Nginx
# CMD ["nginx", "-g", "daemon off;"]

# This is the newer version

FROM node:18-alpine as builder

WORKDIR /app
COPY . .
ENV NODE_OPTIONS=--max-old-space-size=2048
RUN ["npm", "i"]
RUN ["npm", "run", "build"]

FROM nginx:alpine

WORKDIR /usr/share/nginx/

RUN rm -rf html
RUN mkdir html

WORKDIR /

COPY ./nginx.conf /etc/nginx
COPY --from=builder ./app/dist /usr/share/nginx/html

ENTRYPOINT ["nginx", "-g", "daemon off;"]
