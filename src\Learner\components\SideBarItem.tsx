import { useState } from "react";
import { SideBarItemInterface } from "../../types/SideBarItemInterface";
import { useNavigate } from "react-router-dom";

interface Props {
  sideBarItem: SideBarItemInterface;
}

function SideBarItem({ sideBarItem }: Props) {
  const selected = sideBarItem.index === sideBarItem.selectedIndex;
  const [expanded, setExpanded] = useState<boolean>(false);
  const navigate = useNavigate();

  const getColor = (link: string, completed?: boolean) => {
    if (window.location.href.includes(link)) return "text-[#D0B120]";
    return completed ? "text-[#A3A3A3]" : "text-[#000000]";
  };

  return (
    <div className="flex flex-col items-center gap-6 pb-3 pt-5">
      <div
        role="button"
        className={
          "flex h-10 w-3/4 items-center justify-center rounded-lg " +
          (selected ? "bg-[#145DA0]" : "")
        }
      >
        <img className="mr-3 h-4 w-4" src={sideBarItem.iconPath}></img>
        <h1
          className={
            "text-l w-3/4 " + (selected ? "text-white" : "text-[#A3A3A3]")
          }
          onClick={() => {
            if (sideBarItem.children?.length! > 0) {
              setExpanded(!expanded);
            }

            sideBarItem.onClickHandle(sideBarItem.index);
          }}
        >
          {sideBarItem.heading}
        </h1>
      </div>
      {selected && expanded && (
        <div className="mb-6 ml-3 flex w-3/4 flex-col gap-2 rounded-lg">
          <h1 className="text-lg font-bold">
            {sideBarItem.children?.[0].course_name}
          </h1>
          <ul role="button" className="items-start space-y-3 text-lg">
            {sideBarItem.children?.map((child, idx) => {
              return (
                <li
                  className={getColor(child.link, child.completed)}
                  onClick={() => navigate(child.link)}
                  // onClick={() => (child.completed ? null : navigate(child.link))}
                  // onClick={() => child.completed? navigate(child.link): navigate(child.link)}
                  key={child.name + idx}
                >
                  {idx + 1 + ". " + child.name}
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
}

export default SideBarItem;
