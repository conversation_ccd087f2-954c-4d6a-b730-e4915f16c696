import React, { useState, useEffect } from "react";
import { useSearchUsers } from "../hooks/useSearchUser";
import { debounce } from "lodash";
import { UserSearchResponse } from "../../types/User";

interface UserSearchProps {
  onSelect: (user: UserSearchResponse) => void;
}

const UserSearch: React.FC<UserSearchProps> = ({ onSelect }) => {
  const [query, setQuery] = useState<string>("");
  const [queryVAl, setQueryVal] = useState<string>("");
  const [showDropdown, setShowDropdown] = useState<boolean>(false);

  // Debounce the query to avoid too many API calls
  const debouncedQuery = debounce((value: string) => {
    console.log(value);
    setQuery(value);
    setShowDropdown(true);
  }, 300); // Adjust the debounce delay as needed

  // TanStack Query for fetching users based on the query
  const { data: users, isLoading, error } = useSearchUsers(query);

  // Event handler for input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    debouncedQuery(inputValue);
  };

  const handleSelectUser = (user: UserSearchResponse) => {
    onSelect(user); // Call the parent callback
    setShowDropdown(false);
  };

  return (
    <div className="my-2">
      <input
        type="text"
        className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
        placeholder="Search by email"
        // value={query}
        onChange={handleInputChange}
      />

      {/* Handle loading state */}
      {isLoading && <p>Loading...</p>}

      {/* Handle error state */}
      {error && <p>Error fetching users: {error.message}</p>}

      {/* Display the list of user emails */}
      {showDropdown && users && users.length > 0 && (
        <ul className="absolute w-full bg-white border border-gray-200 rounded-md shadow-lg mt-1 max-h-60 overflow-auto z-10">
          {users.map((user) => (
            <li
              key={user.id}
              onClick={() => handleSelectUser(user)}
              className="p-2 cursor-pointer hover:bg-gray-100"
            >
              {user.email}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default UserSearch;
