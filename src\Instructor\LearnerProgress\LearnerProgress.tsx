import React from "react";

const LearnerProgressTable: React.FC = () => {
  return (
    <div className="rounded-lg bg-white p-4 shadow-md">
      <table className="w-full table-auto text-left">
        <thead>
          <tr className="bg-gray-100">
            <th className="p-2">Course</th>
            <th className="p-2">Metric</th>
            <th className="p-2">Grade</th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b">
            <td className="p-2"><PERSON></td>
            <td className="p-2">Average Score of First Try</td>
            <td className="p-2">78%</td>
          </tr>
          <tr className="border-b">
            <td className="p-2"></td>
            <td className="p-2">Average Score of Second Try</td>
            <td className="p-2">82%</td>
          </tr>
          <tr className="border-b">
            <td className="p-2"></td>
            <td className="p-2">Difference Between Tries</td>
            <td className="p-2">4% Increase</td>
          </tr>
          <tr className="bg-[#36537F] font-bold text-white">
            <td className="p-2"></td>
            <td className="p-2">
              No of Users who scored more than 70% in first try
            </td>
            <td className="p-2">45</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default LearnerProgressTable;
