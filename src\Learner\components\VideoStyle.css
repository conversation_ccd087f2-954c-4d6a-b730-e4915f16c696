.hide-seekbar::-webkit-media-controls-timeline,
.hide-seekbar::-webkit-media-controls-current-time-display,
.hide-seekbar::-webkit-media-controls-time-remaining-display,
.hide-seekbar::-webkit-media-controls-seek-back-button,
.hide-seekbar::-webkit-media-controls-seek-forward-button {
  display: none;
}

.hide-seekbar::-moz-media-controls-timeline,
.hide-seekbar::-moz-media-controls-current-time-display,
.hide-seekbar::-moz-media-controls-time-remaining-display,
.hide-seekbar::-moz-media-controls-seek-back-button,
.hide-seekbar::-moz-media-controls-seek-forward-button {
  display: none;
}

.hide-seekbar::-ms-media-controls-timeline,
.hide-seekbar::-ms-media-controls-current-time-display,
.hide-seekbar::-ms-media-controls-time-remaining-display,
.hide-seekbar::-ms-media-controls-seek-back-button,
.hide-seekbar::-ms-media-controls-seek-forward-button {
  display: none;
}

.hide-seekbar::media-controls-timeline-container {
  display: none !important;
}

/* .hide-seekbar-tutorial::-webkit-media-controls-timeline, */
/* .hide-seekbar-tutorial::-webkit-media-controls-current-time-display, */
/* .hide-seekbar-tutorial::-webkit-media-controls-time-remaining-display, */
.hide-seekbar-tutorial::-webkit-media-controls-seek-back-button,
.hide-seekbar-tutorial::-webkit-media-controls-seek-forward-button {
  display: none;
}

/* .hide-seekbar-tutorial::-moz-media-controls-timeline, */
/* .hide-seekbar-tutorial::-moz-media-controls-current-time-display, */
/* .hide-seekbar-tutorial::-moz-media-controls-time-remaining-display, */
.hide-seekbar-tutorial::-moz-media-controls-seek-back-button,
.hide-seekbar-tutorial::-moz-media-controls-seek-forward-button {
  display: none;
}

/* .hide-seekba-tutorialr::-ms-media-controls-timeline, */
/* .hide-seekbar-tutorial::-ms-media-controls-current-time-display, */
/* .hide-seekbar-tutorial::-ms-media-controls-time-remaining-display, */
.hide-seekbar-tutorial::-ms-media-controls-seek-back-button,
.hide-seekbar-tutorial::-ms-media-controls-seek-forward-button {
  display: none;
}

.hide-seekbar-tutorial::media-controls-timeline-container {
  display: none !important;
}
