import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { CourseUpdateResponse } from "../../types/Course";
import { AxiosInstance } from "axios";

// api call
const createCourse = async (
  axiosObject: AxiosInstance,
  name: string,
  activities: number[] = []
): Promise<CourseUpdateResponse> => {
  const data = {
    name: name,
    activities: activities,
  }
  const response = await axiosObject.post<CourseUpdateResponse>(
    "/api/v1/course/create", data
  );
  console.log(response);
  return response.data;
};

export function useCreateCourse() {
  const axiosInstance = useAxios();
  // run the query
  return useMutation({
    mutationFn: ({ name, activities = [] }: {
      name: string, activities?: number[]
    }) => {
      return createCourse(axiosInstance, name, activities);
    },
  })
}
