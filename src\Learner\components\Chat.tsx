import React from "react";
import { Message } from "../../types/Message";
import ChatContent from "./ChatContent";
import ChatInputBox from "./ChatInputBox";
import { useGetMessages } from "../hooks/useGetMessages";
import { usePostMessage } from "../hooks/useMessage";
import { useLocation } from "react-router-dom";

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

const Chat = () => {
  /** Simulate a hook fetching the data */
  const mutation = usePostMessage();
  const {
    messages: { data },
  } = useGetMessages();
  const query = useQueryParam();
  const courseId = query.get("course_id");
  const activityId = query.get("activity_id");

  /** State to control new messages */
  const [chatMessages, setChatMessages] = React.useState<Message[]>(data);
  const [conversationID, setconversationID] = React.useState<string | null>(null);

  /**
   *
   * @param message
   * "Create" a new message
   */
  const sendANewMessage = (message: Message) => {
    setChatMessages((prevMessages) => [...prevMessages, message]);
    mutation.mutate({ conversationId: conversationID, courseId: courseId, 
      activityId: activityId, message: message.message },
          {
            onSuccess: (data) => {
              if (data) {
                setChatMessages((prevMessages) => [...prevMessages, data.chatbot_response]);
                setconversationID(data.id.toString())
              }
            },
            onError: () => {
              console.log('Submission failed. Please try again.');
            }
          }
        )
  };

  return (
    <div className="flex flex-col rounded-lg border border-gray-200 bg-white shadow">
      <ChatContent messages={chatMessages} isLoading={mutation.isPending} />
      <ChatInputBox sendANewMessage={sendANewMessage} />
    </div>
  );
};

export default Chat;
