import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { ActivityUpdateResponse } from "../../types/Activity";
import { AxiosInstance } from "axios";

// api call

const updateActivity = async (
  axiosObject: AxiosInstance,
  activityId: string,
  name: string,
  instructions: string,
  instructions_below: string,
  instructions_cp: string,
  instructions_cp_below: string,
  instructions_rec: string,
  instructions_rec_below: string,
  instructions_wr: string,
  instructions_wr_below: string,
  videoTitle: string,
  videoFile: Blob | null,
  mcqs: string,
  questions: string,
  secondVideoFile: Blob | null,
  mandatory: string,
  fileLink: string,
  pdfFile: Blob | null,
  showCorrectOption: string,
  extra_fields: string,
  practiceActivityData: string,
): Promise<ActivityUpdateResponse> => {
  let formData = new FormData();
  formData.append("name", name);
  if (mcqs) {
    formData.append("mcqs", mcqs);
  }
  if (instructions) {
    formData.append("instructions", instructions);
  }
  if (instructions_below) {
    formData.append("instructions_below", instructions_below);
  }
  if (instructions_cp) {
    formData.append("instructions_cp", instructions_cp);
  }
  if (instructions_cp_below) {
    formData.append("instructions_cp_below", instructions_cp_below);
  }
  if (instructions_rec) {
    formData.append("instructions_rec", instructions_rec);
  }
  if (instructions_rec_below) {
    formData.append("instructions_rec_below", instructions_rec_below);
  }
  if (instructions_wr) {
    formData.append("instructions_wr", instructions_wr);
  }
  if (instructions_wr_below) {
    formData.append("instructions_wr_below", instructions_wr_below);
  }
  if (questions) {
    formData.append("questions", questions);
  }
  if (videoTitle) {
    formData.append("video_title", videoTitle);
  }
  if (videoFile) {
    formData.append("video_file", videoFile);
  }
  if (secondVideoFile) {
    formData.append("second_video", secondVideoFile);
  }
  if (mandatory) {
    formData.append("mandatory", mandatory);
  }
  if (showCorrectOption) {
    formData.append("show_correct_option", showCorrectOption);
  }
  if (fileLink) {
    formData.append("add_resources_file_link", fileLink);
  }
  if (pdfFile) {
    formData.append("add_resources_file", pdfFile);
  }
  if (extra_fields) {
    formData.append("extra_fields", extra_fields);
  }
  if (practiceActivityData) {
    formData.append("practiceActivityData", practiceActivityData);
  }
  const activity = await axiosObject.patch<ActivityUpdateResponse>(
    "/api/v1/activity/update/" + activityId,
    formData,
  );
  console.log(activity);
  return activity.data;
};

export function useUpdateActivity() {
  const axiosInstance = useAxios();
  // run the query
  return useMutation({
    mutationFn: ({
      activityId,
      name = "",
      instructions = "",
      instructions_below = "",
      instructions_cp = "",
      instructions_cp_below = "",
      instructions_rec = "",
      instructions_rec_below = "",
      instructions_wr = "",
      instructions_wr_below = "",
      videoTitle = "",
      videoFile = null,
      mcqs = "",
      questions = "",
      secondVideoFile = null,
      mandatory = "",
      fileLink = "",
      pdfFile = null,
      showCorrectOption = "",
      extra_fields = "",
      practiceActivityData = "",
    }: {
      activityId: string;
      name?: string;
      instructions?: string;
      instructions_below?: string;
      instructions_cp?: string;
      instructions_cp_below?: string;
      instructions_rec?: string;
      instructions_rec_below?: string;
      instructions_wr?: string;
      instructions_wr_below?: string;
      videoTitle?: string;
      videoFile?: Blob | null;
      mcqs?: string;
      questions?: string;
      secondVideoFile?: Blob | null;
      mandatory?: string;
      fileLink?: string;
      pdfFile?: Blob | null;
      showCorrectOption?: string;
      extra_fields?: string;
      practiceActivityData?: string;
    }) => {
      return updateActivity(
        axiosInstance,
        activityId,
        name,
        instructions,
        instructions_below,
        instructions_cp,
        instructions_cp_below,
        instructions_rec,
        instructions_rec_below,
        instructions_wr,
        instructions_wr_below,
        videoTitle,
        videoFile,
        mcqs,
        questions,
        secondVideoFile,
        mandatory,
        fileLink,
        pdfFile,
        showCorrectOption,
        extra_fields,
        practiceActivityData,
      );
    },
  });
}
