import { Pie<PERSON><PERSON>, pieArcLabelClasses } from "@mui/x-charts";
import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useReactMediaRecorder } from "react-media-recorder";
import GradientButton from "../components/GradientButton";
import NextButton from "../components/NextButton";
// import OrSeparator from "../components/OrSeparator";
// import PieChartSkeleton from "../components/PieChartSkeleton";
// import ReportElement from "../components/ReportElement";
import Spinner from "../components/Spinner";
// import VideoDropzone from "../components/VideoDropzone";
import RedButton from "../components/RedButton";
import showError from "../scripts/showErrorDialog";
import { usePostActivityProgress } from "../hooks/usePostActivityProgress";
import { useGetActivity } from "../hooks/useGetActivity";
import { useQueryClient } from "@tanstack/react-query";
// import { Label } from "@mui/icons-material";
import "../components/VideoStyle.css";
import ListeningComponent from "./ListeningComponent";
import RespondingComponent from "./RespondingComponent";

interface Data {
  value: number;
  label: string;
}

interface Size {
  width: number;
  height: number;
}

interface PieChartComponentProps {
  data: Data[];
  size: Size;
}

interface SentimentScores {
  Angry: number;
  Happy: number;
  Sad: number;
  Neutral: number;
}

interface SentimentClassification {
  Audio: string;
  Video: string;
  Text: string;
}

interface SentimentScoresIndependent {
  Audio: SentimentScores;
  Video: SentimentScores;
  Text: SentimentScores;
}

interface ReasonsIndependent {
  Audio: string;
  Video: string;
  Text: string;
}

interface ResponseData {
  facial_expressions: number;
  eye_contact: number;
  body_movement_and_posture: number;
  gestures: number;
  tone_and_manner_of_speech: number;
  choice_of_words: number;
}

interface CombinedSentimentAnalysis {
  "Combined Sentiment": string;
  "Comparison and Discrepancy": string;
  Summary: string;
}

interface ProfessionalConductEvaluation {
  "Conduct Assessment": string;
  "Examples of Unprofessional Conduct": string;
  Recommendations: string;
}

interface Response {
  "Sentiment Classification": SentimentClassification;
  "Sentiment Scores": SentimentScoresIndependent;
  Reasons: ReasonsIndependent;
  "Combined Sentiment Analysis": CombinedSentimentAnalysis;
  "Professional Conduct Evaluation": ProfessionalConductEvaluation;
}

const PieChartComponent: React.FC<PieChartComponentProps> = ({
  data,
  size,
}) => {
  return (
    <PieChart
      margin={{ top: 10, bottom: 30, left: 10, right: 10 }}
      series={[
        {
          arcLabel: (item) => `${item.label} (${item.value})`,
          arcLabelMinAngle: 45,
          data,
        },
      ]}
      sx={{
        [`& .${pieArcLabelClasses.root}`]: {
          fill: "white",
          fontWeight: "bold",
        },
      }}
      slotProps={{
        legend: {
          direction: "row",
          position: { vertical: "bottom", horizontal: "middle" },
          itemMarkWidth: 15,
          itemMarkHeight: 15,
          markGap: 5,
          itemGap: 10,
          padding: 0,
          labelStyle: {
            fontSize: 10,
          },
        },
      }}
      {...size}
    />
  );
};

const size: Size = {
  width: 250,
  height: 250,
};

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

const ActivityContainer: React.FC = () => {
  const queryClient = useQueryClient();
  const query = useQueryParam();
  const course_id = query.get("course_id") ?? "";
  const activity_id = query.get("activity_id") ?? "";
  const [watchTutorial, setWatchTutorial] = useState(false);
  const [tutorialRecording, setTutorialRecording] = useState(false);
  const [watchVideo, setWatchVideo] = useState(false);
  const [error, setError] = useState(false);
  const [recording, setRecording] = useState(false);
  const [cameraStatus, setcameraStatus] = useState<string>("not");
  const navigate = useNavigate();

  const redirectToLink = (nextActivityLink: string) => {
    navigate("/my-learning" + nextActivityLink);
  };

  const isBossChallenge = window.location.href.includes("boss");

  const [response, setResponse] = useState<boolean>(false);
  const [clasification, setClassification] = useState<
    SentimentClassification | object
  >({});
  const [sentimentDataObject, setSentimentDataObject] = useState<object>({});
  const [reasons, setReasons] = useState<ReasonsIndependent | object>({});
  const [conmbinedSentiment, setCombinedSentiment] = useState<
    CombinedSentimentAnalysis | object
  >({});
  const [professionalConduct, setProfessionalConduct] = useState<
    ProfessionalConductEvaluation | object
  >({});
  const [respondingData, setRespondingData] = useState<ResponseData | null>(
    null,
  );
  const [listeningData, setListeningData] = useState<ResponseData | null>(null);
  const [youDidWellAtListening, setyouDidWellAtListening] = useState<string[]>(
    [],
  );
  const [youDidWellAtResponding, setyouDidWellAtResponding] = useState<
    string[]
  >([]);
  const [youCanImproveOnListening, setyouCanImproveOnListening] = useState<
    string[]
  >([]);
  const [youCanImproveOnResponding, setyouCanImproveOnResponding] = useState<
    string[]
  >([]);
  const [nextActivityLink, setNextActivityLink] = useState("");
  const [heygenVideoId, setHeygenVideoId] = useState<number | undefined>(
    undefined,
  );
  const [heygenVideoUrl, setHeygenVideoUrl] = useState<string>("");
  const {
    data: activity,
    isLoading,
    isFetching,
    isError,
  } = useGetActivity(activity_id);
  const mutation = usePostActivityProgress("listening");
  const mutationResponding = usePostActivityProgress("responding");

  // async function handleDrop(acceptedFiles: File[]) {
  //   setWatchVideo(true);
  //   mutation.mutate(
  //     {
  //       courseId: course_id,
  //       activityId: activity_id,
  //       userInput: "",
  //       video: acceptedFiles[0],
  //     },
  //     {
  //       onSuccess: (data) => {
  //         if (data) {
  //           if (data.next_activity_link) {
  //             setNextActivityLink(data.next_activity_link);
  //           }
  //           if (data.result) {
  //             setResponse(true);
  //             loadResponse(data.result);
  //           }
  //         }
  //       },
  //       onError: () => {
  //         setError(true);
  //         showError("Submission failed", "Please try again.");
  //         console.log("Submission failed. Please try again.");
  //       },
  //     },
  //   );
  // }

  // useEffect(() => {
  //   setWatchVideo(false);
  //   setError(false);
  //   setResponse(false);
  // }, []);

  const handleUpload = async (videoBlob: Blob) => {
    console.log("uploading blob");
    if (!watchTutorial) {
      setWatchTutorial(!watchTutorial);
      mutation.mutate(
        {
          courseId: course_id,
          activityId: activity_id,
          userInput: "",
          video: videoBlob,
        },
        {
          onSuccess: (data) => {
            if (data) {
              if (data.next_activity_link) {
                setNextActivityLink(data.next_activity_link);
              }
              if (data.result) {
                // setResponse(true);
                loadListeningData(data.result);
              }
            }
          },
          onError: () => {
            setError(true);
            showError("Submission failed", "Please try again.");
            console.log("Submission failed. Please try again.");
          },
        },
      );
    } else {
      setWatchVideo(true);
      mutationResponding.mutate(
        {
          courseId: course_id,
          activityId: activity_id,
          userInput: "",
          video: videoBlob,
        },
        {
          onSuccess: (data) => {
            if (data) {
              if (data.next_activity_link) {
                setNextActivityLink(data.next_activity_link);
              }
              if (data.result) {
                setResponse(true);
                loadRespondingData(data.result);
              }
            }
          },
          onError: () => {
            setError(true);
            showError("Submission failed", "Please try again.");
            console.log("Submission failed. Please try again.");
          },
        },
      );
    }
  };

  // useEffect(() => {
  //   const response = {
  //     "Sentiment Classification": {
  //       "Audio": "Neutral",
  //       "Video": "Neutral",
  //       "Text": "Neutral"
  //     },
  //     "Sentiment Scores": {
  //       "Audio": {
  //         "Angry": 5,
  //         "Happy": 10,
  //         "Sad": 5,
  //         "Neutral": 80
  //       },
  //       "Video": {
  //         "Angry": 5,
  //         "Happy": 10,
  //         "Sad": 5,
  //         "Neutral": 80
  //       },
  //       "Text": {
  //         "Angry": 5,
  //         "Happy": 5,
  //         "Sad": 5,
  //         "Neutral": 85
  //       }
  //     },
  //     "Reasons": {
  //       "Audio": "The spectrogram shows a relatively even intensity pattern and lacks high-intensity fluctuations typical of strong emotions like anger or excitement, indicating a neutral tone.",
  //       "Video": "The subject shows a calm demeanor with no excessive facial expressions or body movements indicative of strong emotions, contributing to a neutral classification.",
  //       "Text": "The transcription consists of polite and formal language without emotional exclamations or suggestive words, reinforcing a neutral sentiment."
  //     },
  //     "Combined Sentiment Analysis": {
  //       "Combined Sentiment": "Neutral",
  //       "Comparison and Discrepancy": "All three modalities (Audio, Video, and Text) consistently suggest a neutral sentiment. The audio displays no strong emotional cues, the video frames show a calm and composed demeanor, and the text uses polite, formal language without emotional indicators.",
  //       "Summary": "The sentiment analysis across all modalities consistently indicates a neutral sentiment. There are no significant discrepancies or varying interpretations between the audio, video, or text."
  //     },
  //     "Professional Conduct Evaluation": {
  //       "Conduct Assessment": "Based on the analysis, there are no indications of unprofessional conduct. The subject displays a high level of politeness and formality.",
  //       "Examples of Unprofessional Conduct": "N/A - No examples of unprofessional conduct were noted.",
  //       "Recommendations": "To maintain professional conduct, it is recommended to continue using clear, polite language and staying composed. Ensuring all required documentation is noted beforehand can enhance the clarity and preparedness in future interactions."
  //     }
  //   }
  //   loadResponse(response);
  // }, []);

  const loadListeningData = (response: any) => {
    if (response && typeof response == "object") {
      setListeningData(response["listening"]);
      setyouDidWellAtListening(response["you_did_well_at_the_following"]);
      setyouCanImproveOnListening(
        response["you_can_improve_by_focusing_on_the_following"],
      );
    }
  };

  const loadRespondingData = (response: any) => {
    if (response && typeof response == "object") {
      setRespondingData(response["responding"]);
      setyouDidWellAtResponding(response["you_did_well_at_the_following"]);
      setyouCanImproveOnResponding(
        response["you_can_improve_by_focusing_on_the_following"],
      );
    }
  };

  const setSentiData = (sentimentScores: SentimentScoresIndependent) => {
    const sentimentData: { [key: string]: object[] } = {};

    Object.entries(sentimentScores).forEach(([exp, expVal]) => {
      sentimentData[exp] = Object.entries(expVal).map(([key, val]) => ({
        label: key,
        value: val,
      }));
    });

    setSentimentDataObject(sentimentData);
  };

  const {
    status,
    startRecording,
    stopRecording,
    mediaBlobUrl,
    previewStream,
    clearBlobUrl,
  } = useReactMediaRecorder({
    onStop(blobUrl, blob) {
      if (!watchTutorial) {
        console.log("recorder blob url:", blobUrl);
        handleUpload(blob);
        clearBlobUrl();
        // setWatchTutorial(!watchTutorial);
      }
    },
    video: true,
    audio: true,
  });

  const handleTutorialStarted = () => {
    stopRecording();
    clearBlobUrl();
    startRecording();
  };

  const handleTutorialEnded = () => {
    stopRecording();
  };

  const getBlobAndUpload = () => {
    if (mediaBlobUrl) {
      fetch(mediaBlobUrl)
        .then((response) => response.blob())
        .then((blob) => handleUpload(blob))
        .then(() => clearBlobUrl());
    }
  };

  const handleRecord = () => {
    // handleStopRecord();
    setRecording(true);
    stopRecording();
    clearBlobUrl();
    setcameraStatus("close");
    startRecording();
  };

  const openCamera = () => {
    clearBlobUrl();
    setcameraStatus("open");
    startRecording();
  };

  const handleStopRecord = () => {
    setRecording(false);
    stopRecording();
  };

  useEffect(() => {
    queryClient.invalidateQueries({ queryKey: ["activity"] });
    setWatchVideo(false);
    setError(false);
    setResponse(false);
    setWatchTutorial(false);
    setcameraStatus("not");
    stopRecording();
    setRecording(false);
    clearBlobUrl();
    setRespondingData(null);
    setListeningData(null);
    setTutorialRecording(false);
  }, [activity_id]);

  // Log activity data when it's loaded
  useEffect(() => {
    if (activity) {
      console.log("Activity loaded:", {
        id: activity.id,
        name: activity.name,
        type: activity.type,
        video: activity.video,
        second_video: activity.second_video,
        extra_fields: activity.extra_fields,
      });
    }
  }, [activity]);

  // Extract Heygen video information from instructions and extra_fields
  useEffect(() => {
    // First try to get the video URL from extra_fields
    if (activity?.extra_fields && activity.extra_fields.heygen_video_url) {
      console.log(
        "Found Heygen video URL in extra_fields:",
        activity.extra_fields.heygen_video_url,
      );
      setHeygenVideoUrl(activity.extra_fields.heygen_video_url);

      if (activity.extra_fields.heygen_video_id) {
        setHeygenVideoId(activity.extra_fields.heygen_video_id);
      }
      return;
    }

    // If not found in extra_fields, try to extract from instructions
    if (activity?.instructions) {
      // Create a temporary div to parse the HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = activity.instructions;

      // Find the div with Heygen video data
      const heygenDiv = tempDiv.querySelector("div[data-heygen-video-id]");

      if (heygenDiv) {
        const videoId = heygenDiv.getAttribute("data-heygen-video-id");
        const videoUrl = heygenDiv.getAttribute("data-heygen-video-url");

        console.log("Found Heygen video data in instructions:", {
          videoId,
          videoUrl,
        });

        if (videoId) setHeygenVideoId(parseInt(videoId));
        if (videoUrl) setHeygenVideoUrl(videoUrl);
      } else {
        console.log(
          "No Heygen video data found in instructions or extra_fields",
          {
            instructions: activity.instructions,
            extra_fields: activity.extra_fields,
          },
        );
      }
    }
  }, [activity]);

  function setResponseToNull() {
    setError(false);
    setResponse(false);
    clearBlobUrl();
    setWatchVideo(false);
    setcameraStatus("not");
    setWatchTutorial(false);
    stopRecording();
    setRecording(false);
    setRespondingData(null);
    setListeningData(null);
    setTutorialRecording(false);
  }

  const closeVideo = () => {
    setWatchVideo(false);
    if (error) {
      setResponse(false);
    } else {
      setResponse(true);
    }
    setError(false);
    setcameraStatus("not");
    clearBlobUrl();
  };

  useEffect(() => {
    return () => {
      if (status === "recording") {
        stopRecording();
      }
    };
  }, [status, stopRecording]);

  const VideoPreview = ({ stream }: { stream: MediaStream | null }) => {
    const videoRef = useRef<HTMLVideoElement>(null);

    useEffect(() => {
      if (videoRef.current && stream) {
        videoRef.current.srcObject = stream;
      }
    }, [stream]);
    if (!stream) {
      return null;
    }
    return <video ref={videoRef} width={640} height={480} autoPlay />;
  };

  const VideoPreviewControl = ({ stream }: { stream: MediaStream | null }) => {
    const videoRefC = useRef<HTMLVideoElement>(null);

    useEffect(() => {
      if (videoRefC.current && stream) {
        videoRefC.current.srcObject = stream;
      }
    }, [stream]);
    if (!stream) {
      return null;
    }
    return <video ref={videoRefC} width={640} height={480} autoPlay controls />;
  };

  return (
    <div>
      <Spinner loading={(isLoading || isFetching) && !isError} />
      <h1
        key={activity_id}
        className="mb-4 text-center text-2xl font-bold leading-none tracking-tight text-gray-900"
      >
        {activity?.name}
      </h1>
      {watchVideo ? (
        <div className="flex flex-col items-center justify-center space-y-20 bg-[#F7F6E3] py-10">
          <div className="flex w-[607px] flex-col items-start">
            {activity?.second_video ? (
              <div className="flex bg-slate-300 p-3">
                <video
                  key={activity?.second_video}
                  width="607"
                  height="362"
                  controls
                  className="hide-seekbar"
                >
                  <source src={activity?.second_video} type="video/mp4" />
                </video>
              </div>
            ) : (
              <div className="p-4 text-red-600">
                No second video available. Debug info:{" "}
                {JSON.stringify({
                  activitySecondVideo: activity?.second_video,
                  heygenVideoUrl,
                })}
              </div>
            )}
            <h1 className="mt-8 text-left">
              While the system is analyzing your response, here’s a sample video
              from the Trainer on how we could have responded to the elderly
              citizen. Do you notice any similarities or differences from the
              way you responded?
            </h1>
          </div>
          {!mutation.isPending ? (
            <RedButton onClick={closeVideo}>
              {error
                ? "Error: Try Again"
                : mutation.isPending
                  ? "Skip"
                  : "Continue"}
            </RedButton>
          ) : (
            <RedButton onClick={closeVideo} disabled={true}>
              Processing...
            </RedButton>
          )}
        </div>
      ) : (
        <>
          <div>
            <div className="flex flex-col items-center justify-center space-y-20 bg-[#F7F6E3] py-10">
              <div className="flex flex-col items-start">
                {activity?.video ? (
                  <div className="flex bg-slate-300 p-3">
                    <video
                      key={activity?.video}
                      width="607"
                      height="362"
                      controls
                      className="hide-seekbar"
                      onPlay={() => {
                        if (!tutorialRecording) {
                          setTutorialRecording(!tutorialRecording);
                          handleTutorialStarted();
                        }
                      }}
                      onEnded={handleTutorialEnded}
                    >
                      <source src={activity?.video} type="video/mp4" />
                      {/* <source src="/demo-video.mov" /> */}
                    </video>
                  </div>
                ) : (
                  <div className="p-4 text-red-600">
                    No video available. Debug info:{" "}
                    {JSON.stringify({
                      activityVideo: activity?.video,
                      heygenVideoUrl,
                    })}
                  </div>
                )}
              </div>
            </div>
            {watchTutorial && (
              <div className="flex flex-col items-start">
                {response ? (
                  <h1 className="text-xl font-semibold">
                    Your Results from AI Analysis
                  </h1>
                ) : (
                  <>
                    {isBossChallenge ? (
                      <h1 className="text-lg">
                        For this activity, once you are ready, click Start
                        Recording THEN play the video so that the system also
                        records your listening face while you are listening to
                        this stakeholder. Then click STOP RECORDING once you
                        have finished your response.
                      </h1>
                    ) : (
                      <h1 className="text-lg">
                        Now let's try responding to the elderly citizen in the
                        previous scenario. Don’t worry - just respond as you
                        would usually do in a real-life setting. We want to see
                        what your natural response would be, before any further
                        training. Imagine that you are now the volunteer who has
                        to respond to the same elderly citizen in the previous
                        scenario.
                        <br />
                        To do this, please click on the red “View Camera
                        Preview” button, then “Start Recording”. Right after
                        clicking “Start Recording”, please play the video a
                        SECOND time and respond instinctively while we are being
                        recorded. Once done, we will then click on the “Upload”
                        button for an analysis, and while we are waiting, we
                        will also get to see an example of a possible response.
                        <br />
                        Ready? Click START Recording, then PLAY the video
                        immediately
                      </h1>
                    )}
                  </>
                )}
                <div className="flex w-full flex-col items-center justify-center space-y-20 bg-[#F7F6E3] py-10">
                  {response ? (
                    <>
                      <div className="grid w-full max-w-7xl grid-cols-2 gap-4 text-center">
                        {mutation.isPending ? (
                          <p>Loading...</p>
                        ) : mutation.isError ? (
                          <p>Error...</p>
                        ) : listeningData ? (
                          <ListeningComponent
                            listeningData={listeningData}
                            youDidWellAt={youDidWellAtListening}
                            youCanImproveOn={youCanImproveOnListening}
                          />
                        ) : null}
                        {mutationResponding.isPending ? (
                          <p>Loading...</p>
                        ) : mutationResponding.isError ? (
                          <p>Error...</p>
                        ) : respondingData ? (
                          <RespondingComponent
                            respondingData={respondingData}
                            youDidWellAt={youDidWellAtResponding}
                            youCanImproveOn={youCanImproveOnResponding}
                          />
                        ) : null}
                      </div>
                      {!isBossChallenge ? (
                        <div className="text-center">
                          <GradientButton
                            text="Try Again"
                            color1="#2B3D59"
                            color2="#375685"
                            onClick={setResponseToNull}
                          />
                        </div>
                      ) : null}
                      <NextButton
                        onClick={() => redirectToLink(nextActivityLink)}
                      />
                    </>
                  ) : (
                    <>
                      {/* <VideoDropzone onDrop={handleDrop} />
                    <OrSeparator /> */}
                      <div className="text-center">
                        {cameraStatus == "open" ? (
                          <VideoPreview stream={previewStream} />
                        ) : recording ? (
                          <VideoPreviewControl stream={previewStream} />
                        ) : null}
                        {!recording && mediaBlobUrl && (
                          <div>
                            <video
                              key={mediaBlobUrl}
                              style={{ margin: "auto" }}
                              width="640"
                              height="480"
                              controls
                            >
                              <source src={mediaBlobUrl} type="video/mp4" />
                            </video>
                          </div>
                        )}
                        <div>
                          {!recording && cameraStatus == "not" && (
                            <GradientButton
                              text="View Camera Preview"
                              color1="#E6635A"
                              color2="#D64D44"
                              onClick={openCamera}
                            />
                          )}
                          {!recording &&
                            ["open", "close"].includes(cameraStatus) && (
                              <GradientButton
                                text={
                                  mediaBlobUrl
                                    ? "Record Again"
                                    : "Start Recording"
                                }
                                color1="#E6635A"
                                color2="#D64D44"
                                onClick={handleRecord}
                              />
                            )}
                          {recording && (
                            <GradientButton
                              text="Stop Recording"
                              color1="#E6635A"
                              color2="#D64D44"
                              onClick={handleStopRecord}
                            />
                          )}
                          {!recording && mediaBlobUrl && (
                            <GradientButton
                              text="Upload"
                              color1="#E6635A"
                              color2="#D64D44"
                              onClick={getBlobAndUpload}
                            />
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
            {!watchTutorial && (
              <>
                <div className="text-3xl font-extrabold">
                  Please watch the video above
                </div>
                <div key={activity_id}>
                  {isBossChallenge ? (
                    <h1 className="text-left">
                      {/* {activity?.instructions} */}
                      We hope that you have gained some insights after going
                      through the tutorials! It’s time for a final practicum to
                      apply everything that you have learned so far.
                      <br />
                      Now imagine you are another volunteer at the same event.
                      The elderly person, who felt offended by your colleague
                      who spoke to him earlier, has approached you to share how
                      he feels.
                      <br />
                      Watch the video above to prepare yourself for your
                      response in the next activity.
                    </h1>
                  ) : (
                    <h1 className="text-left">{activity?.instructions}</h1>
                  )}
                </div>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default ActivityContainer;
