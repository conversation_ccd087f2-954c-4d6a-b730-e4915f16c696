import { BrowserRouter, Route, Routes, Navigate } from "react-router-dom";
import { useState } from "react";
import HomeScreen from "./Learner/Home/HomeScreen";
import MyLearningScreen from "./Learner/MyLearningScreen";
// import WelcomeScreen from "./Learner/WelcomeScreen";
import Login from "./MainScreens/SIgnIn/Login";
import { useAuth } from "./Learner/context/AuthProvider";
import ProtectedRoute from "./Learner/ProtectedRoute";
import MainInstructorScreen from "./Instructor/MainInstructorScreen";
import MainAdminScreen from "./Admin/MainAdminScreen";
import MainSuperAdminScreen from "./SuperAdmin/MainSuperAdminScreen";
import Demo from "./Demo/Demo";
import CacheDebugPanel from "./components/CacheDebugPanel";

function App() {
  const { token, userType } = useAuth();
  const [showCacheDebug, setShowCacheDebug] = useState(false);

  // Only show cache debug panel in development
  const isDevelopment = import.meta.env.DEV;

  return (
    <BrowserRouter>
      <main>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route
            path="my-learning/*"
            element={<ProtectedRoute element={<MyLearningScreen />} />}
          />
          <Route
            path="/home-screen"
            element={<ProtectedRoute element={<HomeScreen />} />}
          />
          <Route path="/demo" element={<Demo />} />
          <Route
            path="/"
            element={<Navigate to={getNextLink(token, userType)} />}
          />
          <Route
            path="/instructor/*"
            element={
              token && userType && userType == "instructor" ? (
                <ProtectedRoute element={<MainInstructorScreen />} />
              ) : (
                <Navigate to="/login" />
              )
            }
          />
          <Route
            path="/admin/*"
            element={
              token && userType && userType == "admin" ? (
                <ProtectedRoute element={<MainAdminScreen />} />
              ) : (
                <Navigate to="/login" />
              )
            }
          />
          <Route
            path="/superadmin/*"
            element={
              token && userType && userType == "superadmin" ? (
                <ProtectedRoute element={<MainSuperAdminScreen />} />
              ) : (
                <Navigate to="/login" />
              )
            }
          />
        </Routes>

        {/* Cache Debug Panel - Only in development */}
        {isDevelopment && (
          <CacheDebugPanel
            isVisible={showCacheDebug}
            onToggle={() => setShowCacheDebug(!showCacheDebug)}
          />
        )}
      </main>
    </BrowserRouter>
  );
}

function getNextLink(token: string | null, userType: string | null) {
  // console.log(token, userType, userType == "instructor"? "/instructor": "/home-screen")
  if (token && userType) {
    return userType == "instructor"
      ? "/instructor"
      : userType == "admin"
        ? "/admin"
        : userType == "superadmin"
          ? "/superadmin"
          : userType == "learner"
            ? "/home-screen"
            : "/login";
  }
  return "/login";
}

export default App;
