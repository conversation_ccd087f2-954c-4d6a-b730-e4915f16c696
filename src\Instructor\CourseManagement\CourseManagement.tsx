import { useNavigate } from "react-router-dom";
import CourseCard from "./CourseCard";
import Spinner from "../../Learner/components/Spinner";
import { useGetAllCourses } from "../hooks/useGetAllCourses";
import { useDeleteCourse } from "../hooks/useDeleteCourse";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import showError from "../../Learner/scripts/showErrorDialog";

function CourseManagement() {
  const navigate = useNavigate();
  const {
    data: allCourses,
    isLoading,
    isFetching,
    isError,
    refetch,
  } = useGetAllCourses();

  const deleteMutation = useDeleteCourse();

  const handleDeleteCourse = (courseId: string) => {
    if (confirm("Are you sure you want to delete this course?")) {
      deleteMutation.mutate(
        { courseId },
        {
          onSuccess: () => {
            showSuccess("Success", "Course deleted successfully!");
            refetch(); // Refresh the courses list
          },
          onError: () => {
            showError("Error", "Failed to delete course. Please try again.");
          },
        },
      );
    }
  };

  return (
    <>
      <Spinner loading={isFetching || deleteMutation.isPending} />
      <div className="flex h-fit w-full flex-col bg-[#FEFBF2] bg-opacity-70 p-14">
        <div className="mb-8 flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-[#36537F]">Courses</h2>
          <button
            onClick={() => navigate("/instructor/create-course")}
            className="rounded-lg bg-[#36537F] px-6 py-3 text-lg font-medium text-white hover:bg-blue-600"
          >
            Create New Course
          </button>
        </div>
        <div className="flex flex-wrap gap-4">
          {allCourses &&
            allCourses.map((course) => (
              <CourseCard
                key={course.id}
                activities={course && course?.activities}
                title={course?.name || ""}
                onEditClick={() =>
                  navigate(
                    "/instructor/course-development?course_id=" + course.id,
                  )
                }
                onDeleteClick={() => handleDeleteCourse(course.id.toString())}
              />
            ))}
        </div>
      </div>
    </>
  );
}

export default CourseManagement;
