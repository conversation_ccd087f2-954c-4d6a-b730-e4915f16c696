import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { usePostActivityProgress } from "../hooks/usePostActivityProgress";
import { useGetActivity } from "../hooks/useGetActivity";
import { useCourseId } from "../hooks/useCourseId";
import QuestionCard from "./QuestionCard";
import { Question } from "../hooks/useQuestion";
import GradientButton from "../components/GradientButton";
import { useNavigate } from "react-router-dom";
import showError from "../scripts/showErrorDialog";
import showSuccess from "../scripts/showSuccessDialog";
import Spinner from "../components/Spinner";
import NextButton from "../components/NextButton";
import { useQueryClient } from "@tanstack/react-query";

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

function Quiz() {
  const queryClient = useQueryClient();
  const query = useQueryParam();
  const course_id = useCourseId();
  const activity_id = query.get("activity_id") ?? "";
  const {
    data: activity,
    isLoading,
    isFetching,
    isError,
  } = useGetActivity(activity_id);
  const mutation = usePostActivityProgress();
  const [nextActivityLink, setnextActivityLink] = useState("");
  const navigate = useNavigate();

  const redirectToLink = (nextActivityLink: string) => {
    navigate("/my-learning" + nextActivityLink);
  };

  const [selectedList, setSelectedList] = useState<Question[]>([]);
  const [showResult, setShowResult] = useState<boolean>(false);

  useEffect(() => {
    queryClient.invalidateQueries({ queryKey: ["activity"] });
  }, [activity_id]);

  useEffect(() => {
    if (activity?.mcqs !== undefined) {
      setSelectedList(activity?.mcqs);
    }
  }, [activity?.mcqs]);

  useEffect(() => {
    setShowResult(false);
  }, []);

  const ResultsComp = ({
    correct,
    incorrect,
  }: {
    correct: number;
    incorrect: number;
  }) => (
    <div className="flex h-24 w-96 flex-col items-start justify-center gap-3 rounded bg-gradient-to-tr from-[#2B3D59] to-[#375685] p-5 text-xl font-medium text-white">
      <h1>Correct Answers: {correct}</h1>
      <h1>Incorrect Answers : {incorrect}</h1>
    </div>
  );

  function getResultsHTML(correct: number, incorrect: number) {
    return `
      <div style="display: flex; height: 6rem; flex-direction: column; align-items: flex-center; justify-content: center; gap: 0.75rem; border-radius: 0.5rem; background: linear-gradient(to top right, #2B3D59, #375685); padding: 1.25rem; font-size: 1.25rem; font-weight: 500; color: white;">
        <h1>Correct Answers: ${correct}</h1>
        <h1>Incorrect Answers: ${incorrect}</h1>
      </div>
    `;
  }

  // const { data: questions, isLoading } = useQuestions();
  // const { mutate } = mutateQuestion();

  const onSubmitHandle = () => {
    if (activity?.mcqs !== undefined) {
      console.log(selectedList);
      if (selectedList.map((e) => e.selectedOption).includes(undefined)) {
        showError("Error", "Please answer all questions first");
      } else {
        mutation.mutate(
          {
            courseId: course_id,
            activityId: activity_id,
            userInput: JSON.stringify(selectedList),
          },
          {
            onSuccess: (data) => {
              if (data) {
                if (data.next_activity_link) {
                  setnextActivityLink(data.next_activity_link);
                }
                if (data.result && data.result.mcq_result) {
                  data.result.mcq_result?.forEach(
                    (e: {
                      id: number;
                      result: boolean | undefined;
                      right_option: string | undefined;
                    }) => {
                      const idx = selectedList.findIndex((v) => v.id == e.id);
                      selectedList[idx].result = e.result;
                      selectedList[idx].right_option = e.right_option;
                    },
                  );
                  setSelectedList(selectedList);
                }
                const correct = activity?.mcqs.filter((question) => {
                  return question.result === true;
                }).length;
                const inCorrect = activity?.mcqs.filter((question) => {
                  return question.result === false;
                }).length;

                showSuccess(
                  "Success",
                  "Quiz is successfully submitted",
                  getResultsHTML(correct, inCorrect),
                );
              }
            },
            onError: () => {
              showError("Submission failed", "Please try again.");
              console.log("Submission failed. Please try again.");
            },
          },
        );
        setShowResult(true);
      }
    }
  };

  const onQuestionSelectHandle = (q: Question, selectedOption: string) => {
    setSelectedList((prevList) => {
      if (prevList.length > 0) {
        const idx = prevList.findIndex((v) => v.id == q.id);
        q.selectedOption = selectedOption;
        if (idx === -1) {
          prevList.push(q);
        } else {
          prevList[idx] = q;
        }
        // console.log('index is', prevList.findIndex(v => v.id == q.id))
      } else {
        q.selectedOption = selectedOption;
        prevList.push(q);
      }
      return prevList;
    });
  };
  return (
    <>
      <Spinner
        loading={(isFetching || isLoading || mutation.isPending) && !isError}
      />
      {activity && (
        <>
          {activity?.instructions && (
            <p
              className="mb-4"
              dangerouslySetInnerHTML={{ __html: activity.instructions }}
            />
          )}
          <div className="grid grid-cols-1 gap-8">
            {isLoading ? (
              <></>
            ) : (
              selectedList.map((singleMCQ, index) => (
                <QuestionCard
                  key={singleMCQ.id}
                  q={{ ...singleMCQ, index: index + 1 }}
                  disable={mutation.isSuccess}
                  onSelect={onQuestionSelectHandle}
                />
              ))
            )}
          </div>
          {mutation.isSuccess && activity?.show_correct_option ? (
            <p className="text-center">
              * Correct option are highlighted in blue color.
            </p>
          ) : null}
          <div className="text-center">
            <GradientButton
              type="button"
              text="Submit"
              color1="#2B3D59"
              color2="#375685"
              width="200px"
              disabled={mutation.isPending || mutation.isSuccess}
              onClick={onSubmitHandle}
            />
          </div>
          <NextButton
            onClick={() => redirectToLink(nextActivityLink)}
            display={Boolean(nextActivityLink)}
          />
        </>
      )}
    </>
  );
}

export default Quiz;
