import { QueryClient } from '@tanstack/react-query';

/**
 * Comprehensive cache management utility
 * Clears all caches except authentication tokens
 */
export class CacheManager {
  private static queryClient: QueryClient | null = null;

  /**
   * Set the query client instance for cache clearing
   */
  static setQueryClient(client: QueryClient) {
    this.queryClient = client;
  }

  /**
   * Clear all browser storage except auth tokens
   */
  static clearBrowserStorage() {
    try {
      // Preserve auth tokens
      const token = localStorage.getItem("token");
      const userType = localStorage.getItem("userType");
      
      // Clear localStorage except auth tokens
      localStorage.clear();
      
      // Restore auth tokens if they existed
      if (token) localStorage.setItem("token", token);
      if (userType) localStorage.setItem("userType", userType);
      
      // Clear sessionStorage completely
      sessionStorage.clear();
      
      console.log("Browser storage cleared, auth tokens preserved");
    } catch (error) {
      console.warn("Error clearing browser storage:", error);
    }
  }

  /**
   * Clear IndexedDB databases
   */
  static async clearIndexedDB() {
    try {
      if ('indexedDB' in window) {
        const databases = await indexedDB.databases();
        const deletePromises = databases.map(db => {
          if (db.name) {
            return new Promise<void>((resolve, reject) => {
              const deleteReq = indexedDB.deleteDatabase(db.name!);
              deleteReq.onsuccess = () => resolve();
              deleteReq.onerror = () => reject(deleteReq.error);
            });
          }
          return Promise.resolve();
        });
        
        await Promise.all(deletePromises);
        console.log("IndexedDB cleared successfully");
      }
    } catch (error) {
      console.warn("Error clearing IndexedDB:", error);
    }
  }

  /**
   * Clear React Query cache
   */
  static clearReactQueryCache() {
    try {
      if (this.queryClient) {
        this.queryClient.clear();
        console.log("React Query cache cleared");
      }
    } catch (error) {
      console.warn("Error clearing React Query cache:", error);
    }
  }

  /**
   * Clear specific React Query cache keys
   */
  static clearSpecificQueries(queryKeys: string[]) {
    try {
      if (this.queryClient) {
        queryKeys.forEach(key => {
          this.queryClient!.removeQueries({ queryKey: [key] });
        });
        console.log("Specific queries cleared:", queryKeys);
      }
    } catch (error) {
      console.warn("Error clearing specific queries:", error);
    }
  }

  /**
   * Reset Zustand stores to initial state
   */
  static resetZustandStores() {
    try {
      // Reset demo store if it exists
      if (typeof window !== 'undefined' && (window as any).resetDemoStore) {
        (window as any).resetDemoStore();
      }
      
      console.log("Zustand stores reset");
    } catch (error) {
      console.warn("Error resetting Zustand stores:", error);
    }
  }

  /**
   * Clear all caches except authentication tokens
   */
  static async clearAllCacheExceptAuth() {
    console.log("Starting comprehensive cache clearing...");
    
    // Clear browser storage (preserving auth)
    this.clearBrowserStorage();
    
    // Clear React Query cache
    this.clearReactQueryCache();
    
    // Clear IndexedDB
    await this.clearIndexedDB();
    
    // Reset Zustand stores
    this.resetZustandStores();
    
    console.log("All caches cleared successfully, auth tokens preserved");
  }

  /**
   * Clear cache on app initialization
   */
  static initializeCacheClear() {
    // Run cache clearing immediately
    this.clearAllCacheExceptAuth();
  }

  /**
   * Get cache status for debugging
   */
  static getCacheStatus() {
    const status = {
      localStorage: {
        itemCount: localStorage.length,
        hasToken: !!localStorage.getItem("token"),
        hasUserType: !!localStorage.getItem("userType")
      },
      sessionStorage: {
        itemCount: sessionStorage.length
      },
      reactQuery: {
        hasClient: !!this.queryClient,
        queryCount: this.queryClient?.getQueryCache().getAll().length || 0
      }
    };
    
    console.log("Cache status:", status);
    return status;
  }
}

// Export convenience functions
export const clearAllCacheExceptAuth = () => CacheManager.clearAllCacheExceptAuth();
export const clearBrowserStorage = () => CacheManager.clearBrowserStorage();
export const clearReactQueryCache = () => CacheManager.clearReactQueryCache();
export const getCacheStatus = () => CacheManager.getCacheStatus();
