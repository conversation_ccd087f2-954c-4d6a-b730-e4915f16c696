import React from 'react';
import { useGetLoginAnalytics } from '../hooks/useGetLoginAnalytics';
import Spinner from '../../Learner/components/Spinner';

const LoginUsersTable: React.FC = () => {
  const { data: login, isFetching } = useGetLoginAnalytics();
  return (
    <>
      <Spinner loading={isFetching} />
      <div className="p-4 bg-white rounded-lg shadow-md">
        <table className="w-full text-left table-auto">
          <thead>
            <tr className="bg-gray-100">
              <th className="p-2 font-medium">Metric</th>
              <th className="p-2 font-medium">Quantity</th>
              <th className="p-2 font-medium">Duration</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b">
              <td className="p-2">No of Active Users</td>
              <td className="p-2">{login?.data.active_users}</td>
              <td className="p-2">Daily</td>
            </tr>
            <tr className="border-b">
              <td className="p-2">Users that Accessed the Course</td>
              <td className="p-2">{login?.data.users_accessed_course}</td>
              <td className="p-2">-</td>
            </tr>
            <tr className="border-b">
              <td className="p-2">Average User Session</td>
              <td className="p-2">{login?.data.average_user_session}</td>
              <td className="p-2">Daily</td>
            </tr>
            <tr>
              <td className="p-2">Time of day when most users</td>
              <td className="p-2">{login?.data.time_of_day_most_users_active}</td>
              <td className="p-2">Daily</td>
            </tr>
          </tbody>
        </table>
      </div>
    </>
  );
};

export default LoginUsersTable;
