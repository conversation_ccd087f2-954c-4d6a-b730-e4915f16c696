import { ReactNode, useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import Swal from "sweetalert2";
import { useAuth } from "../../Learner/context/AuthProvider";

interface sideBarItem {
  name: string;
  icon: ReactNode;
  children?: string[];
  selected?: boolean;
}

const sideBarItems: sideBarItem[] = [
  {
    name: "Instructor-Portal",
    icon: (
      <svg
        width="13"
        height="15"
        viewBox="0 0 13 15"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M3.78114 0.525002C4.78864 0.248436 5.85929 0 6.5 0C7.14071 0 8.21136 0.249374 9.21886 0.524063C10.1188 0.774124 11.0127 1.04607 11.8996 1.33969C12.1552 1.42503 12.3817 1.5814 12.5532 1.79084C12.7246 2.00029 12.8341 2.25431 12.8691 2.52375C13.4225 6.72093 12.1383 9.83156 10.5801 11.8894C9.91941 12.7697 9.13156 13.5449 8.24293 14.1891C7.93565 14.412 7.61009 14.608 7.26979 14.775C7.00886 14.8987 6.73029 15 6.5 15C6.26972 15 5.99022 14.8987 5.73021 14.775C5.44793 14.6409 5.1155 14.445 4.75707 14.1891C3.86846 13.5448 3.08062 12.7697 2.41986 11.8894C0.861718 9.83156 -0.422494 6.72093 0.130933 2.52375C0.165929 2.25444 0.27546 2.00056 0.446942 1.79128C0.618425 1.582 0.844903 1.4258 1.10036 1.34062C1.71136 1.13906 2.75043 0.806252 3.78114 0.525002Z"
          fill="#A3A3A3"
        />
        <path
          d="M6.56679 6.26814C6.1384 7.23194 5.43509 9.52585 7.00462 11.9229C6.74726 11.9666 6.48732 11.9923 6.22658 12C5.88259 11.4374 4.5526 8.9662 5.8095 6.18226C5.81861 6.1624 5.82248 6.14042 5.82074 6.11852C5.819 6.09663 5.8117 6.07559 5.79957 6.05751C5.78745 6.03943 5.77092 6.02495 5.75164 6.01551C5.73236 6.00607 5.71101 6.002 5.68972 6.00372C2.91163 6.23588 1.03489 3.19116 1 3.19116C3.64747 2.15531 6.07474 5.73178 6.14971 5.8441C6.15015 5.84512 6.15079 5.84603 6.1516 5.84677C6.15271 5.84473 6.15397 5.84278 6.15537 5.84094C6.24497 5.69951 7.97647 3.0376 11 3C11 3.07059 10.2981 5.14302 7.24794 5.72135C7.10003 5.74941 6.96089 5.81376 6.84231 5.90895C6.72373 6.00414 6.62922 6.12736 6.56679 6.26814Z"
          fill="white"
        />
      </svg>
    ),
  },
  {
    name: "Course-Management",
    icon: (
      <svg
        width="11"
        height="14"
        viewBox="0 0 11 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.375 14C0.996875 14 0.673292 13.863 0.40425 13.5891C0.135208 13.3152 0.000458333 12.9855 0 12.6V1.4C0 1.015 0.13475 0.685533 0.40425 0.4116C0.67375 0.137667 0.997333 0.000466667 1.375 0H9.625C10.0031 0 10.3269 0.1372 10.5964 0.4116C10.8659 0.686 11.0005 1.01547 11 1.4V12.6C11 12.985 10.8655 13.3147 10.5964 13.5891C10.3274 13.8635 10.0036 14.0005 9.625 14H1.375ZM4.8125 6.3L6.53125 5.25L8.25 6.3V1.4H4.8125V6.3Z"
          fill="#A3A3A3"
        />
      </svg>
    ),
  },
  {
    name: "Create-Course",
    icon: (
      <svg
        width="11"
        height="14"
        viewBox="0 0 11 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.375 14C0.996875 14 0.673292 13.863 0.40425 13.5891C0.135208 13.3152 0.000458333 12.9855 0 12.6V1.4C0 1.015 0.13475 0.685533 0.40425 0.4116C0.67375 0.137667 0.997333 0.000466667 1.375 0H9.625C10.0031 0 10.3269 0.1372 10.5964 0.4116C10.8659 0.686 11.0005 1.01547 11 1.4V12.6C11 12.985 10.8655 13.3147 10.5964 13.5891C10.3274 13.8635 10.0036 14.0005 9.625 14H1.375ZM4.8125 6.3L6.53125 5.25L8.25 6.3V1.4H4.8125V6.3Z"
          fill="#A3A3A3"
        />
      </svg>
    ),
  },
  {
    name: "Course-Enrollment",
    icon: (
      <svg
        width="11"
        height="14"
        viewBox="0 0 11 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.375 14C0.996875 14 0.673292 13.863 0.40425 13.5891C0.135208 13.3152 0.000458333 12.9855 0 12.6V1.4C0 1.015 0.13475 0.685533 0.40425 0.4116C0.67375 0.137667 0.997333 0.000466667 1.375 0H9.625C10.0031 0 10.3269 0.1372 10.5964 0.4116C10.8659 0.686 11.0005 1.01547 11 1.4V12.6C11 12.985 10.8655 13.3147 10.5964 13.5891C10.3274 13.8635 10.0036 14.0005 9.625 14H1.375ZM4.8125 6.3L6.53125 5.25L8.25 6.3V1.4H4.8125V6.3Z"
          fill="#A3A3A3"
        />
      </svg>
    ),
  },
  {
    name: "Create-Scenario",
    icon: (
      <svg
        width="11"
        height="14"
        viewBox="0 0 11 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.375 14C0.996875 14 0.673292 13.863 0.40425 13.5891C0.135208 13.3152 0.000458333 12.9855 0 12.6V1.4C0 1.015 0.13475 0.685533 0.40425 0.4116C0.67375 0.137667 0.997333 0.000466667 1.375 0H9.625C10.0031 0 10.3269 0.1372 10.5964 0.4116C10.8659 0.686 11.0005 1.01547 11 1.4V12.6C11 12.985 10.8655 13.3147 10.5964 13.5891C10.3274 13.8635 10.0036 14.0005 9.625 14H1.375ZM4.8125 6.3L6.53125 5.25L8.25 6.3V1.4H4.8125V6.3Z"
          fill="#A3A3A3"
        />
      </svg>
    ),
  },
  // {
  //   name: "Learner-Progress",
  //   icon: (
  //     <svg
  //       width="14"
  //       height="14"
  //       viewBox="0 0 14 14"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M6.4165 4.66668H7.58317V5.25001H6.4165V4.66668ZM6.4165 2.33334H7.58317V2.91668H6.4165V2.33334ZM6.4165 5.83334H7.58317V6.41668H6.4165V5.83334Z"
  //         fill="#A3A3A3"
  //       />
  //       <path
  //         d="M12.2498 7V5.25C10.4715 5.24944 8.7484 5.8682 7.37667 7H6.623C5.25127 5.8682 3.52821 5.24944 1.74984 5.25V7C1.59513 7 1.44675 7.06146 1.33736 7.17085C1.22796 7.28025 1.1665 7.42862 1.1665 7.58333V8.75C1.1665 8.90471 1.22796 9.05308 1.33736 9.16248C1.44675 9.27188 1.59513 9.33333 1.74984 9.33333V11.6667C3.69941 11.6647 5.57658 12.4051 6.99984 13.7375C8.42469 12.4077 10.3008 11.6676 12.2498 11.6667V9.33333C12.4045 9.33333 12.5529 9.27188 12.6623 9.16248C12.7717 9.05308 12.8332 8.90471 12.8332 8.75V7.58333C12.8332 7.42862 12.7717 7.28025 12.6623 7.17085C12.5529 7.06146 12.4045 7 12.2498 7Z"
  //         fill="#A3A3A3"
  //       />
  //       <path
  //         d="M5.24984 2.91667C5.572 2.91667 5.83317 2.6555 5.83317 2.33333C5.83317 2.01117 5.572 1.75 5.24984 1.75C4.92767 1.75 4.6665 2.01117 4.6665 2.33333C4.6665 2.6555 4.92767 2.91667 5.24984 2.91667Z"
  //         fill="#A3A3A3"
  //       />
  //       <path
  //         d="M8.74984 2.91667C9.072 2.91667 9.33317 2.6555 9.33317 2.33333C9.33317 2.01117 9.072 1.75 8.74984 1.75C8.42767 1.75 8.1665 2.01117 8.1665 2.33333C8.1665 2.6555 8.42767 2.91667 8.74984 2.91667Z"
  //         fill="#A3A3A3"
  //       />
  //       <path
  //         d="M9.33317 4.66667H4.6665C4.20252 4.6662 3.75767 4.48168 3.42958 4.15359C3.10149 3.8255 2.91697 3.38065 2.9165 2.91667V1.75C2.91697 1.28601 3.10149 0.841163 3.42958 0.513075C3.75767 0.184987 4.20252 0.000463292 4.6665 0L9.33317 0C9.79716 0.000463292 10.242 0.184987 10.5701 0.513075C10.8982 0.841163 11.0827 1.28601 11.0832 1.75V2.91667C11.0827 3.38065 10.8982 3.8255 10.5701 4.15359C10.242 4.48168 9.79716 4.6662 9.33317 4.66667ZM4.6665 1.16667C4.51179 1.16667 4.36342 1.22812 4.25403 1.33752C4.14463 1.44692 4.08317 1.59529 4.08317 1.75V2.91667C4.08317 3.07138 4.14463 3.21975 4.25403 3.32915C4.36342 3.43854 4.51179 3.5 4.6665 3.5H9.33317C9.48788 3.5 9.63625 3.43854 9.74565 3.32915C9.85505 3.21975 9.9165 3.07138 9.9165 2.91667V1.75C9.9165 1.59529 9.85505 1.44692 9.74565 1.33752C9.63625 1.22812 9.48788 1.16667 9.33317 1.16667H4.6665Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  // {
  //   name: "Feedback-&-Questions",
  //   icon: (
  //     <svg
  //       width="13"
  //       height="13"
  //       viewBox="0 0 13 13"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M0 13V1.3C0 0.9425 0.1274 0.636567 0.3822 0.3822C0.637 0.127833 0.942933 0.000433333 1.3 0H11.7C12.0575 0 12.3636 0.1274 12.6184 0.3822C12.8732 0.637 13.0004 0.942933 13 1.3V9.1C13 9.4575 12.8728 9.76365 12.6184 10.0184C12.3641 10.2732 12.0579 10.4004 11.7 10.4H2.6L0 13ZM6.5 8.45C6.68417 8.45 6.83865 8.3876 6.96345 8.2628C7.08825 8.138 7.15043 7.98373 7.15 7.8C7.14957 7.61627 7.08717 7.462 6.9628 7.3372C6.83843 7.2124 6.68417 7.15 6.5 7.15C6.31583 7.15 6.16157 7.2124 6.0372 7.3372C5.91283 7.462 5.85043 7.61627 5.85 7.8C5.84957 7.98373 5.91197 8.13822 6.0372 8.26345C6.16243 8.38868 6.3167 8.45087 6.5 8.45ZM5.85 5.85H7.15V1.95H5.85V5.85Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  {
    name: "Analytics",
    children: [
      "Overview",
      "Login-Users",
      "Hardware-Test",
      "Plunge-Activity",
      "Quiz",
      "AI-Practice",
      "Pre-Survey",
      "Feedback",
    ],
    icon: (
      <svg
        width="13"
        height="13"
        viewBox="0 0 13 13"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.88889 10.1111H4.33333V6.5H2.88889V10.1111ZM8.66667 10.1111H10.1111V2.88889H8.66667V10.1111ZM5.77778 10.1111H7.22222V7.94444H5.77778V10.1111ZM5.77778 6.5H7.22222V5.05555H5.77778V6.5ZM1.44444 13C1.04722 13 0.707296 12.8587 0.424667 12.5761C0.142037 12.2934 0.000481481 11.9533 0 11.5556V1.44444C0 1.04722 0.141556 0.707296 0.424667 0.424667C0.707778 0.142037 1.0477 0.000481481 1.44444 0H11.5556C11.9528 0 12.2929 0.141556 12.5761 0.424667C12.8592 0.707778 13.0005 1.0477 13 1.44444V11.5556C13 11.9528 12.8587 12.2929 12.5761 12.5761C12.2934 12.8592 11.9533 13.0005 11.5556 13H1.44444Z"
          fill="#A3A3A3"
        />
      </svg>
    ),
  },
  {
    name: "Settings",
    icon: (
      <svg
        width="12"
        height="13"
        viewBox="0 0 12 13"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.0023 8.26996C5.57892 8.26996 5.18244 8.10141 4.88225 7.79339C4.58347 7.48537 4.4178 7.07855 4.4178 6.64412C4.4178 6.20969 4.58347 5.80287 4.88225 5.49484C5.18244 5.18827 5.57892 5.01828 6.0023 5.01828C6.42569 5.01828 6.82217 5.18827 7.12236 5.49484C7.42114 5.80287 7.58681 6.20969 7.58681 6.64412C7.58681 7.07855 7.42114 7.48537 7.12236 7.79339C6.97574 7.945 6.80122 8.06519 6.60894 8.14701C6.41666 8.22882 6.21046 8.27061 6.0023 8.26996ZM11.8405 4.84828L10.9144 5.66048C10.9583 5.93653 10.981 6.21841 10.981 6.49882C10.981 6.77924 10.9583 7.06256 10.9144 7.33717L11.8405 8.14936C11.9104 8.21081 11.9605 8.29266 11.984 8.38401C12.0075 8.47537 12.0034 8.57191 11.9722 8.6608L11.9594 8.69857C11.7046 9.42985 11.3227 10.1077 10.8323 10.6993L10.8068 10.7298C10.7472 10.8016 10.6679 10.8533 10.5792 10.8779C10.4904 10.9026 10.3965 10.899 10.3098 10.8678L9.15999 10.4479C8.73519 10.8053 8.26224 11.0872 7.74965 11.2834L7.52734 12.5169C7.51056 12.6098 7.46663 12.6953 7.40138 12.762C7.33612 12.8287 7.25264 12.8735 7.16201 12.8903L7.12378 12.8976C6.38746 13.0341 5.61149 13.0341 4.87517 12.8976L4.83693 12.8903C4.74631 12.8735 4.66282 12.8287 4.59756 12.762C4.53231 12.6953 4.48838 12.6098 4.47161 12.5169L4.24788 11.2775C3.74014 11.0798 3.26715 10.7986 2.84745 10.445L1.68916 10.8678C1.60246 10.8993 1.50847 10.9029 1.41969 10.8783C1.33091 10.8536 1.25155 10.8018 1.19215 10.7298L1.16666 10.6993C0.677137 10.1071 0.295353 9.42939 0.0395221 8.69857L0.0267781 8.6608C-0.0369419 8.47918 0.0154501 8.27577 0.158466 8.14936L1.09586 7.32845C1.05196 7.0553 1.03072 6.77633 1.03072 6.50028C1.03072 6.22131 1.05196 5.94235 1.09586 5.6721L0.161298 4.85119C0.0913417 4.78974 0.0412729 4.70789 0.0177496 4.61654C-0.00577364 4.52518 -0.00163684 4.42864 0.0296101 4.33976L0.0423541 4.30198C0.29865 3.57115 0.676723 2.89553 1.16949 2.30128L1.19498 2.27077C1.25453 2.19892 1.33389 2.14728 1.42262 2.12264C1.51134 2.098 1.60526 2.10152 1.692 2.13274L2.85028 2.55554C3.27225 2.19957 3.74237 1.9177 4.25071 1.72301L4.47444 0.483653C4.49121 0.390727 4.53514 0.305236 4.6004 0.238539C4.66565 0.171843 4.74914 0.127097 4.83977 0.110248L4.878 0.102983C5.62156 -0.0343275 6.38304 -0.0343275 7.12661 0.102983L7.16484 0.110248C7.25547 0.127097 7.33896 0.171843 7.40421 0.238539C7.46946 0.305236 7.5134 0.390727 7.53017 0.483653L7.75248 1.7172C8.26507 1.9148 8.73802 2.19522 9.16282 2.55264L10.3126 2.13274C10.3993 2.10127 10.4933 2.09762 10.5821 2.12228C10.6709 2.14693 10.7502 2.19872 10.8096 2.27077L10.8351 2.30128C11.3279 2.89844 11.706 3.57115 11.9623 4.30198L11.975 4.33976C12.0359 4.51992 11.9835 4.72188 11.8405 4.84828ZM6.0023 4.08985C4.62737 4.08985 3.51297 5.23331 3.51297 6.64412C3.51297 8.05492 4.62737 9.19838 6.0023 9.19838C7.37724 9.19838 8.49163 8.05492 8.49163 6.64412C8.49163 5.23331 7.37724 4.08985 6.0023 4.08985Z"
          fill="#A3A3A3"
        />
      </svg>
    ),
  },
  {
    name: "Logout",
    icon: (
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.89476 0C1.39224 0 0.910298 0.189642 0.554962 0.527208C0.199626 0.864773 0 1.32261 0 1.8V10.2C0 10.6774 0.199626 11.1352 0.554962 11.4728C0.910298 11.8104 1.39224 12 1.89476 12H5.68427C6.1868 12 6.66874 11.8104 7.02407 11.4728C7.37941 11.1352 7.57903 10.6774 7.57903 10.2V1.8C7.57903 1.32261 7.37941 0.864773 7.02407 0.527208C6.66874 0.189642 6.1868 0 5.68427 0H1.89476ZM8.39567 3.1758C8.51411 3.06332 8.67473 3.00013 8.84221 3.00013C9.00968 3.00013 9.1703 3.06332 9.28874 3.1758L11.8151 5.5758C11.9335 5.68832 12 5.8409 12 6C12 6.1591 11.9335 6.31168 11.8151 6.4242L9.28874 8.8242C9.16962 8.9335 9.01008 8.99397 8.84448 8.99261C8.67888 8.99124 8.52047 8.92814 8.40337 8.81689C8.28627 8.70565 8.21984 8.55516 8.2184 8.39784C8.21696 8.24052 8.28062 8.08896 8.39567 7.9758L9.8439 6.6H4.4211C4.2536 6.6 4.09295 6.53679 3.9745 6.42426C3.85606 6.31174 3.78952 6.15913 3.78952 6C3.78952 5.84087 3.85606 5.68826 3.9745 5.57574C4.09295 5.46321 4.2536 5.4 4.4211 5.4H9.8439L8.39567 4.0242C8.27727 3.91168 8.21075 3.7591 8.21075 3.6C8.21075 3.4409 8.27727 3.28832 8.39567 3.1758Z"
          fill="#A3A3A3"
        />
      </svg>
    ),
  },
  {
    name: "Notifications",
    icon: (
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.89476 1.52588e-05C1.39224 1.52588e-05 0.910298 0.189658 0.554962 0.527223C0.199626 0.864789 0 1.32263 0 1.80002V10.2C0 10.6774 0.199626 11.1352 0.554962 11.4728C0.910298 11.8104 1.39224 12 1.89476 12H5.68427C6.1868 12 6.66874 11.8104 7.02407 11.4728C7.37941 11.1352 7.57903 10.6774 7.57903 10.2V1.80002C7.57903 1.32263 7.37941 0.864789 7.02407 0.527223C6.66874 0.189658 6.1868 1.52588e-05 5.68427 1.52588e-05H1.89476ZM8.39567 3.17582C8.51411 3.06333 8.67473 3.00014 8.84221 3.00014C9.00968 3.00014 9.1703 3.06333 9.28874 3.17582L11.8151 5.57582C11.9335 5.68833 12 5.84092 12 6.00002C12 6.15911 11.9335 6.3117 11.8151 6.42422L9.28874 8.82422C9.16962 8.93351 9.01008 8.99399 8.84448 8.99262C8.67888 8.99125 8.52047 8.92815 8.40337 8.81691C8.28627 8.70566 8.21984 8.55517 8.2184 8.39786C8.21696 8.24054 8.28062 8.08898 8.39567 7.97582L9.8439 6.60002H4.4211C4.2536 6.60002 4.09295 6.5368 3.9745 6.42428C3.85606 6.31176 3.78952 6.15915 3.78952 6.00002C3.78952 5.84089 3.85606 5.68827 3.9745 5.57575C4.09295 5.46323 4.2536 5.40002 4.4211 5.40002H9.8439L8.39567 4.02422C8.27727 3.9117 8.21075 3.75911 8.21075 3.60002C8.21075 3.44092 8.27727 3.28833 8.39567 3.17582Z"
          fill="#A3A3A3"
        />
      </svg>
    ),
  },
];

const handleLogout = (logout: () => void) => {
  Swal.fire({
    icon: "question",
    title: "Logging Out ...",
    text: "Are you sure?",
    showCancelButton: true,
    confirmButtonText: "Yes",
    cancelButtonText: "No",
  }).then((result) => {
    if (result.isConfirmed) {
      logout();
    }
  });
};

function SideBarChild({
  child,
  index,
  selectedIndex,
  onClickChild,
}: {
  child: string;
  index: number;
  selectedIndex: number;
  onClickChild: () => void;
}) {
  const navigate = useNavigate();
  return (
    <button
      className="h-12"
      onClick={() =>
        navigate(
          "/instructor/analytics/" +
            child.toLocaleLowerCase().replace(" ", "-"),
        )
      }
    >
      <div
        className={`mb-4 h-8 w-full cursor-pointer py-6 text-center text-lg font-normal ${index === selectedIndex ? "text-[#2e79c0] underline" : "text-[#A3A3A3]"}`}
        onClick={onClickChild}
      >
        {child.replace("-", " ")}
      </div>
    </button>
  );
}

function SideBarItem({
  item,
  index,
  selectedIndex,
  collapseChild,
  onClick,
}: {
  item: sideBarItem;
  index: number;
  selectedIndex: number;
  collapseChild: boolean;
  onClick: () => void;
}) {
  const [selectedChild, setSelectedChild] = useState<number>(-1);
  return (
    <div className="w-full">
      <div
        className={`my-4 flex max-h-fit min-h-10 w-full cursor-pointer flex-col justify-center rounded-lg p-5 ${index === selectedIndex ? "bg-[#145DA0]" : ""}`}
        onClick={onClick}
      >
        <div className="flex flex-row items-center gap-4">
          {item.icon}
          <h1
            className={`text-lg font-normal ${index === selectedIndex ? "text-white" : "text-[#A3A3A3]"}`}
          >
            {item.name.replace("-", " ").replace("&-", "& ")}
          </h1>
          {collapseChild && item.children && index === selectedIndex ? (
            <svg
              width="11"
              height="9"
              viewBox="0 0 11 9"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M5.5 9L10.2631 0.75H0.73686L5.5 9Z" fill="white" />
            </svg>
          ) : (
            <></>
          )}
        </div>
      </div>
      {collapseChild && index === selectedIndex ? (
        <div className="flex w-full flex-col items-center justify-start">
          {item.children?.map((child, index) => (
            <SideBarChild
              key={index}
              child={child}
              index={index}
              selectedIndex={selectedChild}
              onClickChild={() => setSelectedChild(index)}
            />
          ))}
        </div>
      ) : (
        <></>
      )}
    </div>
  );
}

function SideBar() {
  const [selectedItem, setSelectedItem] = useState<number>(-1);
  const [collapse, setCollapse] = useState<boolean>(true);
  const navigate = useNavigate();
  const { logout } = useAuth();
  const location = useLocation();

  // Set selected item based on current route
  useEffect(() => {
    const path = location.pathname
      .replace("course-development", "course-management")
      .replace("activity-development", "course-management");
    sideBarItems.forEach((item, index) => {
      const itemPath = item.name.includes("Portal")
        ? "/instructor"
        : "/instructor/" + item.name.toLowerCase();

      if (path === itemPath || path.startsWith(itemPath + "/")) {
        setSelectedItem(index);
        // If the item has children, expand it
        if (item.children) {
          setCollapse(true);
        }
      }
    });
  }, [location.pathname]);

  return (
    <ul className="w-3/12 divide-y-2 divide-[#E6DDB3] px-4">
      {sideBarItems.map((sideBarItem, index) => (
        <SideBarItem
          key={sideBarItem.name}
          item={sideBarItem}
          index={index}
          selectedIndex={selectedItem}
          onClick={() => {
            if (sideBarItem.children && selectedItem === index) {
              setCollapse(!collapse);
            } else {
              setCollapse(true);
              setSelectedItem(index);
            }
            sideBarItem.name !== "Logout"
              ? navigate(
                  sideBarItem.name.includes("Portal")
                    ? "/instructor"
                    : "/instructor/" + sideBarItem.name.toLowerCase(),
                )
              : handleLogout(logout);
          }}
          collapseChild={collapse}
        />
      ))}
    </ul>
  );
}

export default SideBar;
