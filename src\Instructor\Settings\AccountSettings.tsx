import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import React, { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import GradientButton from "../../Learner/components/GradientButton";
import Spinner from "../../Learner/components/Spinner";
import { useGetUser } from "../../Learner/hooks/useGetUser";
import { useUpdateUser } from "../../Learner/hooks/useUpdateUser";
import showError from "../../Learner/scripts/showErrorDialog";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import { User } from "../../types/User";
import { useQueryClient } from "@tanstack/react-query";

const formSchema = z.object({
  firstName: z.string().nonempty("First name is required"),
  lastName: z.string(),
  email: z.string().email("Invalid email address"),
  dob: z.string().nonempty("Date of birth is required"),
  organizationName: z.string(),
  race: z.enum(["chinese", "malay", "indian", "others"]).nullable(),
  role: z.enum(["Frontline", "Internal-facing", "Hybrid"]).nullable(),
  organizationType: z
    .enum(["Corporate", "Social Sector", "Government", "School"])
    .nullable(),
});

type FormValues = z.infer<typeof formSchema>;

const AccountSettings: React.FC = () => {
  const mutation = useUpdateUser();
  const queryClient = useQueryClient();
  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
  });

  const onSubmit = (data: FormValues) => {
    console.log(data);
    const userData: User = {
      name: data.firstName,
      last_name: data.lastName,
      email: data.email,
      date_of_birth: data.dob,
      ethnicity: data.race,
      organization_name: data.organizationName,
      role_in_organization: data.role,
      organization_type: data.organizationType,
    };
    mutation.mutate(
      {
        user: userData,
      },
      {
        onSuccess: (data) => {
          queryClient.invalidateQueries({ queryKey: ["user"] });
          showSuccess("Success", "User is successfully updated.");
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };
  const { data: user, isFetching, isError } = useGetUser();

  useEffect(() => {
    if (user) {
      reset({
        firstName: user.name || "",
        lastName: user.last_name || "",
        email: user.email || "",
        dob: user.date_of_birth || "",
        organizationName: user.organization_name || "",
        race: user.ethnicity || "others",
        role: user.role_in_organization || "Frontline",
        organizationType: user.organization_type || "Corporate",
      });
    }
  }, [user, reset]);

  return (
    <>
      <Spinner
        loading={
          (isFetching || mutation.isPending) && !(isError || mutation.isError)
        }
      />
      <div className="bg-yellow-50 p-6">
        <h2 className="mb-4 text-lg font-bold text-blue-800">
          Account Settings
        </h2>
      </div>
      <form
        className="grid grid-cols-2 gap-6 p-6"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex flex-col gap-y-2">
          <label className="text-base font-medium text-[#535353]">
            First Name
          </label>
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => <input {...field} className="border p-2" />}
          />
          {errors.firstName && (
            <p className="text-red-500">{errors.firstName.message}</p>
          )}
        </div>

        <div className="flex flex-col gap-y-2">
          <label className="text-base font-medium text-[#535353]">
            Last Name/Surname
          </label>
          <Controller
            name="lastName"
            control={control}
            render={({ field }) => <input {...field} className="border p-2" />}
          />
          {errors.lastName && (
            <p className="text-red-500">{errors.lastName.message}</p>
          )}
        </div>

        <div className="flex flex-col gap-y-2">
          <label className="text-base font-medium text-[#535353]">
            Email Address
          </label>
          <Controller
            name="email"
            control={control}
            render={({ field }) => <input {...field} className="border p-2" />}
          />
          {errors.email && (
            <p className="text-red-500">{errors.email.message}</p>
          )}
        </div>

        <div className="flex flex-col gap-y-2">
          <label className="text-base font-medium text-[#535353]">
            Date of Birth (DOB)
          </label>
          <Controller
            name="dob"
            control={control}
            render={({ field }) => (
              <input type="date" {...field} className="border p-2" />
            )}
          />
          {errors.dob && <p className="text-red-500">{errors.dob.message}</p>}
        </div>

        <div className="flex flex-col gap-y-2">
          <label className="text-base font-medium text-[#535353]">
            Organization Name
          </label>
          <Controller
            name="organizationName"
            control={control}
            render={({ field }) => <input {...field} className="border p-2" />}
          />
          {errors.organizationName && (
            <p className="text-red-500">{errors.organizationName.message}</p>
          )}
        </div>

        <div className="flex flex-col gap-y-2">
          <label className="text-base font-medium text-[#535353]">Race</label>
          <Controller
            name="race"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                className="border p-2"
                onChange={(e) => field.onChange(e.target.value || null)}
                value={field.value ?? ""}
              >
                <option value="">Select...</option>
                <option value="chinese">Chinese</option>
                <option value="malay">Malay</option>
                <option value="indian">Indian</option>
                <option value="others">Others</option>
              </select>
            )}
          />
          {errors.race && <p className="text-red-500">{errors.race.message}</p>}
        </div>

        <div className="flex flex-col gap-y-2">
          <label className="text-base font-medium text-[#535353]">
            Role in Organisation
          </label>
          <Controller
            name="role"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                className="border p-2"
                onChange={(e) => field.onChange(e.target.value || null)} // Convert empty string to null on change
                value={field.value ?? ""} // Convert null to empty string for the value
              >
                <option value="">Select...</option>
                <option value="Frontline">Frontline</option>
                <option value="Internal-facing">Internal-facing</option>
                <option value="Hybrid">Hybrid</option>
              </select>
            )}
          />
          {errors.role && <p className="text-red-500">{errors.role.message}</p>}
        </div>

        <div className="flex flex-col gap-y-2">
          <label className="text-base font-medium text-[#535353]">
            Organization Type
          </label>
          <Controller
            name="organizationType"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                className="border p-2"
                onChange={(e) => field.onChange(e.target.value || null)} // Convert empty string to null on change
                value={field.value ?? ""} // Convert null to empty string for the value
              >
                <option value="">Select...</option>
                <option value="Corporate">Corporate</option>
                <option value="Social Sector">Social Sector</option>
                <option value="Government">Government</option>
                <option value="School">School</option>
              </select>
            )}
          />
          {errors.organizationType && (
            <p className="text-red-500">{errors.organizationType.message}</p>
          )}
        </div>
      </form>
      <div className="mt-4 p-6">
        <GradientButton
          text="Save"
          color1="#2B3D59"
          color2="#375685"
          width="250px"
          onClick={handleSubmit(onSubmit)}
        />
      </div>
    </>
  );
};

export default AccountSettings;
