import React from "react";
import AccountSettings from "./AccountSettings";
import NameVerification from "./NameVerification";
import PasswordChange from "./PasswordChange";

const SettingsHome: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100 p-6 md:p-10">
      <div className="mx-auto max-w-4xl space-y-8">
        <div className="mb-8">
          <h1 className="mb-2 text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">
            Manage your account settings and preferences
          </p>
        </div>

        <AccountSettings />
        {/* <NameVerification name={"learner"} /> */}
        <PasswordChange />
      </div>
    </div>
  );
};

export default SettingsHome;
