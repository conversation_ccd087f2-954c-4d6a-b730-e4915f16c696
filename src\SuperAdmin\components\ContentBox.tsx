import { ReactNode } from "react";

interface Props {
  children?: ReactNode;
  heading: string;
  icon?: ReactNode;
}

function ContentBox({ children, heading, icon }: Props) {
  return (
    <div className="flex h-full w-full flex-col space-y-9 bg-[#F5DB80] bg-opacity-10 p-[62px]">
      <span className="flex items-center space-x-5">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path
            d="M6.44444 14.2222H9.55556V16H6.44444V14.2222ZM0 1.77778H16V4.88889H0V1.77778ZM2.66667 7.11111H13.3333V10.2222H2.66667V7.11111Z"
            fill="#9A9A9A"
          />
        </svg>
        <h1 className="text-[18px] font-normal text-[#8C8C8C]">
          <span className="text-[18px] font-semibold text-[#9A9A9A]">
            Skillseed
          </span>
          / {heading}
        </h1>
      </span>
      <span className="flex items-center space-x-7">
        {icon ? icon : <img src="/practice-activity-logo.png" alt="Activity logo" />}

        <h1 className="text-3xl font-bold text-[#1c1c1c]">{heading}</h1>
      </span>
      <div className="flex max-h-fit min-h-[85%] w-[90%] flex-col space-y-14 rounded-xl bg-white px-14 py-8">
        {children}
      </div>
    </div>
  );
}

export default ContentBox;
