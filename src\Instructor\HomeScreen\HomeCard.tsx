import React from "react";
import { useNavigate } from "react-router-dom";

interface CardProps {
  title: string;
  buttonText: string;
  imageSrc: string;
  link: string;
}

const Card: React.FC<CardProps> = ({ title, buttonText, imageSrc, link }) => {
  const navigate = useNavigate();
  return (
    <div className="flex rounded-lg bg-gray-100 bg-[url('/course-card-bg.png')] p-4 shadow-md">
      <div className="mb-4 w-1/2 items-start justify-between">
        <img src={imageSrc} alt={title} className="" />
      </div>
      <div className="flex-end w-1/2 self-center">
        <h3 className="mb-2 text-xl font-bold">{title}</h3>
        <button
          onClick={() => navigate("/instructor" + link)}
          className="w-[100px] rounded-lg bg-[#358139] px-4 py-2 text-white"
        >
          {buttonText}
        </button>
      </div>
    </div>
  );
};

export default Card;
