import React, { useState } from "react";

interface FileUploadProps {
  handleFileUpload: (file: File) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ handleFileUpload }) => {
  const [fileName, setFileName] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFileName(file.name);
      handleFileUpload(file); // Pass the file to the parent component
    }
  };

  return (
    <div className="flex flex-col items-start space-y-4">
      {/* Title */}
      <h2 className="text-2xl font-medium text-[#6F6F6F] text-opacity-90">
        User Upload
      </h2>

      {/* Description */}
      <p className="text-xl font-medium text-[#484848]">File Types: excel</p>

      {/* File Upload */}
      <label
        htmlFor="file-upload"
        className="flex w-full cursor-pointer items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-4 hover:bg-gray-50"
      >
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2">
            {/* Upload Icon */}
            <svg
              className="h-6 w-6 text-blue-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16v1a2 2 0 002 2h12a2 2 0 002-2v-1M16 12l-4-4m0 0l-4 4m4-4v12"
              />
            </svg>

            {/* Text */}
            <span className="font-medium text-blue-500">
              {fileName || "Choose a File"}
            </span>
          </div>
        </div>
        {/* Hidden File Input */}
        <input
          id="file-upload"
          type="file"
          accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          className="hidden"
          onChange={handleFileChange}
        />
      </label>
    </div>
  );
};

export default FileUpload;
