import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { ActivityCreateResponse } from "../../types/Activity";
import { AxiosInstance } from "axios";

// api call

const createActivity = async (
  axiosObject: AxiosInstance,
  name: string,
  type: string
): Promise<ActivityCreateResponse> => {
  const data = {
    name: name,
    type: type
  }
  const response = await axiosObject.post<ActivityCreateResponse>(
    "/api/v1/activity/create", data
  );
  console.log(response);
  return response.data;
};

export function useCreateActivity() {
  const axiosInstance = useAxios();
  // run the query
  return useMutation({
    mutationFn: ({ name = "", type = "" }: {
      name: string, type: string
    }) => {
      return createActivity(axiosInstance, name, type);
    },
  })
}
