import React, { useState } from "react";
import RemoveRedEyeOutlinedIcon from "@mui/icons-material/RemoveRedEyeOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import GradientButton from "../../Learner/components/GradientButton";
import { useChangePassword } from "../../Learner/hooks/useChangePassword";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import showError from "../../Learner/scripts/showErrorDialog";
import Spinner from "../../Learner/components/Spinner";

const PasswordChange: React.FC = () => {
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [rePassword, setRePassword] = useState("");
  const [showOldPassword, setShowOldPassword] = useState(false); // Toggle for old password visibility
  const [showNewPassword, setShowNewPassword] = useState(false); // Toggle for new password visibility
  const [showRePassword, setShowRePassword] = useState(false); // Toggle for retype password visibility
  const mutation = useChangePassword();

  const handleSubmit = async () => {
    if (oldPassword && newPassword && rePassword) {
      if (newPassword === rePassword) {
        if (newPassword.length < 8) {
          showError(
            "Failed",
            "New password should be atleast 8 characters long.",
          );
          return;
        }
        mutation.mutate(
          { oldPassword, newPassword },
          {
            onSuccess: (data) => {
              if (data && data.message) {
                if (data.success) {
                  showSuccess(data.message, "");
                } else {
                  showError(data.message, "");
                }
              }
            },
            onError: (error) => {
              showError("Failed", "Please Check Credentials and try again.");
              console.log("Submission failed. Please try again.");
            },
          },
        );
      } else {
        showError("Failed", "New password and Retyped password are not same.");
      }
    } else {
      showError("Failed", "Please enter valid values.");
    }
  };

  return (
    <>
      <Spinner loading={mutation.isPending} />
      <div className="bg-yellow-50 p-6">
        <h2 className="mb-4 text-lg font-bold text-blue-800">Password</h2>
        <div className="grid grid-cols-3 gap-4">
          {/* Current Password Field */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700">
              Current Password
            </label>
            <input
              type={showOldPassword ? "text" : "password"}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 shadow-sm"
              onChange={(evt) => setOldPassword(evt.target.value)}
            />
            {/* Eye icon to toggle password visibility */}
            <span
              className="absolute right-3 top-8 cursor-pointer"
              onClick={() => setShowOldPassword(!showOldPassword)}
            >
              {showOldPassword ? (
                <VisibilityOffOutlinedIcon style={{ color: "grey" }} />
              ) : (
                <RemoveRedEyeOutlinedIcon style={{ color: "grey" }} />
              )}
            </span>
          </div>

          {/* New Password Field */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700">
              New Password
            </label>
            <input
              type={showNewPassword ? "text" : "password"}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 shadow-sm"
              onChange={(evt) => setNewPassword(evt.target.value)}
            />
            {/* Eye icon to toggle password visibility */}
            <span
              className="absolute right-3 top-8 cursor-pointer"
              onClick={() => setShowNewPassword(!showNewPassword)}
            >
              {showNewPassword ? (
                <VisibilityOffOutlinedIcon style={{ color: "grey" }} />
              ) : (
                <RemoveRedEyeOutlinedIcon style={{ color: "grey" }} />
              )}
            </span>
          </div>

          {/* Retype Password Field */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700">
              Retype Password
            </label>
            <input
              type={showRePassword ? "text" : "password"}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2 shadow-sm"
              onChange={(evt) => setRePassword(evt.target.value)}
            />
            {/* Eye icon to toggle password visibility */}
            <span
              className="absolute right-3 top-8 cursor-pointer"
              onClick={() => setShowRePassword(!showRePassword)}
            >
              {showRePassword ? (
                <VisibilityOffOutlinedIcon style={{ color: "grey" }} />
              ) : (
                <RemoveRedEyeOutlinedIcon style={{ color: "grey" }} />
              )}
            </span>
          </div>
        </div>

        <GradientButton
          text="Change Password"
          color1="#2B3D59"
          color2="#375685"
          width="250px"
          onClick={handleSubmit}
        />
      </div>
    </>
  );
};

export default PasswordChange;
