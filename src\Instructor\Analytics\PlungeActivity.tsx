import React from 'react';
import { useGetPlungeAnalytics } from '../hooks/useGetPlungeAnalytics';
import Spinner from '../../Learner/components/Spinner';

const PlungeActivityTable: React.FC = () => {
  const { data: plunge, isFetching } = useGetPlungeAnalytics();
  return (
    <>
      <Spinner loading={isFetching} />
      <div className="p-4 bg-white rounded-lg shadow-md">
        <table className="w-full text-left table-auto">
          <thead>
            <tr className="bg-gray-100">
              <th className="p-2 font-medium">Metric</th>
              <th className="p-2 font-medium">Quantity</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b">
              <td className="p-2">Most Used Phrases</td>
              <td className="p-2">{plunge?.data.most_used_pharase}</td>
            </tr>
            <tr className="border-b">
              <td className="p-2">Average time to completion</td>
              <td className="p-2">{plunge?.data.average_time_to_complete}</td>
            </tr>
            <tr className="border-b">
              <td className="p-2">Reply Times</td>
              <td className="p-2">{plunge?.data.total_replay_time}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </>
  );
};

export default PlungeActivityTable;
