import { useQuery } from "@tanstack/react-query";
import { AxiosInstance } from "axios";
import useAxios from "../../hooks/axiosObject";

export interface ActivityDetail {
  id: string;
  name: string;
  type: string;
  result: string;
}

export interface UserDetailedResult {
  userId: string;
  userName: string;
  courseId: string;
  courseName: string;
  activities: ActivityDetail[];
  overallResult: string;
}

// Simulated API call with delay
const getUserDetailedResults = async (
  axiosObject: AxiosInstance,
  userId: string,
  courseId: string,
): Promise<UserDetailedResult> => {
  // In the future, this would be a real API call:
  console.log(userId, courseId);
  const [aiActivityResponse, quizResponse, userResponse] = await Promise.all([
    axiosObject.get<any>(
      `api/v1/activity-progress/get/result?user_id=${userId}&activity_type=practice_activity`,
    ),
    axiosObject.get<any>(
      `api/v1/activity-progress/get/result?user_id=${userId}&activity_type=quiz`,
    ),
    axiosObject.get<any>(`api/v1/user/get/${userId}`),
  ]);

  console.log("userResponse", userResponse.data);
  // return response.data;
  const activityData1 = aiActivityResponse.data.map((item: any) => {
    const listeningSum = Object.values(item.result.listening.listening).reduce(
      (a: any, b: any) => a + b,
      0,
    );
    console.log(listeningSum);

    return {
      id: item.id,
      name: "AI Activity",
      type: "AI Activity",
      result: listeningSum,
    };
  });

  const activityData = quizResponse.data.map((item: any) => {
    return {
      id: item.id,
      name: "Quiz",
      type: "Quiz",
      result: item.result.percentage,
    };
  });

  // For now, return mock data
  // const key = `${userId}_${courseId}`;
  // if (mockDetailedResults[key]) {
  //   return mockDetailedResults[key];ó
  // }

  // Return a default if not found
  return {
    userId,
    userName: userResponse.data.data.email,
    courseId,
    courseName: "Unknown Course",
    activities: activityData1.concat(activityData) as ActivityDetail[],
    overallResult: "0%",
  };
};

export function useGetUserDetailedResults(userId: string, courseId: string) {
  const axiosObject = useAxios();

  return useQuery<UserDetailedResult>({
    queryKey: ["userDetailedResults", userId, courseId],
    refetchOnWindowFocus: false,
    queryFn: () => getUserDetailedResults(axiosObject, userId, courseId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!userId && !!courseId,
  });
}
