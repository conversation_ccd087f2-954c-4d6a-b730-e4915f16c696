import { Fragment, useEffect, useState, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { useDrag } from "react-dnd";
import CourseCard from "../CourseManagement/CourseCard";
import DraggableActivityList, {
  SIDEBAR_ACTIVITY,
  SidebarDragItem,
} from "./DraggableActivityList";
import { ActivityName } from "../../types/Activity";
import { useCreateActivity } from "../hooks/useCreateActivity";
import { useDeleteActivity } from "../hooks/useDeleteActivity";
import { useGetCourse } from "../hooks/useGetCourse";
import { useUpdateCourse } from "../hooks/useUpdateCourse";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import showError from "../../Learner/scripts/showErrorDialog";
import Spinner from "../../Learner/components/Spinner";

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

interface ActivityType {
  name: string;
  type: string;
}

const activityTypes: ActivityType[] = [
  {
    name: "Hardware Test",
    type: "hardware_test",
  },
  {
    name: "Pre Course Survey",
    type: "pre_course_survey",
  },
  {
    name: "Quiz",
    type: "quiz",
  },
  {
    name: "Tutorial",
    type: "tutorial",
  },
  {
    name: "Plunge Activity",
    type: "plunge_activity",
  },
  {
    name: "Practice Activity",
    type: "practice_activity",
  },
  {
    name: "Recap",
    type: "recap",
  },
  {
    name: "Boss Challenge",
    type: "boss_challenge",
  },
  {
    name: "Feedback",
    type: "feedback",
  },
  {
    name: "Additional Resources",
    type: "additional_resources",
  },
];

// Draggable sidebar item component
interface DraggableSidebarItemProps {
  item: ActivityType;
  isSelected: boolean;
  onClick: () => void;
}

const DraggableSidebarItem: React.FC<DraggableSidebarItemProps> = ({
  item,
  isSelected,
  onClick,
}) => {
  const ref = useRef<HTMLHeadingElement>(null);

  const [{ isDragging }, drag] = useDrag<
    SidebarDragItem,
    void,
    { isDragging: boolean }
  >({
    type: SIDEBAR_ACTIVITY,
    item: {
      activityType: item.type,
      activityName: item.name,
      type: SIDEBAR_ACTIVITY,
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(ref);

  return (
    <h1
      ref={ref}
      onClick={onClick}
      className={`cursor-pointer text-lg font-medium transition-all duration-200 hover:text-[#36537F] ${
        isSelected ? "text-[#36537F] underline" : "text-[#777777]"
      } ${isDragging ? "opacity-50" : ""}`}
      style={{ opacity: isDragging ? 0.5 : 1 }}
    >
      {item.name}
      {isDragging && (
        <span className="ml-2 text-sm text-blue-500">(dragging...)</span>
      )}
    </h1>
  );
};

function CourseDevelopment() {
  const [allActivityTypes, setAllActivityTypes] =
    useState<ActivityType[]>(activityTypes);
  const [selectedModule, setSelectedModule] = useState<ActivityType | null>(
    null,
  );
  const [activities, setActivities] = useState<ActivityName[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [activitiesUpdated, setActivitiesUpdated] = useState(false);
  const query = useQueryParam();
  const course_id = query.get("course_id") ?? "";
  const navigate = useNavigate();

  const handleModuleClick = (module: ActivityType) => {
    setSelectedModule(module);
  };

  // const { data: allActivities, isLoading, isFetching, isError } = useGetAllActivities();
  const { data: course, isFetching: isFetchingCourse } =
    useGetCourse(course_id);
  const mutation = useUpdateCourse();
  const activityMutation = useCreateActivity();
  const activityDelete = useDeleteActivity();

  useEffect(() => {
    if (course && course.activities) {
      setActivities(course.activities);
    }
  }, [course]);

  useEffect(() => {
    if (activitiesUpdated) {
      // handleUpdateCourse();
      setActivitiesUpdated(false); // Reset the flag after the update
    }
  }, [activitiesUpdated]);

  const handleAddActivity = () => {
    if (!selectedModule) {
      showError("Error", "Please select a module first!");
      // setModalMessage("Please select a module first!");
      // setShowModal(true);
      return;
    }
    activityMutation.mutate(
      {
        name: selectedModule.name,
        type: selectedModule.type,
      },
      {
        onSuccess: (data) => {
          if (selectedIndex !== null) {
            setActivities((prevActivities) => {
              const updatedActivities = [...prevActivities];
              updatedActivities.splice(selectedIndex + 1, 0, data.data);
              setActivitiesUpdated(true);
              return updatedActivities;
            });
          } else {
            setActivities((prevActivities) => [...prevActivities, data.data]);
            setActivitiesUpdated(true);
          }
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
    setSelectedModule(null); // Reset selected module after adding
  };

  const handleEditActivity = () => {
    if (selectedIndex === null) {
      showError("Error", "Please select a module first!");
      // setModalMessage("Please select a module first!");
      // setShowModal(true);
      return;
    }
    navigate(
      `/instructor/activity-development?activity_id=${activities[selectedIndex].id}&course_id=${course_id}`,
    );
  };

  const handleDeleteActivity = () => {
    if (activities.length === 1) {
      showError("Error", "All activities are deleted!");
      return;
    } else if (selectedIndex === null) {
      showError("Error", "Please select activity to delete!");
      return;
    }
    console.log(selectedIndex);
    if (selectedIndex) {
      activityDelete.mutate(
        {
          activityId: activities[selectedIndex].id.toLocaleString(),
        },
        {
          onSuccess: (data) => {
            if (selectedIndex !== null) {
              setActivities((prevActivities) => {
                const updatedActivities = [...prevActivities];
                updatedActivities.splice(selectedIndex, 1);
                setActivitiesUpdated(true);
                return updatedActivities;
              });
            }
          },
          onError: () => {
            showError("Submission failed", "Please try again.");
            console.log("Submission failed. Please try again.");
          },
        },
      );
    }
  };

  const handleUpdateCourse = () => {
    mutation.mutate(
      {
        courseId: course_id,
        name: "",
        activities: activities.map((e) => e.id),
      },
      {
        onSuccess: (data) => {
          showSuccess("Success", "Course Updated Successfully!");
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  const handleSelectActivity = (index: number) => {
    console.log(index);
    setSelectedIndex(index);
    const newActivities = activities.map((e, i) => {
      return { ...e, selected: i === index ? true : false };
    });
    setActivities([...newActivities]);
  };

  // Handle activity reordering
  const handleMoveActivity = (newActivities: ActivityName[]) => {
    setActivities(newActivities);
    setActivitiesUpdated(true);
  };

  // Handle adding activity via drag and drop
  const handleAddActivityViaDrop = (
    activityType: string,
    activityName: string,
    insertIndex: number,
  ) => {
    activityMutation.mutate(
      {
        name: activityName,
        type: activityType,
      },
      {
        onSuccess: (data) => {
          setActivities((prevActivities) => {
            const updatedActivities = [...prevActivities];
            updatedActivities.splice(insertIndex, 0, data.data);
            setActivitiesUpdated(true);
            return updatedActivities;
          });
          showSuccess("Success", "Activity added successfully!");
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  return (
    <>
      <Spinner
        loading={
          isFetchingCourse ||
          mutation.isPending ||
          activityMutation.isPending ||
          activityDelete.isPending
        }
      />
      <DndProvider backend={HTML5Backend}>
        <Fragment>
          <div className="flex flex-col">
            <div className="flex w-full flex-row items-center justify-between rounded bg-[#EDEDED] px-8 py-3 text-xl font-semibold text-[#36537F]">
              {course?.name}
              <button
                onClick={() => navigate(-1)}
                className="items-center rounded-lg bg-[#36537F] px-8 py-2 text-center text-white hover:bg-blue-600"
              >
                Back
              </button>
            </div>
            <div className="flex w-full bg-[#FEFBF2] bg-opacity-70">
              <div className="flex h-[745px] w-4/12 flex-col justify-between rounded bg-[#F9F0F0] p-11">
                <div className="max-h-[585px] space-y-10 overflow-y-auto">
                  <h1 className="text-xl font-semibold text-[#36537F]">
                    Modules
                  </h1>
                  <ul className="snap-y space-y-6 overflow-y-auto">
                    {allActivityTypes &&
                      allActivityTypes.map((item) => (
                        <DraggableSidebarItem
                          key={item.type}
                          item={item}
                          isSelected={selectedModule === item}
                          onClick={() => handleModuleClick(item)}
                        />
                      ))}
                  </ul>
                  <div className="mt-4 text-sm text-gray-600">
                    <p>
                      💡 Tip: You can drag activities directly into the course
                      list!
                    </p>
                  </div>
                </div>
                <button
                  className="h-[40px] w-[195px] rounded-lg bg-[#36537F] px-10 text-center text-lg font-medium text-white hover:bg-blue-400 sm:text-sm"
                  onClick={handleAddActivity}
                >
                  Add Activity
                </button>
              </div>
              <div className="flex h-[745px] w-8/12 flex-col items-center justify-between rounded p-11">
                <div className="h-[585px] max-h-[730px] w-full overflow-y-auto">
                  {/* Use the traditional CourseCard when there are no activities */}
                  {activities.length === 0 ? (
                    <CourseCard
                      title=""
                      activities={activities}
                      onSelectClick={handleSelectActivity}
                      hideEditButton={true}
                    />
                  ) : (
                    <div className="w-full">
                      <div className="mb-4 text-center text-sm text-gray-600">
                        Drag and drop activities to reorder them, or drag from
                        sidebar to add new ones
                      </div>
                      <DraggableActivityList
                        activities={activities}
                        onMoveActivity={handleMoveActivity}
                        onSelectClick={handleSelectActivity}
                        onAddActivity={handleAddActivityViaDrop}
                      />
                    </div>
                  )}
                </div>
                <div className="flex w-full items-center justify-between">
                  <button
                    onClick={handleEditActivity}
                    className="mx-2 h-[40px] flex-grow rounded-lg bg-[#36537F] text-center text-lg font-medium text-white hover:bg-blue-400 sm:text-sm"
                  >
                    Edit Activity
                  </button>
                  <button
                    onClick={handleDeleteActivity}
                    className="mx-2 h-[40px] flex-grow rounded-lg bg-[#36537F] text-center text-lg font-medium text-white hover:bg-blue-400 sm:text-sm"
                  >
                    Delete Activity
                  </button>
                  <button
                    onClick={handleUpdateCourse}
                    className="mx-2 h-[40px] flex-grow rounded-lg bg-[#EEC300] text-center text-lg font-medium text-white hover:bg-yellow-300 sm:text-sm"
                  >
                    Save Order
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Fragment>
      </DndProvider>
    </>
  );
}

export default CourseDevelopment;
