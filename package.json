{"name": "skillseed-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@heygen/streaming-avatar": "^2.0.16", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "@mui/x-charts": "^7.6.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-form": "^1.14.1", "@tanstack/react-query": "^5.51.21", "@tanstack/react-query-devtools": "^5.51.1", "@types/styled-components": "^5.1.34", "axios": "^1.7.2", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "dotenv": "^16.4.5", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.477.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^18.2.0", "react-day-picker": "^9.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-media-recorder": "^1.6.6", "react-quill": "^2.0.0", "react-resizable-panels": "^3.0.3", "react-router-dom": "^6.23.1", "react-spinners": "^0.13.8", "recharts": "^2.12.7", "sonner": "^2.0.5", "styled-components": "^6.1.11", "sweetalert2": "^11.12.3", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.5"}, "devDependencies": {"@types/lodash": "^4.17.7", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "prettier": "^3.3.2", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.2.0"}}