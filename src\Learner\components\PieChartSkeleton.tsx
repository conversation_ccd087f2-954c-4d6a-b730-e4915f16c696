import React from "react";
import { Box, Skeleton, Typography } from "@mui/material";


const PieChartSkeleton: React.FC = () => {
  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      height="100%"
      width="100%"
    >
      <Skeleton variant="circular" width={200} height={200} animation="wave">
        <Typography
          variant="h6"
          component="div"
          position="absolute"
          visibility={"visible"}
          top="50%"
          left="50%"
          sx={{
            transform: "translate(-50%, -50%)",
          }}
        >
          No Sentiment Analysis
        </Typography>
      </Skeleton>
    </Box>
  );
};

export default PieChartSkeleton;
