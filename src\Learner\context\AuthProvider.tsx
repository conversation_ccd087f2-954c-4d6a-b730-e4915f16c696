import React, { createContext, useState, use<PERSON>ontext, ReactNode } from "react";
import axios from "axios";

interface AuthContextType {
  token: string | null;
  userType: string | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
}

const redirectDict = {
  "/instructor": "instructor",
  "/admin": "admin",
  "/superadmin": "superadmin",
  "/home-screen": "learner",
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [token, setToken] = useState<string | null>(
    localStorage.getItem("token"),
  );

  const [userType, setUserType] = useState<string | null>(
    localStorage.getItem("userType"),
  );

  const login = async (username: string, password: string) => {
    try {
      const axiosInstance = axios.create({
        baseURL: import.meta.env.VITE_BACKENDURL, // Replace with your API base URL
      });
      const response = await axiosInstance.post("/api/v1/auth/login", {
        username,
        password,
      });
      const newToken = response.data.data.access_token;
      setToken(newToken);
      localStorage.setItem("token", newToken);
      localStorage.setItem(
        "userType",
        redirectDict[
          response.data.data.redirect_page as keyof typeof redirectDict
        ] || "learner",
      );
      setUserType(
        redirectDict[
          response.data.data.redirect_page as keyof typeof redirectDict
        ] || "learner",
      );
    } catch (error) {
      console.error("Login failed", error);
      throw error;
    }
  };

  const logout = () => {
    setToken(null);
    setUserType(null);
    localStorage.removeItem("token");
    localStorage.removeItem("userType");
    // Use history API instead of direct location change
    // This is more compatible with client-side routing
    window.history.pushState({}, "", "/login");
    // Trigger a navigation event so React Router can detect the change
    window.dispatchEvent(new PopStateEvent("popstate"));
  };

  return (
    <AuthContext.Provider value={{ token, userType, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
