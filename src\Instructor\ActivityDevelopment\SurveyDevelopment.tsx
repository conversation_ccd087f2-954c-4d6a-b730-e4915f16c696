import React, { useState } from 'react';
import { FeedbackQuestions } from '../../types/Activity';

interface EditQuestionListProps {
  surveyType: string;
  initialQuestions: any[];
  onSave: (questions: any[]) => void;
}

const EditSurveyQuestionList: React.FC<EditQuestionListProps> = ({ surveyType, initialQuestions, onSave }) => {
  const [questions, setQuestions] = useState<any[]>(initialQuestions);

  const handleQuestionChange = (index: number, value: string) => {
    const updatedQuestions = questions.map((q, i) => (i === index ? value : q));
    setQuestions(updatedQuestions);
  };

  const handleAddQuestion = () => {
    setQuestions([...questions, '']);
  };

  const handleRemoveQuestion = (index: number) => {
    setQuestions(questions.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    onSave(questions);
  };

  return (
    <div className="w-full mx-auto p-6 bg-gray-100 rounded-lg">
      <h2 className="text-2xl font-bold mb-4">
        Edit {surveyType === "pre_course_survey"? "Pre Course Survey": "Feedback"} Questions
      </h2>

      {questions.map((question, index) => (
        <div key={index} className="mb-4 p-4 bg-white shadow rounded-lg flex items-center justify-between">
          <input
            type="text"
            value={question}
            onChange={(e) => handleQuestionChange(index, e.target.value)}
            className="w-full p-2 border border-gray-300 rounded mr-4"
            placeholder={`Question ${index + 1}`}
          />
          <button
            onClick={() => handleRemoveQuestion(index)}
            className="text-red-600 hover:text-red-800"
          >
            Remove
          </button>
        </div>
      ))}

      <div className="flex justify-between items-center mt-4">
        <button
          onClick={handleAddQuestion}
          className="mt-6 ml-4 px-4 py-2 bg-[#EEC300] text-white rounded-md w-[200px]"
        >
          Add Question
        </button>
        <button
          onClick={handleSave}
          className="mt-6 ml-4 px-4 py-2 bg-[#EEC300] text-white rounded-md w-[200px]"
        >
          Save All
        </button>
      </div>
    </div>
  );
};

export default EditSurveyQuestionList;
