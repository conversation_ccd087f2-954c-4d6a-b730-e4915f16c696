import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { Course } from "../../types/Course";
import { AxiosInstance } from "axios";

// api call

const getCourse = async (
  axiosObject: AxiosInstance,
  course_id: string,
): Promise<Course> => {
  const course = await axiosObject.get<Course>(
    "/api/v1/course/get/" + course_id,
  );
  console.log(course);
  return course.data;
};

export function useGetCourse(course_id: string) {
  const axiosObject = useAxios();
  // run the query
  return useQuery<Course>({
    queryKey: ["course"],
    refetchOnWindowFocus: false,
    queryFn: () => getCourse(axiosObject, course_id),
  });
}
