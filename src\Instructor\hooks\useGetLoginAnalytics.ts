import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { LoginUserResponse } from "../../types/Analytics";
import { AxiosInstance } from "axios";

// api call

const getAnalytics = async (
  axiosObject: AxiosInstance,
): Promise<LoginUserResponse> => {
  const data = await axiosObject.get<LoginUserResponse>(
    "/api/v1/analytics/instructor/login_user",
  );
  console.log(data);
  return data.data;
};

export function useGetLoginAnalytics() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<LoginUserResponse>({
    queryKey: ["loginAnalytics"],
    refetchOnWindowFocus: false,
    queryFn: () => getAnalytics(axiosObject),
  });
}
