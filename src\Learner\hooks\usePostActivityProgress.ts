import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";

import {
  ActivityProgress,
  ActivityProgressData,
} from "../../types/ActivityProgress";
import { AxiosInstance } from "axios";

// api call
export async function postActivityProgress(
  axiosObject: AxiosInstance,
  course_id: string,
  activity_id: string,
  user_input: string,
  video: Blob | null,
  videoTag: string,
): Promise<ActivityProgress> {
  var formData = new FormData();
  formData.append("course_id", course_id);
  formData.append("activity_id", activity_id);
  if (user_input !== "") {
    console.log(user_input);
    formData.append("user_input", user_input);
  }
  if (video !== null) {
    formData.append("video_file", video);
  }
  if (videoTag !== "") {
    formData.append("video_tag", videoTag);
  }
  const activity_progress = await axiosObject.post<ActivityProgressData>(
    "/api/v1/activity-progress/create",
    formData,
  );
  console.log("respondng: ", activity_progress);
  return activity_progress.data.data;
}

export const usePostActivityProgress = (videoTag: string = "") => {
  const axiosInstance = useAxios();
  return useMutation({
    mutationFn: ({
      courseId,
      activityId,
      userInput,
      video = null,
    }: {
      courseId: string;
      activityId: string;
      userInput: string;
      video?: Blob | null;
    }) => {
      return postActivityProgress(
        axiosInstance,
        courseId,
        activityId,
        userInput,
        video,
        videoTag,
      );
    },
  });
};
