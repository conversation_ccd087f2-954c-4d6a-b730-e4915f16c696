import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { AxiosInstance } from "axios";
import { Cohort } from "../../types/Cohort";

// api call

const getCohorts = async (
  axiosObject: AxiosInstance,
): Promise<Cohort[]> => {
  const cohorts = await axiosObject.get<Cohort[]>(
    "/api/v1/cohort/get/all",
  );
  console.log(cohorts);
  return cohorts.data;
};

export function useGetCohorts() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<Cohort[]>({
    queryKey: ["cohorts"],
    refetchOnWindowFocus: false,
    queryFn: () => getCohorts(axiosObject),
    staleTime: Infinity,
    gcTime: Infinity,
  });
}
