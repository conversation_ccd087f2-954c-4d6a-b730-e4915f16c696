import { useNavigate } from "react-router-dom";
import "../components/ResponsiveStyles.css";

function WelcomeScreen() {
  const navigate = useNavigate();
  return (
    <div className="flex h-screen">
      {/* Left side with image and logo */}
      <div className="h-screen w-1/2 bg-gray-100">
        <div className="text-center">
          <img
            src="welcome-background.png"
            alt="SkillSeed Logo"
            className="h-screen w-full object-cover"
          />
        </div>
      </div>

      {/* Right side with form */}
      <div className="flex w-1/2 items-center justify-around">
        <div className="w-3/4">
          <div className="mb-72 flex flex-1 justify-end">
            <p className="text-gray-600">
              Already have an account?{" "}
              <button
                className="h-14 w-40 rounded-lg border-2 border-black text-center text-blue-500"
                onClick={() => navigate("sign-in")}
              >
                Sign In
              </button>
            </p>
          </div>
          <h2 className="mb-4 text-center text-3xl font-bold">Welcome Back!</h2>
          <p className="mb-8 h-[88px] w-[576px] text-center text-2xl text-gray-600">
            Your profile is incomplete. Please complete your profile details to
            get started!
          </p>
          <button
            className="mb-4 h-[84px] w-[636px] rounded-lg bg-yellow-500 px-4 py-2 text-4xl text-black"
            onClick={() => navigate("home-screen")}
          >
            Continue
          </button>
          <div className="text-center">
            <p>
              Need help?{" "}
              <a href="mailto:<EMAIL>" className="text-blue-500">
                Click here
              </a>{" "}
              or email us at:{" "}
              <a href="mailto:<EMAIL>" className="text-blue-500">
                <EMAIL>
              </a>
            </p>
          </div>
          <p className="mt-4 text-xs text-gray-500">
            By continuing, you agree to our{" "}
            <a href="#" className="text-blue-500">
              Privacy Policy
            </a>
            ,{" "}
            <a href="#" className="text-blue-500">
              Terms & Conditions
            </a>
            , and FCRA Disclaimer. Please see our Fraud Disclaimer.
          </p>
        </div>
      </div>
    </div>
  );
}

export default WelcomeScreen;
