import React, { useState } from "react";

const NotificationPage = () => {
  const [message, setMessage] = useState("");
  const [selectedCohort, setSelectedCohort] = useState("Cohort 1");
  const [emailChecked, setEmailChecked] = useState(false);
  const [smsChecked, setSmsChecked] = useState(false);

  const handleSend = () => {
    // Handle send logic
    console.log({
      message,
      selectedCohort,
      emailChecked,
      smsChecked,
    });
  };

  return (
    <div className="mx-auto w-full rounded-lg bg-gray-50 p-6 shadow">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-700">Insert Message</h2>
      </div>

      <div className="mb-4 grid grid-cols-1 gap-4">
        <div className="flex items-center justify-between">
          <label className="font-semibold text-gray-700">Message</label>
          <div className="flex items-center space-x-2">
            <label className="font-semibold text-gray-700">
              Select Cohort:
            </label>
            <select
              value={selectedCohort}
              onChange={(e) => setSelectedCohort(e.target.value)}
              className="rounded-md border bg-white p-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option>Cohort 1</option>
              <option>Cohort 2</option>
              <option>Cohort 3</option>
            </select>
            <input
              type="text"
              placeholder="search"
              className="rounded-md border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type here..."
          className="w-full rounded-md border bg-white p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={5}
        />
      </div>

      <div className="mb-6 flex items-center space-x-4">
        <label className="font-semibold text-gray-700">
          <input
            type="checkbox"
            checked={emailChecked}
            onChange={() => setEmailChecked(!emailChecked)}
            className="mr-2"
          />
          Email
        </label>
        <label className="font-semibold text-gray-700">
          <input
            type="checkbox"
            checked={smsChecked}
            onChange={() => setSmsChecked(!smsChecked)}
            className="mr-2"
          />
          SMS
        </label>
      </div>

      <div>
        <button
          onClick={handleSend}
          className="rounded-md bg-[#36537F] px-6 py-2 font-semibold text-white transition duration-300 hover:bg-blue-600"
        >
          Send
        </button>
      </div>
    </div>
  );
};

export default NotificationPage;
