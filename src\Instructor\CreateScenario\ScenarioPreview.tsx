import React from "react";
import AvatarPreview from "./AvatarPreview";

interface ScenarioPreviewProps {
  avatarId: string;
  selectedScenario: string;
  onBack: () => void;
  onProcess: () => void;
  setSelectedScenario: (scenario: string) => void;
  activityType: string;
}

const ScenarioPreview: React.FC<ScenarioPreviewProps> = ({
  avatarId,
  selectedScenario,
  onBack,
  onProcess,
  setSelectedScenario,
  activityType,
}) => {
  return (
    <div className="mx-auto max-w-4xl p-6">
      <h2 className="mb-6 text-2xl font-semibold">
        {activityType === "tutorial" ? "Preview Script" : "Preview Scenario"}
      </h2>

      <div className="mb-8">
        <h3 className="mb-4 text-xl font-medium">Selected Avatar</h3>
        <div className="mx-auto max-w-md rounded-lg border border-gray-200 p-4">
          <AvatarPreview avatarId={avatarId} />
        </div>
      </div>

      <div className="mb-8">
        <h3 className="mb-4 text-xl font-medium">Custom Script</h3>
        <div className="rounded-lg border border-gray-200 p-4">
          <textarea
            value={selectedScenario}
            rows={6}
            aria-multiline={true}
            onChange={(e) => setSelectedScenario(e.target.value)}
            className="h-fit w-full resize-none p-2 text-gray-800"
          />
        </div>
      </div>

      <div className="mt-8 flex justify-between">
        <button
          onClick={onBack}
          className="rounded-md border border-gray-300 px-6 py-2 text-gray-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Back
        </button>
        <button
          onClick={onProcess}
          className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Process
        </button>
      </div>
    </div>
  );
};

export default ScenarioPreview;
