import React, { useEffect, useImperative<PERSON><PERSON><PERSON>, forwardRef } from "react";
import { useF<PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON><PERSON>, Button, Box, IconButton } from "@mui/material";
import { Add as AddIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { PracticeActivityData } from "../../types/Activity";

// Zod validation schema
const practiceActivitySchema = z.object({
  interlocutorProfile: z
    .string()
    .min(1, "Interlocutor Profile is required")
    .max(1000, "Interlocutor Profile must be less than 1000 characters"),
  scenarioDescription: z
    .string()
    .min(1, "Scenario Description is required")
    .max(2000, "Scenario Description must be less than 2000 characters"),
  evaluationRubric: z.object({
    criteria: z
      .array(
        z.object({
          name: z
            .string()
            .min(1, "Criterion name is required")
            .max(200, "Criterion name must be less than 200 characters"),
          questions: z
            .array(
              z.object({
                question: z
                  .string()
                  .min(1, "Question is required")
                  .max(500, "Question must be less than 500 characters"),
              }),
            )
            .min(1, "At least one question is required per criterion")
            .max(3, "Maximum 3 questions per criterion"),
        }),
      )
      .min(1, "At least one criterion is required")
      .max(3, "Maximum 3 criteria allowed"),
  }),
});

type FormValues = z.infer<typeof practiceActivitySchema>;

export interface PracticeActivityFormRef {
  validateAndGetData: () => { isValid: boolean; data?: PracticeActivityData };
  triggerValidation: () => Promise<boolean>;
}

interface PracticeActivityFormProps {
  initialData?: PracticeActivityData;
  onSave: (data: PracticeActivityData) => void;
  onValidate?: (isValid: boolean, data?: PracticeActivityData) => void;
  isLoading?: boolean;
}

const PracticeActivityForm = forwardRef<
  PracticeActivityFormRef,
  PracticeActivityFormProps
>(({ initialData, onSave, onValidate, isLoading = false }, ref) => {
  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    getValues,
    trigger,
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(practiceActivitySchema),
    mode: "onChange",
    defaultValues: initialData,
  });

  // Reset form when initialData changes
  useEffect(() => {
    if (initialData) {
      console.log(
        "PracticeActivityForm: Resetting form with initialData:",
        initialData,
      );
      reset(initialData);
    }
  }, [initialData, reset]);

  // Expose validation methods via ref
  useImperativeHandle(
    ref,
    () => ({
      validateAndGetData: () => {
        const data = getValues();
        console.log("PracticeActivityForm: validateAndGetData called");
        console.log("PracticeActivityForm: Current form data:", data);
        console.log("PracticeActivityForm: Form is valid:", isValid);
        console.log("PracticeActivityForm: Form errors:", errors);
        return {
          isValid,
          data: isValid ? (data as PracticeActivityData) : undefined,
        };
      },
      triggerValidation: async () => {
        return await trigger();
      },
    }),
    [isValid, getValues, trigger, errors],
  );

  // Call onValidate when validation state changes
  useEffect(() => {
    if (onValidate) {
      const data = getValues();
      onValidate(isValid, isValid ? (data as PracticeActivityData) : undefined);
    }
  }, [isValid, onValidate, getValues]);

  const {
    fields: criteriaFields,
    append: appendCriterion,
    remove: removeCriterion,
  } = useFieldArray({
    control,
    name: "evaluationRubric.criteria",
  });

  console.log(
    "PracticeActivityForm: criteriaFields length:",
    criteriaFields.length,
  );
  console.log("PracticeActivityForm: criteriaFields:", criteriaFields);

  const onSubmit = (data: FormValues) => {
    onSave(data as PracticeActivityData);
  };

  const addCriterion = () => {
    if (criteriaFields.length < 3) {
      appendCriterion({
        name: "",
        questions: [{ question: "" }],
      });
    }
  };

  const removeCriterionHandler = (index: number) => {
    if (criteriaFields.length > 1) {
      removeCriterion(index);
    }
  };

  return (
    <Box
      sx={{
        width: "100%",
        margin: "auto",
        padding: "20px",
        backgroundColor: "#FAF7F0",
        borderRadius: "8px",
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Interlocutor Profile Section */}
        <div>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold" }}>
            1. Interlocutor Profile *
          </Typography>
          <Typography variant="body2" sx={{ mb: 2, color: "#666" }}>
            Describe the person the learner will interact with in the simulation
          </Typography>
          <Controller
            name="interlocutorProfile"
            control={control}
            render={({ field }) => (
              <textarea
                {...field}
                className="w-full rounded-md border border-gray-300 p-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={4}
                placeholder="Describe the interlocutor's role, background, personality, and any relevant characteristics..."
              />
            )}
          />
          {errors.interlocutorProfile && (
            <p className="mt-1 text-sm text-red-600">
              {errors.interlocutorProfile.message}
            </p>
          )}
        </div>

        {/* Scenario Description Section */}
        <div>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold" }}>
            2. Scenario Description *
          </Typography>
          <Typography variant="body2" sx={{ mb: 2, color: "#666" }}>
            Provide a detailed explanation of the practice scenario context
          </Typography>
          <Controller
            name="scenarioDescription"
            control={control}
            render={({ field }) => (
              <textarea
                {...field}
                className="w-full rounded-md border border-gray-300 p-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={5}
                placeholder="Describe the situation, setting, objectives, and any specific challenges or constraints..."
              />
            )}
          />
          {errors.scenarioDescription && (
            <p className="mt-1 text-sm text-red-600">
              {errors.scenarioDescription.message}
            </p>
          )}
        </div>

        {/* Evaluation Rubric Section */}
        <div>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold" }}>
            3. Evaluation Rubric *
          </Typography>
          <Typography variant="body2" sx={{ mb: 2, color: "#666" }}>
            Create assessment criteria with specific questions (1-3 criteria,
            each with 1-3 questions)
          </Typography>

          {criteriaFields.map((criterion, criterionIndex) => (
            <CriterionSection
              key={`criterion-${criterion.id || criterionIndex}`}
              criterionIndex={criterionIndex}
              control={control}
              errors={errors}
              onRemove={() => removeCriterionHandler(criterionIndex)}
              canRemove={criteriaFields.length > 1}
            />
          ))}

          {criteriaFields.length < 3 && (
            <Button
              type="button"
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={addCriterion}
              sx={{ mt: 2 }}
            >
              Add Criterion
            </Button>
          )}

          {errors.evaluationRubric?.criteria && (
            <p className="mt-1 text-sm text-red-600">
              {errors.evaluationRubric.criteria.message}
            </p>
          )}
        </div>
      </form>
    </Box>
  );
});

// Separate component for criterion section to manage questions
interface CriterionSectionProps {
  criterionIndex: number;
  control: any;
  errors: any;
  onRemove: () => void;
  canRemove: boolean;
}

const CriterionSection: React.FC<CriterionSectionProps> = ({
  criterionIndex,
  control,
  errors,
  onRemove,
  canRemove,
}) => {
  const {
    fields: questionFields,
    append: appendQuestion,
    remove: removeQuestion,
  } = useFieldArray({
    control,
    name: `evaluationRubric.criteria.${criterionIndex}.questions`,
  });

  console.log(
    `CriterionSection ${criterionIndex}: questionFields length:`,
    questionFields.length,
  );

  const addQuestion = () => {
    if (questionFields.length < 3) {
      appendQuestion({ question: "" });
    }
  };

  const removeQuestionHandler = (questionIndex: number) => {
    if (questionFields.length > 1) {
      removeQuestion(questionIndex);
    }
  };

  return (
    <Box
      sx={{
        border: "1px solid #ddd",
        borderRadius: "8px",
        p: 3,
        mb: 2,
        backgroundColor: "#fff",
      }}
    >
      <div className="mb-3 flex items-center justify-between">
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          Criterion {criterionIndex + 1}
        </Typography>
        {canRemove && (
          <IconButton onClick={onRemove} color="error" size="small">
            <DeleteIcon />
          </IconButton>
        )}
      </div>

      {/* Criterion Name */}
      <div className="mb-4">
        <Typography variant="body2" sx={{ mb: 1, fontWeight: "medium" }}>
          Criterion Name *
        </Typography>
        <Controller
          name={`evaluationRubric.criteria.${criterionIndex}.name`}
          control={control}
          render={({ field }) => (
            <input
              {...field}
              className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Communication Skills, Problem Solving, etc."
            />
          )}
        />
        {errors.evaluationRubric?.criteria?.[criterionIndex]?.name && (
          <p className="mt-1 text-sm text-red-600">
            {errors.evaluationRubric.criteria[criterionIndex].name.message}
          </p>
        )}
      </div>

      {/* Questions */}
      <div>
        <Typography variant="body2" sx={{ mb: 2, fontWeight: "medium" }}>
          Assessment Questions *
        </Typography>
        {questionFields.map((question, questionIndex) => (
          <div key={question.id} className="mb-2 flex items-start gap-2">
            <div className="flex-1">
              <Controller
                name={`evaluationRubric.criteria.${criterionIndex}.questions.${questionIndex}.question`}
                control={control}
                render={({ field }) => (
                  <textarea
                    {...field}
                    className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                    placeholder={`Question ${questionIndex + 1}...`}
                  />
                )}
              />
              {errors.evaluationRubric?.criteria?.[criterionIndex]?.questions?.[
                questionIndex
              ]?.question && (
                <p className="mt-1 text-sm text-red-600">
                  {
                    errors.evaluationRubric.criteria[criterionIndex].questions[
                      questionIndex
                    ].question.message
                  }
                </p>
              )}
            </div>
            {questionFields.length > 1 && (
              <IconButton
                onClick={() => removeQuestionHandler(questionIndex)}
                color="error"
                size="small"
              >
                <DeleteIcon />
              </IconButton>
            )}
          </div>
        ))}

        {questionFields.length < 3 && (
          <Button
            type="button"
            variant="text"
            startIcon={<AddIcon />}
            onClick={addQuestion}
            size="small"
          >
            Add Question
          </Button>
        )}
      </div>
    </Box>
  );
};

export default PracticeActivityForm;
