import { useQuery } from "@tanstack/react-query";
import useAxios from "../../../hooks/axiosObject";
import { AxiosInstance } from "axios";

type CohortSummary = {
  cohort: string;
  course: string;
  totalStudents: number;
  averageScore: number;
  completionRate: number;
  activities: {
    [key: string]: {
      avg: number;
      completed: number;
    };
  };
};

const getCohortSummaries = async (axiosObject: AxiosInstance) => {
  const response = await axiosObject.get<CohortSummary[]>(
    "/api/v1/course/get/analytics",
  );
  return response.data;
};

function useCohortSummaries() {
  const axiosObject = useAxios();

  return useQuery<CohortSummary[]>({
    queryKey: ["cohortSummaries"],
    refetchOnWindowFocus: false,
    queryFn: () => getCohortSummaries(axiosObject),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export default useCohortSummaries;
