import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { ActivityResultCohortResponse } from "../../types/Activity";
import { AxiosInstance } from "axios";

// api call

const getActivityResult = async (
  axiosObject: AxiosInstance,
  cohortId: string,
  activityType: string,
): Promise<ActivityResultCohortResponse> => {
  const response = await axiosObject.get<ActivityResultCohortResponse>(
    "/api/v1/activity-progress/get/result-by-cohort", {
      params: { cohort_id: cohortId, activity_type: activityType },
    },
  );
  console.log(response);
  return response.data;
};

export function useGetActivityResultByCohort(cohortId: string, activityType: string) {
  const axiosObject = useAxios();
  // run the query
  return useQuery<ActivityResultCohortResponse>({
    queryKey: ["activityResult", cohortId, activityType],
    refetchOnWindowFocus: false,
    queryFn: () => getActivityResult(axiosObject, cohortId, activityType),
    enabled: !!cohortId,
    staleTime: 5 * 60 * 1000,
  });
}
