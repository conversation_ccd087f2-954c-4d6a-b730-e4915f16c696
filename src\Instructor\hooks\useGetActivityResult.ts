import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { ActivityResultResponse } from "../../types/Activity";
import { AxiosInstance } from "axios";

// api call

const getActivityResult = async (
  axiosObject: AxiosInstance,
  userId: string,
  activityType: string,
): Promise<ActivityResultResponse[]> => {
  const response = await axiosObject.get<ActivityResultResponse[]>(
    "/api/v1/activity-progress/get/result",
    {
      params: { user_id: userId, activity_type: activityType },
    },
  );
  console.log(
    "activity result for " + userId + " and " + activityType,
    response,
  );
  return response.data;
};

export function useGetActivityResult(userId: string, activityType: string) {
  const axiosObject = useAxios();
  // run the query
  return useQuery<ActivityResultResponse[]>({
    queryKey: ["activitieResult", userId, activityType],
    refetchOnWindowFocus: false,
    queryFn: () => getActivityResult(axiosObject, userId, activityType),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
}
