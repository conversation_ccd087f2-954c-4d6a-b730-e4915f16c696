import { useQuery } from "@tanstack/react-query";
import { AxiosInstance } from "axios";
import useAxios from "../../hooks/axiosObject";
import { Course } from "../../types/CourseProgress";

// api call
const getCourseProgress = async (
  axiosObject: AxiosInstance,
): Promise<Course[]> => {
  const courses_progress = await axiosObject.get<Course[]>(
    "/api/v1/course/get/all",
  );
  console.log(courses_progress);
  return courses_progress.data;
};

// user query
export function useGetCourseProgress() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<Course[]>({
    queryKey: ["admin_courses"],
    queryFn: () => getCourseProgress(axiosObject),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}
