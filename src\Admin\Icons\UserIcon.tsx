import React from "react";

interface UserIconProps {
  selected?: boolean;
}

function UserIcon({ selected = false }: UserIconProps) {
  return (
    <svg
      width="15"
      height="21"
      viewBox="0 0 15 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.33333 15.7858H5V10.148H3.33333V15.7858ZM10 15.7858H11.6667V4.51024H10V15.7858ZM6.66667 15.7858H8.33333V12.4031H6.66667V15.7858ZM6.66667 10.148H8.33333V7.89291H6.66667V10.148ZM1.66667 20.2961C1.20833 20.2961 0.816111 20.0754 0.49 19.6342C0.163889 19.1929 0.000555556 18.6619 0 18.0409V2.25512C0 1.63496 0.163333 1.10426 0.49 0.663005C0.816667 0.221753 1.20889 0.000751706 1.66667 0H13.3333C13.7917 0 14.1842 0.221002 14.5108 0.663005C14.8375 1.10501 15.0006 1.63571 15 2.25512V18.0409C15 18.6611 14.8369 19.1922 14.5108 19.6342C14.1847 20.0762 13.7922 20.2968 13.3333 20.2961H1.66667Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default UserIcon;
