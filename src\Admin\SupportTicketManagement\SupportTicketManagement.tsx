import React from "react";

interface EmailItem {
  brand: string;
  email: string;
  ticketCreation: "Enabled" | "Disabled";
  status: "Verified" | "Not Verified";
  starred: boolean;
}

const SupportTicketManagement: React.FC = () => {
  const emails: EmailItem[] = [
    {
      brand: "<PERSON> Hales",
      email: "<EMAIL>",
      ticketCreation: "Enabled",
      status: "Verified",
      starred: true,
    },
    {
      brand: "<PERSON> Hales",
      email: "<EMAIL>",
      ticketCreation: "Enabled",
      status: "Verified",
      starred: false,
    },
    {
      brand: "<PERSON>",
      email: "<EMAIL>",
      ticketCreation: "Disabled",
      status: "Verified",
      starred: true,
    },
    {
      brand: "<PERSON> Hales",
      email: "<EMAIL>",
      ticketCreation: "Enabled",
      status: "Not Verified",
      starred: true,
    },
    {
      brand: "<PERSON> Hale<PERSON>",
      email: "<EMAIL>",
      ticketCreation: "Disabled",
      status: "Verified",
      starred: false,
    },
    {
      brand: "<PERSON> Hales",
      email: "<EMAIL>",
      ticketCreation: "Enabled",
      status: "Not Verified",
      starred: false,
    },
    {
      brand: "<PERSON> Hales",
      email: "<EMAIL>",
      ticketCreation: "Enabled",
      status: "Not Verified",
      starred: true,
    },
    {
      brand: "Alex Hales",
      email: "<EMAIL>",
      ticketCreation: "Enabled",
      status: "Verified",
      starred: true,
    },
  ];

  return (
    <div className="mx-auto max-w-6xl p-6">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex space-x-4">
          <button className="border-b-2 border-blue-600 px-4 py-2 font-semibold text-blue-600">
            Support Emails
          </button>
          <button className="px-4 py-2 text-gray-500">Configurations</button>
          <button className="px-4 py-2 text-gray-500">DKIM Settings</button>
        </div>
        <button className="rounded-md bg-blue-700 px-4 py-2 text-white">
          Add Email
        </button>
      </div>

      <div className="rounded-lg bg-white shadow">
        <div className="flex items-center justify-between border-b p-4">
          <div className="flex items-center">
            <h2 className="text-xl font-semibold">Ticket Management</h2>
            <span className="ml-2 rounded-full bg-gray-200 p-1">
              <svg
                width="34"
                height="34"
                viewBox="0 0 34 34"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M16.983 0C7.599 0 0 7.616 0 17C0 26.384 7.599 34 16.983 34C26.384 34 34 26.384 34 17C34 7.616 26.384 0 16.983 0Z"
                  fill="url(#paint0_linear_0_1)"
                />
                <path
                  d="M19.864 21.4L17 19.5312L14.136 21.4L15 18.0525L12.368 15.875L15.768 15.6637L17 12.4625L18.232 15.6637L21.632 15.875L19 18.0525M23.4 17.5C23.4 17.069 23.5686 16.6557 23.8686 16.351C24.1687 16.0462 24.5757 15.875 25 15.875V12.625C25 12.194 24.8314 11.7807 24.5314 11.476C24.2313 11.1712 23.8243 11 23.4 11H10.6C10.1757 11 9.76869 11.1712 9.46863 11.476C9.16857 11.7807 9 12.194 9 12.625V15.875C9.42435 15.875 9.83131 16.0462 10.1314 16.351C10.4314 16.6557 10.6 17.069 10.6 17.5C10.6 17.931 10.4314 18.3443 10.1314 18.649C9.83131 18.9538 9.42435 19.125 9 19.125V22.375C9 22.806 9.16857 23.2193 9.46863 23.524C9.76869 23.8288 10.1757 24 10.6 24H23.4C23.8243 24 24.2313 23.8288 24.5314 23.524C24.8314 23.2193 25 22.806 25 22.375V19.125C24.5757 19.125 24.1687 18.9538 23.8686 18.649C23.5686 18.3443 23.4 17.931 23.4 17.5Z"
                  fill="white"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_0_1"
                    x1="-1.8999e-07"
                    y1="17"
                    x2="34"
                    y2="17"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#2B3D59" />
                    <stop offset="1" stopColor="#375685" />
                  </linearGradient>
                </defs>
              </svg>
            </span>
          </div>
          <span className="text-sm text-gray-500">5/10 Support emails</span>
        </div>

        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Brand
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Email
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Ticket Creation
                <svg
                  className="ml-1 inline-block h-4 w-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Status
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {emails.map((item, index) => (
              <tr key={index}>
                <td className="whitespace-nowrap px-6 py-4">
                  {item.starred ? (
                    <svg
                      className="mr-2 inline-block h-5 w-5 text-yellow-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="mr-2 inline-block h-5 w-5 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                      />
                    </svg>
                  )}
                  {item.brand}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                  {item.email}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                  {item.ticketCreation}
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <span
                    className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${item.status === "Verified" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                  >
                    {item.status === "Verified" ? (
                      <svg
                        className="mr-1 h-4 w-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="mr-1 h-4 w-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                    {item.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="mt-4 flex items-center justify-between">
        <div className="flex space-x-2">
          <button className="rounded border px-2 py-1">&lt;&lt;</button>
          <button className="rounded border px-2 py-1">&lt;</button>
          <button className="rounded border bg-blue-600 px-2 py-1 text-white">
            1
          </button>
          <button className="rounded border px-2 py-1">&gt;</button>
          <button className="rounded border px-2 py-1">&gt;&gt;</button>
        </div>
        <span className="text-sm text-gray-500">1 of 1 Pages (5 items)</span>
      </div>
    </div>
  );
};

export default SupportTicketManagement;
