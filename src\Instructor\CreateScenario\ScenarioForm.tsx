import React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

const formSchema = z.object({
  tutorialNature: z.string().nonempty("This field is required"),
  targetAudience: z.string().nonempty("This field is required"),
  learningOutcome: z.string().nonempty("This field is required"),
  instructions: z.string().optional(),
});

const tutorialFormSchema = z.object({
  targetAudience: z.string().nonempty("This field is required"),
  learningOutcome: z.string().nonempty("This field is required"),
  instructions: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface ScenarioFormProps {
  onNext: (data: FormValues) => void;
  activityType: string;
}

const ScenarioForm: React.FC<ScenarioFormProps> = ({
  onNext,
  activityType,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(
      activityType === "tutorial" ? tutorialFormSchema : formSchema,
    ),
  });

  const onSubmit = (data: FormValues) => {
    onNext(data);
  };

  return (
    <div className="mt-8 w-full">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {activityType !== "tutorial" ? (
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              {activityType === "tutorial"
                ? "Industry *"
                : "Name of Scenario *"}
            </label>
            <input
              {...register("tutorialNature")}
              className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={
                activityType === "tutorial"
                  ? "Enter the industry"
                  : "Enter the name of the scenario"
              }
            />
            {errors.tutorialNature && (
              <p className="mt-1 text-sm text-red-600">
                {errors.tutorialNature.message}
              </p>
            )}
          </div>
        ) : null}
        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700">
            {activityType === "tutorial"
              ? "Learning Objectives *"
              : "Who the Scenario is For *"}
          </label>
          <input
            {...register("targetAudience")}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={
              activityType === "tutorial"
                ? "Enter the learning objectives"
                : "Enter the target audience"
            }
          />
          {errors.targetAudience && (
            <p className="mt-1 text-sm text-red-600">
              {errors.targetAudience.message}
            </p>
          )}
        </div>

        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700">
            {activityType === "tutorial"
              ? "Learner Profile *"
              : "Expected Learning Outcome *"}
          </label>
          <input
            {...register("learningOutcome")}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={
              activityType === "tutorial"
                ? "Enter the learner profile"
                : "Enter the expected learning outcome"
            }
          />
          {errors.learningOutcome && (
            <p className="mt-1 text-sm text-red-600">
              {errors.learningOutcome.message}
            </p>
          )}
        </div>

        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700">
            Describe the Instructions
          </label>
          <textarea
            {...register("instructions")}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={4}
            placeholder="Enter instructions (optional)"
          />
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default ScenarioForm;
