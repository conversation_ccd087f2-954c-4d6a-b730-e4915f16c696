import React from "react";

const DropDown: React.FC = () => {
  return (
    <div className="space-y-16 p-4">
      <h2 className="mb-4 text-2xl font-medium text-[#6F6F6F] text-opacity-90">
        Course Management
      </h2>
      <div className="flex items-center">
        <label className="mr-4 text-lg font-semibold text-[#2A2A2A]">
          Select Cohort:
        </label>
        <select className="h-[36px] w-[164px] rounded bg-[#DAE3F2] p-1 text-center text-lg font-semibold text-[#36537F] focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option className="bg-white text-sm font-medium text-[#4C4C4C]">
            Cohort 1
          </option>
          <option className="bg-white text-sm font-medium text-[#4C4C4C]">
            Cohort 2
          </option>
          <option className="bg-white text-sm font-medium text-[#4C4C4C]">
            Cohort 3
          </option>
          <option className="bg-white text-sm font-medium text-[#4C4C4C]">
            Cohort 4
          </option>
          <option className="bg-white text-sm font-medium text-[#4C4C4C]">
            Cohort 5
          </option>
          <option className="bg-white text-sm font-medium text-[#4C4C4C]">
            Cohort 6
          </option>
          <option className="bg-white text-sm font-medium text-[#4C4C4C]">
            Cohort 7
          </option>
        </select>
      </div>
    </div>
  );
};

export default DropDown;
