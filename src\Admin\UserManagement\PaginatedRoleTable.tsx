import React, {useEffect, useState} from 'react';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import { useGetAllUsers } from '../hooks/useGetAllUsers';
import Spinner from '../../Learner/components/Spinner';
import { User } from '../../types/User';

const useDebouncedInput = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

interface UserTableProps {
  roles: User["role"][];
  handleRoleChange: (userId: number | undefined, role: User["role"]) => void;
}

interface Column {
  id: 'email' | 'learner' | 'instructor' | 'admin';
  label: string;
  minWidth?: number;
  align?: 'right';
}

const columns: Column[] = [
  { id: 'email', label: 'Email', minWidth: 100 },
  { id: 'learner', label: 'Learner', minWidth: 100 },
  { id: 'instructor', label: 'Instructor', minWidth: 100 },
  { id: 'admin', label: 'Admin', minWidth: 100 },
];

const UserTable: React.FC<UserTableProps> = ({ roles, handleRoleChange }) => {
  const [users, setUsers] = React.useState<User[]>([]);
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);
  const [searchTerm, setSearchTerm] = React.useState('');

  // Use debounced input for search
  const debouncedSearchTerm = useDebouncedInput(searchTerm, 500); // 500ms delay

  const { data: userData, isFetching, isError } = useGetAllUsers(page + 1, rowsPerPage, debouncedSearchTerm);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0); // Reset to first page when search is initiated
  };

  const handleUserRoleChange = (userId: number | undefined, newRole: User["role"]) => {
    setUsers(
      users.map((user) =>
        user.id === userId
          ? { ...user, role: user.role === newRole ? null : newRole }
          : user,
      ),
    );
    handleRoleChange(userId, newRole);
  };

  React.useEffect(() => {
    if (userData) {
      setUsers(userData.items);
    }
  }, [userData]);

  return (
    <>
      <input
        type="text"
        placeholder="Search by name"
        value={searchTerm}
        onChange={handleSearchChange}
        className="w-full p-2 mb-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
      />
      <Spinner loading={isFetching && !isError} />
      <Paper sx={{ width: '100%' }}>
        <TableContainer sx={{ maxHeight: 440 }}>
          <Table stickyHeader aria-label="sticky table">
            <TableHead className="text-center">
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align={column.align}
                    style={{ top: 0, minWidth: column.minWidth }}
                  >
                    {column.label}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow hover role="checkbox" tabIndex={-1} key={user.id}>
                  <TableCell>{user.email}</TableCell>
                  {roles.map((role) => (
                    <TableCell className="text-center" key={role}>
                      <input
                        type="checkbox"
                        checked={user.role === role}
                        onChange={() => handleUserRoleChange(user.id, role)}
                        className="form-checkbox h-4 w-4 text-blue-600"
                      />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 100]}
          component="div"
          count={userData?.total || 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </>
  );
};

export default UserTable;
