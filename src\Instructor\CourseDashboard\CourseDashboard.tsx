import React from 'react';
import MetricCard from '../CourseDashboard/MetricCard';
import { Select, MenuItem } from '@mui/material';
import { BarChart } from '@mui/x-charts/BarChart';
import { useGetOverviewAnalytics } from '../hooks/useGetOverview';
import Spinner from '../../Learner/components/Spinner';

const CourseDashboard: React.FC = () => {
  const { data: overview, isFetching } = useGetOverviewAnalytics();
  return (
    <div className="p-4">
      <Spinner loading={isFetching} />
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <MetricCard
          value={overview ? overview.data.total_courses : 0}
          label="Total Courses"
          icon={<i className="fas fa-book"></i>} // Adjust the icon as needed
          bgColor="bg-yellow-100"
        />
        <MetricCard
          value={overview ? overview.data.total_students : 0}
          label="Students"
          icon={<i className="fas fa-user-graduate"></i>} // Adjust the icon as needed
          bgColor="bg-green-100"
        />
        <MetricCard
          value={overview ? overview.data.total_students_completed_courses : 0}
          label="Completed"
          icon={<i className="fas fa-check-circle"></i>} // Adjust the icon as needed
          bgColor="bg-red-100"
        />
        <MetricCard
          value={overview ? overview.data.total_students_remaining_courses : 0}
          label="Remaining"
          icon={<i className="fas fa-clock"></i>} // Adjust the icon as needed
          bgColor="bg-blue-100"
        />
      </div>
      <div className="mb-4">
        <label htmlFor="course-select" className="mr-2">Select Course:</label>
        <Select id="course-select" defaultValue="Dignity">
          <MenuItem value="Dignity">Dignity</MenuItem>
          {/* Add more courses as needed */}
        </Select>
      </div>
      <div>
        {overview ? (
          <BarChart
            yAxis={[{ scaleType: 'band', data: ['Total', 'Completed', 'Remaining'] }]}
            series={[{
              data: [overview.data.total_students, overview.data.total_students_completed_courses,
              overview.data.total_students_remaining_courses]
            }]}
            width={1000}
            height={300}
            sx={{margin: "10px 0"}}
            layout='horizontal'
          />
        ) : null}
      </div>
    </div>
  );
};

export default CourseDashboard;
