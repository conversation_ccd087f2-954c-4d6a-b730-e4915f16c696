//
import { create } from "zustand";

type DemoStore = {
  video_type: "food_complaint" | "tiger_enclosure" | "panda_closure" | null;
  demo: boolean;
  setDemo: (demo: boolean) => void;
  setVideoType: (
    video_type: "food_complaint" | "tiger_enclosure" | "panda_closure" | null,
  ) => void;
  reset: () => void;
};

const initialState = {
  video_type: null as
    | "food_complaint"
    | "tiger_enclosure"
    | "panda_closure"
    | null,
  demo: false,
};

export const useDemoStore = create<DemoStore>((set) => ({
  ...initialState,
  setDemo: (demo) => set({ demo }),
  setVideoType: (video_type) => set({ video_type }),
  reset: () => set(initialState),
}));

// Expose reset function globally for cache manager
if (typeof window !== "undefined") {
  (window as any).resetDemoStore = () => {
    useDemoStore.getState().reset();
  };
}
