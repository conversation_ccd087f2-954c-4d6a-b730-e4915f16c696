import { Card } from "./ui/card";
import { Badge } from "./ui/badge";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";
import { mockOrganizationSummaries, mockActivityResults, mockCohortSummaries } from "../data/mockData";

const COLORS = ['#0088FE', '#00C49F', '#FFBB28'];

export function OrganizationView() {
  // Prepare organization comparison data
  const orgComparisonData = mockOrganizationSummaries.map(org => ({
    name: org.organization,
    averageScore: org.averageScore,
    totalStudents: org.totalStudents,
    completionRate: org.completionRate
  }));

  // Calculate organization activity breakdown
  const orgActivityData = mockOrganizationSummaries.map(org => {
    const orgResults = mockActivityResults.filter(result => result.organization === org.organization);
    const aiResults = orgResults.filter(r => r.activityType === 'AI Activity');
    const quizResults = orgResults.filter(r => r.activityType === 'Quiz');
    const viraResults = orgResults.filter(r => r.activityType === 'Vira Activity');

    return {
      organization: org.organization,
      'AI Activity': aiResults.length > 0 ? aiResults.reduce((sum, r) => sum + r.score, 0) / aiResults.length : 0,
      'Quiz': quizResults.length > 0 ? quizResults.reduce((sum, r) => sum + r.score, 0) / quizResults.length : 0,
      'Vira Activity': viraResults.length > 0 ? viraResults.reduce((sum, r) => sum + r.score, 0) / viraResults.length : 0
    };
  });

  // Student distribution by organization
  const studentDistribution = mockOrganizationSummaries.map(org => ({
    name: org.organization,
    value: org.totalStudents
  }));

  return (
    <div className="space-y-6">
      {/* Organization Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-6">
          <div className="flex flex-col">
            <span className="text-sm text-muted-foreground">Total Organizations</span>
            <span className="text-2xl font-bold">{mockOrganizationSummaries.length}</span>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex flex-col">
            <span className="text-sm text-muted-foreground">Total Students</span>
            <span className="text-2xl font-bold">
              {mockOrganizationSummaries.reduce((sum, org) => sum + org.totalStudents, 0)}
            </span>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex flex-col">
            <span className="text-sm text-muted-foreground">Total Cohorts</span>
            <span className="text-2xl font-bold">
              {mockOrganizationSummaries.reduce((sum, org) => sum + org.totalCohorts, 0)}
            </span>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex flex-col">
            <span className="text-sm text-muted-foreground">Top Performer</span>
            <span className="text-2xl font-bold">
              {mockOrganizationSummaries.reduce((best, org) => 
                org.averageScore > best.averageScore ? org : best, mockOrganizationSummaries[0]
              ).organization}
            </span>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Organization Performance Comparison */}
        <Card className="p-6">
          <h3 className="mb-4">Organization Performance Comparison</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={orgComparisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="averageScore" fill="#8884d8" name="Average Score %" />
            </BarChart>
          </ResponsiveContainer>
        </Card>

        {/* Student Distribution */}
        <Card className="p-6">
          <h3 className="mb-4">Student Distribution by Organization</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={studentDistribution}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value }) => `${name}: ${value}`}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
              >
                {studentDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Activity Performance by Organization */}
      <Card className="p-6">
        <h3 className="mb-4">Activity Performance by Organization</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={orgActivityData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="organization" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="AI Activity" fill="#0088FE" />
            <Bar dataKey="Quiz" fill="#00C49F" />
            <Bar dataKey="Vira Activity" fill="#FFBB28" />
          </BarChart>
        </ResponsiveContainer>
      </Card>

      {/* Detailed Organization Table */}
      <Card className="p-6">
        <h3 className="mb-4">Organization Details</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">Organization</th>
                <th className="text-left p-2">Students</th>
                <th className="text-left p-2">Cohorts</th>
                <th className="text-left p-2">Avg Score</th>
                <th className="text-left p-2">Completion Rate</th>
                <th className="text-left p-2">Top Cohort</th>
              </tr>
            </thead>
            <tbody>
              {mockOrganizationSummaries.map((org) => (
                <tr key={org.organization} className="border-b">
                  <td className="p-2">{org.organization}</td>
                  <td className="p-2">{org.totalStudents}</td>
                  <td className="p-2">{org.totalCohorts}</td>
                  <td className="p-2">
                    <Badge variant={org.averageScore >= 90 ? "default" : org.averageScore >= 80 ? "secondary" : "outline"}>
                      {org.averageScore.toFixed(1)}%
                    </Badge>
                  </td>
                  <td className="p-2">{org.completionRate.toFixed(1)}%</td>
                  <td className="p-2">
                    <Badge variant="outline">{org.topPerformingCohort}</Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Cohorts by Organization */}
      <Card className="p-6">
        <h3 className="mb-4">Cohorts by Organization</h3>
        <div className="space-y-4">
          {mockOrganizationSummaries.map((org) => {
            const orgCohorts = mockCohortSummaries.filter(cohort => 
              mockActivityResults.some(result => 
                result.cohort === cohort.cohort && result.organization === org.organization
              )
            );
            
            return (
              <div key={org.organization} className="border rounded-lg p-4">
                <h4 className="mb-2">{org.organization}</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                  {orgCohorts.map((cohort) => (
                    <div key={cohort.cohort} className="flex items-center justify-between p-2 bg-secondary rounded">
                      <span>{cohort.cohort}</span>
                      <Badge variant="outline">{cohort.averageScore.toFixed(1)}%</Badge>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </Card>
    </div>
  );
}