import { Routes, Route, useLocation } from "react-router-dom";
import CourseManagement from "./CourseManagement/CourseManagement";
import Dashboard from "./HomeScreen/InstructoreHome";
import CourseDashboard from "./CourseDashboard/CourseDashboard";
import EnrollmentTable from "./CourseEnrollment/EnrollmentTable";
import LearnerProgressTable from "./LearnerProgress/LearnerProgress";
import Layout from "./Layout";
import Analytics from "./Analytics/MainAnalytics";
import Forum from "./Forum/Forum";
import { courseReviewIcon, courseEnrollmentIcon } from "./components/Icons";
import CourseDevelopment from "./CourseDevelopment/CourseDevelopment";
import ActivityDevelopment from "./ActivityDevelopment/ActivityDevelopment";
import NotificationPage from "./Notifications/Notifications";
import SettingsHome from "./Settings/SettingsHome";
import CreateScenario from "./CreateScenario/CreateScenario";
import CourseCreation from "./CourseCreation/CourseCreation";

function MainInstructorScreen() {
  return (
    <Layout
      heading={HeadingText()}
      icon={getICon()}
      children={
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/course-management" element={<CourseManagement />} />
          <Route path="/create-course" element={<CourseCreation />} />
          <Route path="/create-scenario" element={<CreateScenario />} />
          <Route path="/course-development" element={<CourseDevelopment />} />
          <Route path="/dashboard" element={<CourseDashboard />} />
          <Route path="/course-enrollment" element={<EnrollmentTable />} />
          <Route path="/learner-progress" element={<LearnerProgressTable />} />
          <Route path="/analytics/*" element={<Analytics />} />
          <Route path="/feedback-&-questions" element={<Forum />} />
          <Route
            path="/activity-development"
            element={<ActivityDevelopment />}
          />
          <Route path="/notifications" element={<NotificationPage />} />
          <Route path="/settings" element={<SettingsHome />} />
        </Routes>
      }
    />
  );
}

function HeadingText() {
  const location = useLocation();
  const urlParams = new URLSearchParams(window.location.search);
  const activityType = urlParams.get("activityType");

  let headerText = "";
  switch (location.pathname) {
    case "/instructor/course-development":
      headerText = "Course Development";
      break;
    case "/instructor/create-course":
      headerText = "Create Course";
      break;
    case "/instructor/create-scenario":
      headerText =
        activityType === "tutorial" ? "Generate Video" : "Create a Scenario";
      break;
    case "/instructor/course-management":
      headerText = "Course Management";
      break;
    case "/instructor/dashboard":
      headerText = "Dashboard";
      break;
    case "/instructor/course-enrollment":
      headerText = "Course Enrollment";
      break;
    case "/instructor/learner-progress":
      headerText = "Learner Progress";
      break;
    case "/instructor":
      headerText = "Instructor Portal";
      break;
    case "/instructor/feedback-&-questions":
      headerText = "Feedback & Questions";
      break;
    case "/instructor/activity-development":
      headerText = "Activity Development";
      break;
    case "/instructor/notifications":
      headerText = "Notifications";
      break;
    case "/instructor/settings":
      headerText = "Settings";
      break;
    case "/instructor/pre_course_survey":
      headerText = "Pre Course Survey";
      break;
    case "/instructor/recap":
      headerText = "Recap";
      break;
    case "/instructor/hardware_test":
      headerText = "Hardware Test";
      break;
    case "/instructor/forum":
      headerText = "Forum";
      break;
    case "/instructor/boss_challenge":
      headerText = "Boss Challenge";
      break;
    default:
      headerText = "Analytics";
  }

  return headerText;
}

function getICon() {
  const location = useLocation();

  let icon = null;
  switch (location.pathname) {
    case "/instructor/course-management":
      icon = courseReviewIcon;
      break;
    case "/instructor/create-course":
      icon = courseReviewIcon;
      break;
    case "/instructor/course-development":
      icon = courseReviewIcon;
      break;
    case "/instructor/dashboard":
      icon = courseEnrollmentIcon;
      break;
    case "/instructor/course-enrollment":
      icon = courseEnrollmentIcon;
      break;
    case "/instructor/learner-progress":
      icon = courseReviewIcon;
      break;
    default:
      icon = courseReviewIcon;
  }

  return icon;
}

export default MainInstructorScreen;
