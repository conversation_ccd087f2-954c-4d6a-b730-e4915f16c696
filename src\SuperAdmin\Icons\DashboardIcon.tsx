import React from "react";

interface Props {
  selected?: boolean;
}

function DashboardIcon({ selected }: Props) {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 9.34375H7.65625V0H0V9.34375ZM0 17H7.65625V11.0312H0V17ZM9.34375 17H17V7.65625H9.34375V17ZM9.34375 0V5.96875H17V0H9.34375Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default DashboardIcon;
