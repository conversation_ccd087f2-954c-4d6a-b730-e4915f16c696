import React from 'react';
import StarIcon from '@mui/icons-material/Star';

const articles = [
    'Verify your ID',
    'Cancel a subscription',
    'Get a Course Certificate',
    'Submit peer reviewed assignments',
    'Coursera Honor Code',
    'Apply for financial aid'
];

const PopularArticles: React.FC = () => {
  const half = Math.ceil(articles.length / 2);
  const firstHalf = articles.slice(0, half);
  const secondHalf = articles.slice(half);

  return (
    <div className="bg-gray-200 rounded-lg p-6 w-full">
      <div className="flex items-center mb-4">
        <StarIcon className="text-blue-700 mr-2" />
        <h2 className="text-lg font-semibold text-blue-700">Popular articles</h2>
      </div>
      <div className="items-center grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-600">
        <ul className='list-disc ml-5'>
          {firstHalf.map((article, index) => (
            <li key={index} className="mb-2">{article}</li>
          ))}
        </ul>
        <ul className='list-disc'>
          {secondHalf.map((article, index) => (
            <li key={index} className="mb-2">{article}</li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default PopularArticles;
