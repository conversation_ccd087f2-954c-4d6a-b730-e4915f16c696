import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { HeygenVideo } from "./useHeygenVideos";
import {
  recordStreamToVideo,
  recordVideoElement,
} from "../CreateScenario/utils/videoUtils";

/**
 * Hook to save a Heygen video directly from a video element
 * This is used when we want to save a video that's being streamed from the Heygen API
 */
export function useSaveHeygenVideoFromStream() {
  const axiosInstance = useAxios();

  return useMutation({
    mutationFn: async ({
      name,
      videoElement,
      stream,
      videoBlob,
      recordingDuration = 30000, // Default to 30 seconds
    }: {
      name: string;
      videoElement?: HTMLVideoElement;
      stream?: MediaStream;
      videoBlob?: Blob;
      recordingDuration?: number;
    }) => {
      try {
        let finalVideoBlob: Blob;

        // If we already have a video blob, use it directly
        if (videoBlob) {
          console.log("Using provided video blob, size:", videoBlob.size);
          finalVideoBlob = videoBlob;
        }
        // If we have a direct stream, record it
        else if (stream) {
          console.log("Recording from direct stream");
          finalVideoBlob = await recordStreamToVideo(stream, recordingDuration);
        }
        // Otherwise, try to record from the video element
        else if (videoElement) {
          console.log("Recording from video element");
          finalVideoBlob = await recordVideoElement(
            videoElement,
            recordingDuration,
          );
        } else {
          throw new Error(
            "No video source provided (blob, stream, or element)",
          );
        }

        console.log("Video ready for upload, size:", finalVideoBlob.size);

        if (finalVideoBlob.size < 10000) {
          // Less than 10KB is probably not a valid video
          throw new Error("Failed to capture video content (file too small)");
        }

        // Create a FormData object to send to the backend
        const formData = new FormData();
        formData.append("name", name);

        // Use .webm extension for video files
        formData.append("video_file", finalVideoBlob, `${name}.webm`);

        // Send the request to the backend
        const response = await axiosInstance.post<{
          success: boolean;
          message: string;
          data: HeygenVideo;
        }>("/api/v1/heygen-video/create", formData);

        return response.data.data;
      } catch (error) {
        console.error("Error saving Heygen video:", error);
        throw error;
      }
    },
  });
}
