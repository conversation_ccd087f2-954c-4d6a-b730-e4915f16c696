import React from "react";
import { useNavigate } from "react-router-dom";

interface DashboardCardProps {
  title: string;
  description: string;
  icon: string;
  link: string;
  bgColor: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  description,
  icon,
  link,
  bgColor,
}) => {
  const navigate = useNavigate();

  return (
    <div
      className={`flex cursor-pointer flex-col rounded-lg p-6 shadow-md ${bgColor}`}
      onClick={() => navigate(`/admin${link}`)}
    >
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">{title}</h3>
        <img src={icon} alt={title} className="h-12 w-12" />
      </div>
      <p className="mb-4 text-gray-600">{description}</p>
      <div className="mt-auto">
        <button
          className="rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
          onClick={(e) => {
            e.stopPropagation();
            navigate(`/admin${link}`);
          }}
        >
          Manage
        </button>
      </div>
    </div>
  );
};

const Dashboard: React.FC = () => {
  // Using public images that already exist in the project
  const dashboardItems = [
    {
      title: "User Management",
      description: "Manage user accounts, roles, and permissions",
      icon: "/user-profile-icon.png",
      link: "/user-management",
      bgColor: "bg-blue-50",
    },
    {
      title: "Course Management",
      description: "Create, edit, and manage course content",
      icon: "/course-management-cartoon.png",
      link: "/course-management",
      bgColor: "bg-green-50",
    },
    {
      title: "User Upload",
      description: "Manage group registrations and enrollments",
      icon: "/course-catalogue-icon.png",
      link: "/group-registration",
      bgColor: "bg-yellow-50",
    },
    {
      title: "Support Tickets",
      description: "View and respond to user support tickets",
      icon: "/help-center-icon.png",
      link: "/support-ticket-management",
      bgColor: "bg-purple-50",
    },
    {
      title: "Security Management",
      description: "Configure security settings and access controls",
      icon: "/settings-icon.png",
      link: "/security-management",
      bgColor: "bg-red-50",
    },
    {
      title: "Analytics Overview",
      description: "View detailed analytics and reports",
      icon: "/analytics-cartoon.png",
      link: "/overview",
      bgColor: "bg-indigo-50",
    },
  ];

  return (
    <div>
      <h1 className="mb-12 text-3xl font-bold text-gray-800">
        Admin Dashboard
      </h1>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {dashboardItems.map((item, index) => (
          <DashboardCard
            key={index}
            title={item.title}
            description={item.description}
            icon={item.icon}
            link={item.link}
            bgColor={item.bgColor}
          />
        ))}
      </div>
    </div>
  );
};

export default Dashboard;
