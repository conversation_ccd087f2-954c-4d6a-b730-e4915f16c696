import React from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthProvider";
import { useCourse } from "../context/CourseContext";
import { useGetCourseProgress } from "../hooks/useGetCourseProgress";
import Spinner from "./Spinner";

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  selectedIndex: any;
  setSelectedIndex: (index: any) => void;
}

function splitAndCapitalize(str: string) {
  return str
    .split("_")
    .map(
      (word: string) =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
    )
    .join(" ");
}

const getLink = (course_id: string, activity: any) => {
  return `/my-learning/${activity.type}?course_id=${course_id}&activity_id=${activity.id}`;
};

const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onClose,
  selectedIndex,
  setSelectedIndex,
}) => {
  const { data: courses_progress, isFetching } = useGetCourseProgress();
  const { selectedCourseId, setSelectedCourseId } = useCourse();
  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = (logoutFn: () => void) => {
    logoutFn();
    // The navigation is now handled in the logout function itself
  };

  const handleNavigation = (path: string, index: string) => {
    setSelectedIndex(index);
    navigate(path);
    onClose();
  };

  const handleCourseClick = (courseId: string) => {
    setSelectedIndex(`course_id=${courseId}`);
    setSelectedCourseId(Number(courseId));
  };

  const handleActivityClick = (link: string, completed: boolean) => {
    if (!completed) {
      navigate(link);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 bg-black bg-opacity-50"
      onClick={onClose}
    >
      <div
        className="fixed left-0 top-0 h-full w-4/5 max-w-xs overflow-y-auto bg-white"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">Menu</h2>
            <button
              className="text-gray-500 hover:text-gray-700"
              onClick={onClose}
            >
              ✕
            </button>
          </div>
        </div>

        <Spinner loading={isFetching} />

        <div className="divide-y divide-gray-200">
          <div
            className={`p-4 ${selectedIndex === "home-screen" ? "bg-blue-100" : ""}`}
            onClick={() => handleNavigation("/home-screen", "home-screen")}
          >
            <div className="flex items-center">
              <img className="mr-3 h-4 w-4" src="/home-icon.png" alt="Home" />
              <span
                className={
                  selectedIndex === "home-screen"
                    ? "font-medium"
                    : "text-gray-600"
                }
              >
                Home
              </span>
            </div>
          </div>

          {courses_progress?.map((cp) => (
            <div key={`course_id=${cp.course_id}`} className="p-4">
              <div
                className={`flex items-center justify-between ${selectedIndex === `course_id=${cp.course_id}` ? "font-medium" : "text-gray-600"}`}
                onClick={() => handleCourseClick(cp.course_id.toString())}
              >
                <div className="flex items-center">
                  <img
                    className="mr-3 h-4 w-4"
                    src="/home-icon.png"
                    alt="Course"
                  />
                  <span>{cp.course.name}</span>
                </div>
                <span className="text-sm">
                  {selectedIndex === `course_id=${cp.course_id}` ? "▼" : "▶"}
                </span>
              </div>

              {selectedIndex === `course_id=${cp.course_id}` && (
                <ul className="ml-8 mt-2 space-y-2">
                  {cp.course.activities.map((activity, idx) => (
                    <li
                      key={activity.id}
                      className={`${activity.completed ? "text-green-600" : "text-blue-600"} cursor-pointer`}
                      onClick={() =>
                        handleActivityClick(
                          getLink(cp.course_id.toString(), activity),
                          activity.completed ? true : false,
                        )
                      }
                    >
                      {idx + 1}. {splitAndCapitalize(activity.name)}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          ))}

          <div
            className={`p-4 ${selectedIndex === "completed-courses" ? "bg-blue-100" : ""}`}
            onClick={() =>
              handleNavigation(
                "/my-learning/completed-courses",
                "completed-courses",
              )
            }
          >
            <div className="flex items-center">
              <img
                className="mr-3 h-4 w-4"
                src="/completed-courses-icon.png"
                alt="Completed Courses"
              />
              <span
                className={
                  selectedIndex === "completed-courses"
                    ? "font-medium"
                    : "text-gray-600"
                }
              >
                Completed Courses
              </span>
            </div>
          </div>

          <div
            className={`p-4 ${selectedIndex === "course-catalogue" ? "bg-blue-100" : ""}`}
            onClick={() =>
              handleNavigation(
                "/my-learning/course-catalogue",
                "course-catalogue",
              )
            }
          >
            <div className="flex items-center">
              <img
                className="mr-3 h-4 w-4"
                src="/course-catalogue-icon.png"
                alt="Course Catalogue"
              />
              <span
                className={
                  selectedIndex === "course-catalogue"
                    ? "font-medium"
                    : "text-gray-600"
                }
              >
                Course Catalogue
              </span>
            </div>
          </div>

          <div
            className={`p-4 ${selectedIndex === "forum" ? "bg-blue-100" : ""}`}
            onClick={() => handleNavigation("/my-learning/forum", "forum")}
          >
            <div className="flex items-center">
              <img className="mr-3 h-4 w-4" src="/forum-icon.png" alt="Forum" />
              <span
                className={
                  selectedIndex === "forum" ? "font-medium" : "text-gray-600"
                }
              >
                Forum
              </span>
            </div>
          </div>

          <div
            className={`p-4 ${selectedIndex === "settings" ? "bg-blue-100" : ""}`}
            onClick={() =>
              handleNavigation("/my-learning/settings", "settings")
            }
          >
            <div className="flex items-center">
              <img
                className="mr-3 h-4 w-4"
                src="/settings-icon.png"
                alt="Settings"
              />
              <span
                className={
                  selectedIndex === "settings" ? "font-medium" : "text-gray-600"
                }
              >
                Settings
              </span>
            </div>
          </div>

          <div
            className={`p-4 ${selectedIndex === "help-center" ? "bg-blue-100" : ""}`}
            onClick={() =>
              handleNavigation("/my-learning/help-center", "help-center")
            }
          >
            <div className="flex items-center">
              <img
                className="mr-3 h-4 w-4"
                src="/help-center-icon.png"
                alt="Help Center"
              />
              <span
                className={
                  selectedIndex === "help-center"
                    ? "font-medium"
                    : "text-gray-600"
                }
              >
                Help Center
              </span>
            </div>
          </div>

          <div
            className="p-4 text-gray-600"
            onClick={() => handleLogout(logout)}
          >
            <div className="flex items-center">
              <img
                className="mr-3 h-4 w-4"
                src="/logout-icon.png"
                alt="Logout"
              />
              <span>Logout</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileMenu;
