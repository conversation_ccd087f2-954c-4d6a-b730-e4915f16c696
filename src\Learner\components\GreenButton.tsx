import { ButtonHTMLAttributes, ReactNode } from "react";

interface Props extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
}

export default function GreenButton({ children, ...props }: Props) {
  return (
    <button
      {...props}
      className="mx-auto h-11 w-full max-w-xs rounded-lg bg-gradient-to-r from-[#358139] to-[#358139] text-center text-[#f5f5f5] md:w-60"
    >
      {children}
    </button>
  );
}
