import React from "react";

interface ListeningData {
  facial_expressions: number;
  eye_contact: number;
  body_movement_and_posture: number;
  gestures: number;
  tone_and_manner_of_speech: number;
  choice_of_words: number;
}

interface ListeningComponentProps {
  listeningData: ListeningData;
  youDidWellAt: string[];
  youCanImproveOn: string[];
}

const ListeningComponent: React.FC<ListeningComponentProps> = ({
  listeningData,
  youDidWellAt,
  youCanImproveOn,
}) => {
  // Helper function to display stars
  const renderStars = (rating: number) => {
    const fullStar = "★";
    const emptyStar = "☆";
    return (
      <>
        {[...Array(5)].map((_, i) => (
          <span key={i} className="text-black">
            {i < rating ? fullStar : emptyStar}
          </span>
        ))}
      </>
    );
  };

  return (
    <div className="mx-0 w-full rounded-[24px] bg-yellow-300 p-4 text-black shadow-lg md:mx-2 md:rounded-[44px] md:p-6">
      {/* Title */}
      <h2 className="mb-4 text-center text-xl font-semibold">Listening</h2>

      {/* Behavior Analysis */}
      <div className="mb-4 text-left">
        <h3 className="mb-2 text-base md:text-lg">
          Results are out of 5 stars
        </h3>
        <ul className="list-none space-y-2 text-sm md:text-base">
          <li>
            <strong>Facial Expressions:</strong>{" "}
            {renderStars(listeningData.facial_expressions)}
          </li>
          <li>
            <strong>Eye Contact:</strong>{" "}
            {renderStars(listeningData.eye_contact)}
          </li>
          <li>
            <strong>Body Movement and Posture:</strong>{" "}
            {renderStars(listeningData.body_movement_and_posture)}
          </li>
          <li>
            <strong>Gestures:</strong> {renderStars(listeningData.gestures)}
          </li>
          {/* <li><strong>Tone and Manner of Speech:</strong> {renderStars(listeningData.tone_and_manner_of_speech)}</li> */}
          {/* <li><strong>Choice of Words:</strong> {renderStars(listeningData.choice_of_words)}</li> */}
        </ul>
      </div>

      {/* Positive Aspects */}
      {youDidWellAt && youDidWellAt.length > 0 ? (
        <div className="mb-4 text-left">
          <h3 className="mb-2 text-base font-semibold md:text-lg">
            You did well at the following:
          </h3>
          <ul className="list-none space-y-2 text-sm md:text-base">
            {youDidWellAt.map((item, index) => (
              <li key={index}>- {item ? item : "N/A"}</li>
            ))}
          </ul>
        </div>
      ) : null}

      {/* Areas for Improvement */}
      {youCanImproveOn && youCanImproveOn.length > 0 ? (
        <div className="mb-4 text-left">
          <h3 className="mb-2 text-base font-semibold md:text-lg">
            You can improve by focusing on the following:
          </h3>
          <ul className="list-none space-y-2 text-sm md:text-base">
            {youCanImproveOn.map((item, index) => (
              <li key={index}>- {item ? item : "N/A"}</li>
            ))}
          </ul>
        </div>
      ) : null}
    </div>
  );
};

export default ListeningComponent;
