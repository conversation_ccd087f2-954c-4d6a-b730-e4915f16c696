import React from "react";

const EditIcon = () => (
  <svg
    width="32"
    height="23"
    viewBox="0 0 32 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="32" height="23" rx="2" fill="url(#paint0_linear_214_109)" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.1979 6C19.9612 6 19.7269 6.04273 19.5082 6.12575C19.2896 6.20876 19.0909 6.33044 18.9236 6.48384L15.3068 9.79918C15.0319 10.051 14.844 10.372 14.7675 10.7214L14.4333 12.2549C14.392 12.4447 14.4568 12.6409 14.6062 12.7778C14.7555 12.9146 14.9696 12.974 15.1766 12.936L16.849 12.6292C17.23 12.5593 17.5799 12.3879 17.8551 12.1356L21.4722 8.82001C21.6395 8.66662 21.7723 8.48451 21.8628 8.28409C21.9534 8.08367 22 7.86886 22 7.65193C22 7.43499 21.9534 7.22018 21.8628 7.01976C21.7723 6.81934 21.6395 6.63723 21.4722 6.48384C21.3048 6.33044 21.1062 6.20876 20.8875 6.12575C20.6689 6.04273 20.4345 6 20.1979 6ZM19.9916 7.1955C20.057 7.17067 20.1271 7.15789 20.1979 7.15789C20.2687 7.15789 20.3387 7.17067 20.4041 7.1955C20.4695 7.22033 20.5289 7.25672 20.579 7.30259C20.629 7.34847 20.6687 7.40293 20.6958 7.46287C20.7229 7.52281 20.7368 7.58705 20.7368 7.65193C20.7368 7.7168 20.7229 7.78105 20.6958 7.84098C20.6687 7.90092 20.629 7.95538 20.579 8.00126L20.0068 8.52579L19.2444 7.82728L19.8168 7.30259C19.8668 7.25672 19.9262 7.22033 19.9916 7.1955ZM18.3512 8.64604L19.1136 9.34454L16.9619 11.3169C16.8632 11.4073 16.7381 11.4687 16.6012 11.4938L15.8576 11.6302L16.006 10.9492C16.0335 10.824 16.1008 10.7088 16.1994 10.6185L18.3512 8.64604Z"
      fill="white"
    />
    <path
      d="M11.4482 8.48536C11.5666 8.37679 11.7272 8.31579 11.8948 8.31579H13.7895C14.1383 8.31579 14.4211 8.05659 14.4211 7.73684C14.4211 7.4171 14.1383 7.15789 13.7895 7.15789H11.8948C11.3922 7.15789 10.9103 7.34088 10.555 7.6666C10.1996 7.99233 10 8.4341 10 8.89474V15.2632C10 15.7238 10.1996 16.1656 10.555 16.4913C10.9103 16.817 11.3922 17 11.8948 17H18.8422C19.3447 17 19.8266 16.817 20.182 16.4913C20.5373 16.1656 20.7369 15.7238 20.7369 15.2632V13.5263C20.7369 13.2066 20.4542 12.9474 20.1054 12.9474C19.7565 12.9474 19.4738 13.2066 19.4738 13.5263V15.2632C19.4738 15.4167 19.4072 15.564 19.2888 15.6725C19.1703 15.7811 19.0097 15.8421 18.8422 15.8421H11.8948C11.7272 15.8421 11.5666 15.7811 11.4482 15.6725C11.3297 15.564 11.2632 15.4167 11.2632 15.2632V8.89474C11.2632 8.74119 11.3297 8.59393 11.4482 8.48536Z"
      fill="white"
    />
    <defs>
      <linearGradient
        id="paint0_linear_214_109"
        x1="-1.78814e-07"
        y1="11.5"
        x2="32"
        y2="11.5"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2B3D59" />
        <stop offset="1" stopColor="#375685" />
      </linearGradient>
    </defs>
  </svg>
);

const UserTable = () => {
  const tableData = [
    {
      id: 17566,
      name: "Alex Hales",
      organization: "Ministry",
      course: "Course 1",
      role: "Learner",
    },
    {
      id: 17566,
      name: "Alex Hales",
      organization: "Ministry",
      course: "Course 1",
      role: "Learner",
    },
    {
      id: 17566,
      name: "Alex Hales",
      organization: "Ministry",
      course: "Course 1",
      role: "Learner",
    },
    {
      id: 17566,
      name: "Alex Hales",
      organization: "Ministry",
      course: "Course 1",
      role: "Learner",
    },
    {
      id: 17566,
      name: "Alex Hales",
      organization: "Ministry",
      course: "Course 1",
      role: "Learner",
    },
    {
      id: 17566,
      name: "Alex Hales",
      organization: "Ministry",
      course: "Course 1",
      role: "Learner",
    },
    {
      id: 17566,
      name: "Alex Hales",
      organization: "Ministry",
      course: "Course 1",
      role: "Learner",
    },
    {
      id: 17566,
      name: "Alex Hales",
      organization: "Ministry",
      course: "Course 1",
      role: "Learner",
    },
    // Repeat the objects as needed for other rows
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border border-gray-200 bg-gray-100">
        <thead>
          <tr className="bg-gray-200 text-left text-gray-700">
            <th className="border-b border-gray-300 px-4 py-2">Name</th>
            <th className="border-b border-gray-300 px-4 py-2">Id</th>
            <th className="border-b border-gray-300 px-4 py-2">Organization</th>
            <th className="border-b border-gray-300 px-4 py-2">Course</th>
            <th className="border-b border-gray-300 px-4 py-2">Role</th>
            <th className="border-b border-gray-300 px-4 py-2">Edit</th>
          </tr>
        </thead>
        <tbody>
          {tableData.map((row, index) => (
            <tr key={index} className="text-gray-800">
              <td className="border-b border-gray-300 px-4 py-2">{row.name}</td>
              <td className="border-b border-gray-300 px-4 py-2">{row.id}</td>
              <td className="border-b border-gray-300 px-4 py-2">
                {row.organization}
              </td>
              <td className="border-b border-gray-300 px-4 py-2">
                <select className="bg-transparent">
                  <option value={row.course}>{row.course}</option>
                  {/* Add more course options as needed */}
                </select>
              </td>
              <td className="border-b border-gray-300 px-4 py-2">{row.role}</td>
              <td className="border-b border-gray-300 px-4 py-2">
                <button className="rounded text-white hover:ring-2">
                  <EditIcon />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default UserTable;
