import React, { useState } from 'react';
import CloseIcon from "@mui/icons-material/Close";

interface EditQuestionListProps {
  surveyType: string;
  initialQuestions: { question: string; options: string[] }[];
  onSave: (questions: { question: string; options: string[] }[]) => void;
}

const EditFeedbackQuestionList: React.FC<EditQuestionListProps> = ({ surveyType, initialQuestions, onSave }) => {
  const [questions, setQuestions] = useState<{ question: string; options: string[] }[]>(initialQuestions);

  const handleQuestionChange = (index: number, value: string) => {
    const updatedQuestions = questions.map((q, i) =>
      i === index ? { ...q, question: value } : q
    );
    setQuestions(updatedQuestions);
  };

  const handleOptionChange = (qIndex: number, oIndex: number, value: string) => {
    const updatedQuestions = questions.map((q, i) =>
      i === qIndex
        ? {
          ...q,
          options: q.options.map((opt, optIdx) =>
            optIdx === oIndex ? value : opt
          ),
        }
        : q
    );
    setQuestions(updatedQuestions);
  };

  const handleAddOption = (qIndex: number) => {
    const updatedQuestions = questions.map((q, i) =>
      i === qIndex ? { ...q, options: [...q.options, ''] } : q
    );
    setQuestions(updatedQuestions);
  };

  const handleRemoveOption = (qIndex: number, oIndex: number) => {
    const updatedQuestions = questions.map((q, i) =>
      i === qIndex
        ? { ...q, options: q.options.filter((_, optIdx) => optIdx !== oIndex) }
        : q
    );
    setQuestions(updatedQuestions);
  };

  const handleAddQuestion = () => {
    setQuestions([...questions, { question: '', options: [] }]);
  };

  const handleRemoveQuestion = (index: number) => {
    setQuestions(questions.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    onSave(questions);
  };

  return (
    <div className="w-full mx-auto p-6 bg-gray-100 rounded-lg">
      <h2 className="text-2xl font-bold mb-4">
        Edit {surveyType === 'pre_course_survey' ? 'Pre Course Survey' : 'Feedback'} Questions
      </h2>

      {questions.map((question, index) => (
        <div key={index} className="mb-4 p-4 bg-white shadow rounded-lg">
          {/* Question Input */}
          <div className="flex items-center justify-between mb-4">
            <input
              type="text"
              value={question.question}
              onChange={(e) => handleQuestionChange(index, e.target.value)}
              className="w-full p-2 border border-gray-300 rounded mr-4"
              placeholder={`Question ${index + 1}`}
            />
            <CloseIcon
              sx={{ "&:hover": { color: "red" }, color: "gray" }}
              onClick={() => handleRemoveQuestion(index)} />
          </div>

          {/* Options List */}
          <div className="ml-4">
            <h4 className="font-semibold">Options:</h4>
            {question.options.length === 0 && <p className="text-gray-600">No options added</p>}
            {question.options.map((option, oIndex) => (
              <div key={oIndex} className="flex items-center my-2">
                <input
                  type="text"
                  value={option}
                  onChange={(e) => handleOptionChange(index, oIndex, e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded mr-4"
                  placeholder={`Option ${oIndex + 1}`}
                />
                <CloseIcon
                  sx={{ "&:hover": { color: "red" }, color: "gray" }}
                  onClick={() => handleRemoveOption(index, oIndex)} />
              </div>
            ))}
            <button
              onClick={() => handleAddOption(index)}
              className="text-blue-600 hover:text-blue-800 mt-2"
            >
              Add Option
            </button>
          </div>
        </div>
      ))}

      <div className="flex justify-between items-center mt-4">
        <button
          onClick={handleAddQuestion}
          className="mt-6 ml-4 px-4 py-2 bg-[#EEC300] text-white rounded-md w-[200px]"
        >
          Add Question
        </button>
        <button
          onClick={handleSave}
          className="mt-6 ml-4 px-4 py-2 bg-[#EEC300] text-white rounded-md w-[200px]"
        >
          Save All
        </button>
      </div>
    </div>
  );
};

export default EditFeedbackQuestionList;
