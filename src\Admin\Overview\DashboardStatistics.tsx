import React, { ReactNode } from "react";

const stats = {
  "Total-Users": {
    value: 47,
    icon: (
      <svg
        width="52"
        height="52"
        viewBox="0 0 52 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_d_0_1)">
          <path
            d="M24.98 5C13.94 5 5 13.96 5 25C5 36.04 13.94 45 24.98 45C36.04 45 45 36.04 45 25C45 13.96 36.04 5 24.98 5Z"
            fill="white"
          />
        </g>
        <path
          d="M21.2883 23.8517H20.6781C19.8454 23.8226 19.0154 23.9589 18.2396 24.252C17.4639 24.5451 16.7588 24.9887 16.1683 25.5554L16 25.7447V31.3428H18.8616V28.1651L19.2473 27.7459L19.4227 27.5499C20.3358 26.6456 21.4726 25.9802 22.7261 25.6163C22.0985 25.1559 21.6035 24.5485 21.2883 23.8517Z"
          fill="#35517C"
        />
        <path
          d="M34.8527 25.5351C34.2622 24.9684 33.5572 24.5248 32.7814 24.2317C32.0056 23.9386 31.1756 23.8024 30.3429 23.8314C30.0875 23.8321 29.8324 23.8456 29.5784 23.8719C29.2573 24.5257 28.7758 25.0942 28.1757 25.5284C29.5139 25.8852 30.7261 26.5864 31.6825 27.5566L31.8579 27.7459L32.2366 28.1651V31.3495H35V25.7244L34.8527 25.5351Z"
          fill="#35517C"
        />
        <path
          d="M20.6571 22.5333H20.8745C20.7735 21.6971 20.9257 20.8506 21.3127 20.096C21.6998 19.3414 22.3051 18.7109 23.0557 18.2806C22.7837 17.88 22.4083 17.5543 21.9664 17.3354C21.5245 17.1166 21.0312 17.0122 20.5348 17.0323C20.0385 17.0525 19.5561 17.1966 19.1348 17.4505C18.7136 17.7043 18.368 18.0593 18.1318 18.4807C17.8957 18.902 17.7771 19.3752 17.7877 19.854C17.7983 20.3328 17.9377 20.8007 18.1922 21.212C18.4468 21.6232 18.8078 21.9637 19.2399 22.2001C19.6719 22.4364 20.1603 22.5606 20.6571 22.5603V22.5333Z"
          fill="#35517C"
        />
        <path
          d="M30.0063 22.0262C30.0148 22.1816 30.0148 22.3373 30.0063 22.4927C30.1408 22.5133 30.2768 22.5246 30.413 22.5265H30.5463C31.0409 22.5011 31.5203 22.3526 31.9377 22.0956C32.3551 21.8386 32.6964 21.4818 32.9283 21.0599C33.1602 20.638 33.2748 20.1654 33.261 19.6881C33.2472 19.2108 33.1054 18.7451 32.8495 18.3363C32.5935 17.9276 32.2322 17.5896 31.8005 17.3554C31.3689 17.1213 30.8817 16.9988 30.3863 17C29.891 17.0012 29.4045 17.126 28.9741 17.3623C28.5436 17.5986 28.184 17.9382 27.9302 18.3483C28.565 18.7478 29.087 19.293 29.4493 19.9349C29.8117 20.5768 30.003 21.2954 30.0063 22.0262Z"
          fill="#35517C"
        />
        <path
          d="M25.4053 25.0551C27.1368 25.0551 28.5404 23.7021 28.5404 22.033C28.5404 20.3639 27.1368 19.0108 25.4053 19.0108C23.6739 19.0108 22.2702 20.3639 22.2702 22.033C22.2702 23.7021 23.6739 25.0551 25.4053 25.0551Z"
          fill="#35517C"
        />
        <path
          d="M25.5736 26.6642C24.6577 26.6286 23.7437 26.7719 22.8867 27.0856C22.0298 27.3993 21.2475 27.8769 20.5869 28.4897L20.4116 28.679V32.9586C20.4143 33.098 20.4455 33.2355 20.5034 33.3633C20.5613 33.4911 20.6447 33.6066 20.7489 33.7033C20.8531 33.8 20.9761 33.8759 21.1108 33.9268C21.2454 33.9777 21.3892 34.0025 21.5338 33.9998H29.5925C29.7371 34.0025 29.8808 33.9777 30.0155 33.9268C30.1502 33.8759 30.2731 33.8 30.3773 33.7033C30.4815 33.6066 30.565 33.4911 30.6228 33.3633C30.6807 33.2355 30.7119 33.098 30.7147 32.9586V28.6925L30.5463 28.4897C29.8901 27.875 29.1105 27.3961 28.2554 27.0822C27.4003 26.7683 26.4878 26.6261 25.5736 26.6642Z"
          fill="#35517C"
        />
        <defs>
          <filter
            id="filter0_d_0_1"
            x="0"
            y="0"
            width="52"
            height="52"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="1" dy="1" />
            <feGaussianBlur stdDeviation="3" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_0_1"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_0_1"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    ),
    color: "bg-[#EFBF35]",
  },
  Enrolled: {
    value: 139,
    icon: (
      <svg
        width="52"
        height="52"
        viewBox="0 0 52 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_d_0_1)">
          <path
            d="M24.98 5C13.94 5 5 13.96 5 25C5 36.04 13.94 45 24.98 45C36.04 45 45 36.04 45 25C45 13.96 36.04 5 24.98 5Z"
            fill="white"
          />
        </g>
        <path
          d="M20.3333 17C19.4493 17 18.6014 17.3512 17.9763 17.9763C17.3512 18.6014 17 19.4493 17 20.3333V24.3333H24.0187C24.1176 24.2227 24.2227 24.1176 24.3333 24.0187V17H20.3333ZM17 25.6667H23.2267C23.08 26.084 23 26.5333 23 27V30.3333C23 31.3573 23.3853 32.2933 24.0187 33H20.3333C19.4493 33 18.6014 32.6488 17.9763 32.0237C17.3512 31.3986 17 30.5507 17 29.6667V25.6667ZM33 20.3333V24.0187C32.2667 23.3627 31.3173 23 30.3333 23H27C26.5333 23 26.084 23.08 25.6667 23.2267V17H29.6667C30.5507 17 31.3986 17.3512 32.0237 17.9763C32.6488 18.6014 33 19.4493 33 20.3333ZM24.3333 26.6667C24.3333 25.3787 25.3787 24.3333 26.6667 24.3333H30.6667C31.9547 24.3333 33 25.3787 33 26.6667V30.6667C33 31.2855 32.7542 31.879 32.3166 32.3166C31.879 32.7542 31.2855 33 30.6667 33H26.6667C26.0478 33 25.4543 32.7542 25.0168 32.3166C24.5792 31.879 24.3333 31.2855 24.3333 30.6667V26.6667Z"
          fill="#35517C"
        />
        <defs>
          <filter
            id="filter0_d_0_1"
            x="0"
            y="0"
            width="52"
            height="52"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="1" dy="1" />
            <feGaussianBlur stdDeviation="3" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_0_1"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_0_1"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    ),
    color: "bg-[#28D219]",
  },
  "Not-Enrolled": {
    value: 5,
    icon: (
      <svg
        width="52"
        height="52"
        viewBox="0 0 52 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_d_0_1)">
          <path
            d="M24.98 5C13.94 5 5 13.96 5 25C5 36.04 13.94 45 24.98 45C36.04 45 45 36.04 45 25C45 13.96 36.04 5 24.98 5Z"
            fill="white"
          />
        </g>
        <path
          d="M17 19.8571C17 19.0994 17.301 18.3727 17.8368 17.8368C18.3727 17.301 19.0994 17 19.8571 17H30.1429C30.9006 17 31.6273 17.301 32.1632 17.8368C32.699 18.3727 33 19.0994 33 19.8571V24.0046C32.6686 23.7303 32.2811 23.5246 31.8571 23.4046V19.8571C31.8571 19.4025 31.6765 18.9665 31.355 18.645C31.0336 18.3235 30.5975 18.1429 30.1429 18.1429H25.5714V23.4046C25.1474 23.5246 24.76 23.7303 24.4286 24.0046V18.1429H19.8571C19.4025 18.1429 18.9665 18.3235 18.645 18.645C18.3235 18.9665 18.1429 19.4025 18.1429 19.8571V24.4286H24.0046C23.7303 24.76 23.5246 25.1474 23.4046 25.5714H18.1429V30.1429C18.1429 30.5975 18.3235 31.0336 18.645 31.355C18.9665 31.6765 19.4025 31.8571 19.8571 31.8571H23.4046C23.5246 32.2811 23.7303 32.6686 24.0046 33H19.8571C19.0994 33 18.3727 32.699 17.8368 32.1632C17.301 31.6273 17 30.9006 17 30.1429V19.8571ZM24.4286 26.4286C24.4286 25.3246 25.3246 24.4286 26.4286 24.4286H31C32.104 24.4286 33 25.3246 33 26.4286V31C33 31.5304 32.7893 32.0391 32.4142 32.4142C32.0391 32.7893 31.5304 33 31 33H26.4286C25.8981 33 25.3894 32.7893 25.0144 32.4142C24.6393 32.0391 24.4286 31.5304 24.4286 31V26.4286Z"
          fill="#35517C"
        />
        <defs>
          <filter
            id="filter0_d_0_1"
            x="0"
            y="0"
            width="52"
            height="52"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="1" dy="1" />
            <feGaussianBlur stdDeviation="3" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_0_1"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_0_1"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    ),
    color: "bg-[#D22F19]",
  },
  Learners: {
    value: 23,
    icon: (
      <svg
        width="52"
        height="52"
        viewBox="0 0 52 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_d_0_1)">
          <path
            d="M24.98 5C13.94 5 5 13.96 5 25C5 36.04 13.94 45 24.98 45C36.04 45 45 36.04 45 25C45 13.96 36.04 5 24.98 5Z"
            fill="white"
          />
        </g>
        <path
          d="M19 31V19C19 18.4696 19.214 17.9609 19.5949 17.5858C19.9759 17.2107 20.4925 17 21.0312 17H32V33H21.0312C20.4925 33 19.9759 32.7893 19.5949 32.4142C19.214 32.0391 19 31.5304 19 31ZM19 31C19 30.4696 19.214 29.9609 19.5949 29.5858C19.9759 29.2107 20.4925 29 21.0312 29H32M25.5 23.4C26.3975 23.4 27.125 22.6837 27.125 21.8C27.125 20.9163 26.3975 20.2 25.5 20.2C24.6025 20.2 23.875 20.9163 23.875 21.8C23.875 22.6837 24.6025 23.4 25.5 23.4ZM25.5 23.4C26.1465 23.4 26.7665 23.6529 27.2236 24.1029C27.6807 24.553 27.9375 25.1635 27.9375 25.8M25.5 23.4C24.8535 23.4 24.2335 23.6529 23.7764 24.1029C23.3193 24.553 23.0625 25.1635 23.0625 25.8"
          stroke="#35517C"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <defs>
          <filter
            id="filter0_d_0_1"
            x="0"
            y="0"
            width="52"
            height="52"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="1" dy="1" />
            <feGaussianBlur stdDeviation="3" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_0_1"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_0_1"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    ),
    color: "bg-[#598ABB]",
  },
  "In-Progress": {
    value: 27,
    icon: (
      <svg
        width="52"
        height="52"
        viewBox="0 0 52 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_d_0_1)">
          <path
            d="M24.98 5C13.94 5 5 13.96 5 25C5 36.04 13.94 45 24.98 45C36.04 45 45 36.04 45 25C45 13.96 36.04 5 24.98 5Z"
            fill="white"
          />
        </g>
        <path
          d="M25 16C23.22 16 21.4799 16.5278 19.9999 17.5168C18.5198 18.5057 17.3663 19.9113 16.6851 21.5558C16.0039 23.2004 15.8257 25.01 16.1729 26.7558C16.5202 28.5016 17.3774 30.1053 18.636 31.364C19.8947 32.6226 21.4984 33.4798 23.2442 33.8271C24.99 34.1743 26.7996 33.9961 28.4442 33.3149C30.0887 32.6337 31.4943 31.4802 32.4832 30.0001C33.4722 28.5201 34 26.78 34 25C33.9973 22.6139 33.0482 20.3263 31.361 18.639C29.6737 16.9518 27.3861 16.0027 25 16ZM25 32.7143C22.954 32.7143 20.9919 31.9015 19.5452 30.4548C18.0985 29.0081 17.2857 27.046 17.2857 25C17.2857 22.954 18.0985 20.9919 19.5452 19.5452C20.9919 18.0985 22.954 17.2857 25 17.2857V25L30.4521 30.4521C29.7371 31.1697 28.8874 31.739 27.9518 32.1272C27.0161 32.5155 26.013 32.715 25 32.7143Z"
          fill="#35517C"
        />
        <defs>
          <filter
            id="filter0_d_0_1"
            x="0"
            y="0"
            width="52"
            height="52"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="1" dy="1" />
            <feGaussianBlur stdDeviation="3" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_0_1"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_0_1"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    ),
    color: "bg-[#AD57C2]",
  },
  Completed: {
    value: 44,
    icon: (
      <svg
        width="52"
        height="52"
        viewBox="0 0 52 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_d_0_1)">
          <path
            d="M24.98 5C13.94 5 5 13.96 5 25C5 36.04 13.94 45 24.98 45C36.04 45 45 36.04 45 25C45 13.96 36.04 5 24.98 5Z"
            fill="white"
          />
        </g>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M25 34C29.9707 34 34 29.9707 34 25C34 20.0293 29.9707 16 25 16C20.0293 16 16 20.0293 16 25C16 29.9707 20.0293 34 25 34ZM29.8339 22.1515C29.876 22.1081 29.9089 22.0567 29.9307 22.0003C29.9525 21.9439 29.9627 21.8838 29.9608 21.8233C29.9588 21.7629 29.9447 21.7035 29.9192 21.6487C29.8938 21.5939 29.8576 21.5447 29.8127 21.5042C29.7678 21.4636 29.7153 21.4326 29.6582 21.4128C29.601 21.393 29.5405 21.385 29.4802 21.3891C29.4199 21.3933 29.361 21.4095 29.3072 21.4369C29.2533 21.4643 29.2054 21.5022 29.1665 21.5485L23.776 27.5051L20.8105 24.6742C20.7242 24.5917 20.6087 24.5469 20.4894 24.5496C20.37 24.5523 20.2567 24.6023 20.1742 24.6886C20.0917 24.7749 20.0469 24.8904 20.0496 25.0097C20.0523 25.1291 20.1023 25.2424 20.1886 25.3249L23.4889 28.4749L23.8232 28.7944L24.1333 28.4515L29.8339 22.1515Z"
          fill="#35517C"
        />
        <defs>
          <filter
            id="filter0_d_0_1"
            x="0"
            y="0"
            width="52"
            height="52"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="1" dy="1" />
            <feGaussianBlur stdDeviation="3" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_0_1"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_0_1"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    ),
    color: "bg-[#664719]",
  },
  "Total-Modules": {
    value: 4,
    icon: (
      <svg
        width="52"
        height="52"
        viewBox="0 0 52 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_d_0_1)">
          <path
            d="M24.98 5C13.94 5 5 13.96 5 25C5 36.04 13.94 45 24.98 45C36.04 45 45 36.04 45 25C45 13.96 36.04 5 24.98 5Z"
            fill="white"
          />
        </g>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M22.88 18C22.6148 18 22.3604 18.1054 22.1729 18.2929C21.9854 18.4804 21.88 18.7348 21.88 19V23.25C21.88 23.5152 21.9854 23.7696 22.1729 23.9571C22.3604 24.1446 22.6148 24.25 22.88 24.25H27.13C27.3952 24.25 27.6496 24.1446 27.8371 23.9571C28.0246 23.7696 28.13 23.5152 28.13 23.25V19C28.13 18.7348 28.0246 18.4804 27.8371 18.2929C27.6496 18.1054 27.3952 18 27.13 18H22.88ZM19 25.75C18.7348 25.75 18.4804 25.8554 18.2929 26.0429C18.1054 26.2304 18 26.4848 18 26.75V31C18 31.2652 18.1054 31.5196 18.2929 31.7071C18.4804 31.8946 18.7348 32 19 32H23.25C23.5152 32 23.7696 31.8946 23.9571 31.7071C24.1446 31.5196 24.25 31.2652 24.25 31V26.75C24.25 26.4848 24.1446 26.2304 23.9571 26.0429C23.7696 25.8554 23.5152 25.75 23.25 25.75H19ZM25.75 26.75C25.75 26.4848 25.8554 26.2304 26.0429 26.0429C26.2304 25.8554 26.4848 25.75 26.75 25.75H31C31.2652 25.75 31.5196 25.8554 31.7071 26.0429C31.8946 26.2304 32 26.4848 32 26.75V31C32 31.2652 31.8946 31.5196 31.7071 31.7071C31.5196 31.8946 31.2652 32 31 32H26.75C26.4848 32 26.2304 31.8946 26.0429 31.7071C25.8554 31.5196 25.75 31.2652 25.75 31V26.75Z"
          fill="#35517C"
        />
        <defs>
          <filter
            id="filter0_d_0_1"
            x="0"
            y="0"
            width="52"
            height="52"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="1" dy="1" />
            <feGaussianBlur stdDeviation="3" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_0_1"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_0_1"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    ),
    color: "bg-[#A8AAAB]",
  },
};
type StatKeys = keyof typeof stats;

const StatCard = ({
  value,
  title,
  icon,
  color,
}: {
  value: number;
  title: string;
  icon: ReactNode;
  color: string;
}) => (
  <div
    className={`flex h-[123px] min-w-[239px] items-center rounded-[10px] ${color} cursor-pointer justify-around gap-4 bg-opacity-10 p-2 hover:bg-opacity-20`}
  >
    <div className="flex flex-col space-y-[12px]">
      <h1 className="text-[35px] font-bold text-[#565656]">{value}</h1>
      <h1 className="text-2xl font-medium text-[#939393]">{title}</h1>
    </div>
    {icon}
  </div>
);

function DashboardStatistics() {
  return (
    <div className="flex flex-col space-y-8">
      <h1 className="text-2xl font-medium text-[#6f6f6f]">
        Dashboard Statistics
      </h1>
      <div className="grid grid-cols-3 gap-7">
        {Object.keys(stats).map((k) => {
          const key = k as StatKeys;
          return (
            <StatCard
              key={key}
              color={stats[key].color}
              icon={stats[key].icon}
              value={stats[key].value}
              title={key.replace("-", " ")}
            />
          );
        })}
      </div>
    </div>
  );
}
export default DashboardStatistics;
