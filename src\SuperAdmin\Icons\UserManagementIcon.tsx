import React from "react";

interface Props {
  selected?: boolean;
}

function UserManagementIcon({ selected }: Props) {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.5 8.5C10.7091 8.5 12.5 6.70914 12.5 4.5C12.5 2.29086 10.7091 0.5 8.5 0.5C6.29086 0.5 4.5 2.29086 4.5 4.5C4.5 6.70914 6.29086 8.5 8.5 8.5ZM8.5 10.5C5.73858 10.5 0.5 11.8954 0.5 14.5V16.5H16.5V14.5C16.5 11.8954 11.2614 10.5 8.5 10.5Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default UserManagementIcon;
