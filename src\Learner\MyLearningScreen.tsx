import PlungeActivity from "./PlungeActivity/PlungeActivity";
import Quiz from "./Quiz/Quiz";
import ActivityContainer from "./AIActivity/AIActivityContainer-copy";
import SideBar from "./components/SideBar";
import TopBar from "./components/TopBar";
import CourseGrid from "./CoursesCatalogue/CourseGrid";
import FeedbackForm from "./Feedback/FeedbackForm";
import HelpCenterGrid from "./HelpCenter/HelpCenterGrid";
import SettingsHome from "./Settings/SettingsHome";
import CertificateCard from "./CompletedCourses/CertificateCard";
import Tutorial from "./Tutorial/Tutorial";
import PreCourseSurvey from "./PreCourseSurvey/PreCourseSurvey";
import HardwareCard from "./HardwareTest/HardwareCard";
import Forum from "./Forum/Forum";
import Recap from "./Recap/Recap";
import AdditionalResources from "./AdditionalResources/AdditionalResources";
import CourseContextTest from "./Test/CourseContextTest";

// import ViewQuiltIcon from "@mui/icons-material/ViewQuilt";
// import LocalLibraryIcon from "@mui/icons-material/LocalLibrary";
import { Routes, Route, useLocation } from "react-router-dom";
import ContentBox from "./components/ContentBox";
import ProgressBar from "./components/ProgressBar";
import { useGetCourseProgress } from "./hooks/useGetCourseProgress";
import "./components/GlobalStyle.css";
import "./components/ResponsiveStyles.css";
import { useState } from "react";
import { CourseProvider } from "./context/CourseContext";

function MyLearningScreen() {
  const { data: courses_progress } = useGetCourseProgress();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuClose = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <CourseProvider>
      <div className="mb-12 flex min-h-screen flex-grow flex-col">
        <TopBar onMenuToggle={handleMenuToggle} />
        <div className="learner-main-container flex w-full flex-col md:flex-row">
          <SideBar
            isMobileMenuOpen={isMobileMenuOpen}
            onMobileMenuClose={handleMenuClose}
          />
          <ContentBox heading={HeadingText()}>
            <Routes>
              <Route
                path="/practice_activity"
                element={<ActivityContainer />}
              ></Route>
              <Route path="course-catalogue" element={<CourseGrid />}></Route>
              <Route path="feedback" element={<FeedbackForm />}></Route>
              <Route path="help-center" element={<HelpCenterGrid />}></Route>
              <Route path="settings" element={<SettingsHome />}></Route>
              <Route
                path="completed-courses"
                element={<CertificateCard />}
              ></Route>
              <Route path="feedback" element={<FeedbackForm />}></Route>
              <Route path="forum" element={<Forum />}></Route>
              <Route path="hardware_test" element={<HardwareCard />}></Route>
              <Route
                path="plunge_activity"
                element={<PlungeActivity />}
              ></Route>
              <Route path="quiz" element={<Quiz />}></Route>
              <Route
                path="pre_course_survey"
                element={<PreCourseSurvey />}
              ></Route>
              <Route path="tutorial" element={<Tutorial />}></Route>
              <Route path="video_example" element={<Tutorial />}></Route>
              <Route path="recap" element={<Recap />}></Route>
              <Route
                path="additional_resources"
                element={<AdditionalResources />}
              ></Route>
              <Route
                path="boss_challenge"
                element={<ActivityContainer />}
              ></Route>
              <Route
                path="test-course-context"
                element={<CourseContextTest />}
              ></Route>
            </Routes>
          </ContentBox>
        </div>
      </div>
    </CourseProvider>
  );
}

function HeadingText() {
  const location = useLocation();

  let headerText = "";
  switch (location.pathname) {
    case "/my-learning/course-catalogue":
      headerText = "Course Catalogue";
      break;
    case "/my-learning/feedback":
      headerText = "Post Module Feedback";
      break;
    case "/my-learning/plunge_activity":
      headerText = "Let's Plunge in!";
      break;
    case "/my-learning/quiz":
      headerText = "Quiz";
      break;
    case "/my-learning/help-center":
      headerText = "Help Center";
      break;
    case "/my-learning/settings":
      headerText = "Settings";
      break;
    case "/my-learning/completed-courses":
      headerText = "Completed Courses";
      break;
    case "/my-learning/tutorial":
      headerText = "Tutorial";
      break;
    case "/my-learning/video_example":
      headerText = "Video Example";
      break;
    case "/my-learning/pre_course_survey":
      headerText = "Pre Course Survey";
      break;
    case "/my-learning/recap":
      headerText = "Recap";
      break;
    case "/my-learning/hardware_test":
      headerText = "Hardware Test";
      break;
    case "/my-learning/forum":
      headerText = "Forum";
      break;
    case "/my-learning/boss_challenge":
      headerText = "Final Practicum";
      break;
    case "/my-learning/additional_resources":
      headerText = "Additional Resources";
      break;
    case "/my-learning/test-course-context":
      headerText = "Course Context Test";
      break;

    default:
      headerText = "Practice Activity";
  }

  return headerText;
}

export default MyLearningScreen;
