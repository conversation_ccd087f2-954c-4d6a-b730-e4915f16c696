import React from "react";

function ToggleButton() {
  return (
    <label className="inline-flex w-fit cursor-pointer items-center gap-7">
      <input type="checkbox" value="" className="peer sr-only" />
      <div className="peer relative h-6 w-11 rounded-full bg-[#CACACA] after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rtl:peer-checked:after:-translate-x-full"></div>
      <span className="ms-3 text-[16px] font-medium text-gray-900">
        Turn On
      </span>
    </label>
  );
}

export default ToggleButton;
