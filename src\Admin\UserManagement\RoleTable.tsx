import React, { useState, useEffect } from "react";
import { User } from "../../types/User";
import Spinner from "../../Learner/components/Spinner";
import { useUpdateUser } from "../hooks/useUpdateUser";
import showError from "../../Learner/scripts/showErrorDialog";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import UserTable from "./PaginatedRoleTable";

const UserRoleTable: React.FC = () => {
  const mutation = useUpdateUser();

  const handleRoleChange = (userId: number | undefined, newRole: User["role"]) => {
    mutation.mutate(
      {
        userId: userId? userId.toLocaleString(): "",
        role: newRole
      },
      {
        onSuccess: () => {
          showSuccess("Success", "User Updated Successfully!");
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  const roles: User["role"][] = ["learner", "instructor", "admin"];

  return (
    <>
      <Spinner loading={mutation.isPending && !mutation.isError} />
      <div className="container mx-auto">
        <UserTable roles={roles} handleRoleChange={handleRoleChange}/>
      </div>
    </>
  );
};

export default UserRoleTable;
