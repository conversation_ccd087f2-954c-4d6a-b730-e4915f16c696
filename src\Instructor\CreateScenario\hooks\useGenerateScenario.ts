import { useQuery } from "@tanstack/react-query";
import useAxios from "../../../hooks/axiosObject";

type ScenarioInput = {
  avatarId: string;
  tutorialNature: string;
  targetAudience: string;
  learningOutcome: string;
  instructions?: string;
  additionalInfo?: string;
};

type ScenarioResponse = {
  success: boolean;
  message: string;
  data: {
    scripts: string[];
  };
};

export const useGenerateScenario = (input: ScenarioInput) => {
  const axiosInstance = useAxios();

  return useQuery({
    queryKey: ["generate-scenario", input],
    queryFn: async () => {
      const payload = {
        industry: input.tutorialNature,
        simulation_objective:
          input.additionalInfo || "Enhance professional skills",
        role: input.targetAudience,
        learning_outcome: input.learningOutcome,
      };

      const response = await axiosInstance.post<ScenarioResponse>(
        "/api/v1/simulation/generate-scripts",
        payload,
      );

      return response.data;
    },
    select: (data) => ({
      scenarios: data.data.scripts,
    }),
    enabled: Boolean(
      input.avatarId &&
        input.tutorialNature &&
        input.targetAudience &&
        input.learningOutcome,
    ),
  });
};
