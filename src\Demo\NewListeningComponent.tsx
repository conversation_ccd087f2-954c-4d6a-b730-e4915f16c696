import React from "react";

type Props = {
  data: any;
};

const renderStars = (rating: number) => {
  const fullStar = "★";
  const emptyStar = "☆";
  return (
    <>
      {[...Array(5)].map((_, i) => (
        <span key={i} className="text-white">
          {i < rating ? fullStar : emptyStar}
        </span>
      ))}
    </>
  );
};

export default function NewListeningComponent({ data }: Props) {
  const { listeningEvaluation, overallScore, strengths, improvements } = data;
  return (
    <>
      <div className="mx-2 rounded-[44px] bg-[#22409A] p-6 text-left text-white shadow-lg">
        {/* Title */}
        <h2 className="mb-4 text-center text-xl font-semibold">Listening</h2>
        <DisplayListeningEvaluation data={listeningEvaluation} />

        <h2>
          <strong>Overall Score: </strong>
          {renderStars(overallScore)}
        </h2>

        <h2>
          <strong>Strengths: </strong>
          <p>{strengths}</p>
        </h2>

        <h2>
          <strong>Areas for Improvement: </strong>
          <p>{improvements}</p>
        </h2>
      </div>
    </>
  );
}

type ListeningEvalProps = {
  data: any;
};

function DisplayListeningEvaluation({ data }: ListeningEvalProps) {
  const keys = Object.keys(data);

  return (
    <>
      {keys.map((key) => {
        return (
          <div key={key} className="mb-4">
            <h3>
              <strong>{key}</strong>
            </h3>
            <p>
              <strong>Explanation: </strong>
              {data[key]["explanation"]}
            </p>
            <p>
              <strong>Score: </strong>
              {renderStars(data[key]["score"])}
            </p>
          </div>
        );
      })}
    </>
  );
}
