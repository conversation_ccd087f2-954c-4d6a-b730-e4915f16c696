import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { ActivityDeleteResponse } from "../../types/Activity";
import { AxiosInstance } from "axios";

// api call

const deleteActivity = async (
  axiosObject: AxiosInstance,
  activityId: string,
): Promise<ActivityDeleteResponse> => {
  const response = await axiosObject.delete<ActivityDeleteResponse>(
    "/api/v1/activity/delete/" + activityId
  );
  console.log(response);
  return response.data;
};

export function useDeleteActivity() {
  const axiosInstance = useAxios();
  // run the query
  return useMutation({
    mutationFn: ({ activityId }: {
      activityId: string,
    }) => {
      return deleteActivity(axiosInstance, activityId);
    },
  })
}
