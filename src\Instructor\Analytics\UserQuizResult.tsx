import React from "react";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';

interface QuizResultProps {
  user_input: {
    id: number;
    question: string;
    options: string[];
    index: number;
    selectedOption: string;
  }[];
  result: {
    mcq_result: {
      id: number;
      result: boolean;
      right_option: string;
    }[];
    correct: number;
    incorrect: number;
    percentage: number;
  };
}

const QuizResult: React.FC<QuizResultProps> = ({ user_input, result }) => {
  const getResultIcon = (isCorrect: boolean) => {
    return isCorrect ? (
      <CheckCircleIcon className="text-green-500 h-6 w-6 inline-block" />
    ) : (
      <CancelIcon className="text-red-500 h-6 w-6 inline-block" />
    );
  };

  return (
    <div className="space-y-6">
      {user_input.map((question) => {
        const questionResult = result.mcq_result.find(
          (res) => res.id === question.id
        );

        return (
          <div key={question.id} className="p-4 bg-white rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">
              {question.question}
            </h3>
            <ul className="space-y-2">
              {question.options.map((option, idx) => {
                const isSelected = option === question.selectedOption;
                const isCorrectOption = option === questionResult?.right_option;
                const isCorrect = questionResult?.result;

                return (
                  <li
                    key={idx}
                    className={`p-3 border rounded-lg text-gray-700 transition-colors duration-200 ${
                      isSelected ? "bg-gray-100" : ""
                    } ${isSelected && !isCorrect ? "border-red-500" : ""} ${
                      isSelected && isCorrect ? "border-green-500" : ""
                    }`}
                  >
                    <span className="mr-2">{option}</span>
                    {isSelected && (
                      <span className="ml-2">
                        {getResultIcon(isCorrect || false)}
                      </span>
                    )}
                    {isCorrectOption && (
                      <span className="ml-2 text-green-600 font-semibold">
                        (Correct Answer)
                      </span>
                    )}
                  </li>
                );
              })}
            </ul>
          </div>
        );
      })}
      <div className="p-4 bg-blue-50 rounded-lg shadow-md mt-6">
        <p className="text-gray-800 font-medium">Correct: {result.correct}</p>
        <p className="text-gray-800 font-medium">Incorrect: {result.incorrect}</p>
        <p className="text-gray-800 font-medium">
          Percentage: {result.percentage}%
        </p>
      </div>
    </div>
  );
};

export default QuizResult;
