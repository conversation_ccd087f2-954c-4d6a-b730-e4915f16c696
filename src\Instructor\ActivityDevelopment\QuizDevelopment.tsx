import React, { useState } from 'react';
import { Question } from '../../Learner/hooks/useQuestion';
import CloseIcon from "@mui/icons-material/Close";

interface EditQuestionsProps {
  initialQuestions: Question[];
  onSave: (questions: Question[]) => void;
}

export default function EditQuestions({ initialQuestions, onSave }: EditQuestionsProps) {
  const [questions, setQuestions] = useState<Question[]>(initialQuestions);

  const handleAddQuestion = () => {
    setQuestions([
      ...questions,
      { id: null, question: '', options: ['', '', '', ''], right_option: '' },
    ]);
  };

  const handleQuestionChange = (index: number, value: string) => {
    const updatedQuestions = questions.map((q, i) =>
      i === index ? { ...q, question: value } : q
    );
    setQuestions(updatedQuestions);
  };

  const handleOptionChange = (qIndex: number, oIndex: number, value: string) => {
    const updatedQuestions = questions.map((q, i) =>
      i === qIndex ? { ...q, options: q.options.map((opt, j) => (j === oIndex ? value : opt)) } : q
    );
    setQuestions(updatedQuestions);
  };

  const handleCorrectOptionChange = (qIndex: number, value: string) => {
    const updatedQuestions = questions.map((q, i) =>
      i === qIndex ? { ...q, right_option: value } : q
    );
    setQuestions(updatedQuestions);
  };

  const handleRemoveQuestion = (index: number) => {
    setQuestions(questions.filter((_, i) => i !== index));
  };

  const handleRemoveOption = (qIndex: number, oIndex: number) => {
    const updatedQuestions = questions.map((q, i) => {
      if (i === qIndex && q.options.length > 2) {
        const newOptions = q.options.filter((_, j) => j !== oIndex);
        return { ...q, options: newOptions };
      }
      return q;
    });
    setQuestions(updatedQuestions);
  };

  const handleAddOption = (qIndex: number) => {
    const updatedQuestions = questions.map((q, i) => {
      if (i === qIndex && q.options.length < 5) {
        return { ...q, options: [...q.options, ''] };
      }
      return q;
    });
    setQuestions(updatedQuestions);
  };

  const handleSave = () => {
    onSave(questions);
  };

  return (
    <>
      <div className="max-w-4xl mx-auto p-6 bg-yellow-50 rounded-md">
        <h2 className="text-2xl font-semibold mb-4">Edit Multiple Choice Questions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {questions.map((question, qIndex) => (
            <div key={qIndex} className="p-4 bg-gray-50 rounded-md shadow-sm">
              <input
                type="text"
                className="w-full p-2 mb-4 border border-gray-300 rounded-md"
                placeholder={`Question ${qIndex + 1}`}
                value={question.question}
                onChange={(e) => handleQuestionChange(qIndex, e.target.value)}
              />
              {question.options.map((option, oIndex) => (
                <div key={oIndex} className="flex items-center mb-2">
                  <input
                    type="radio"
                    className="mr-2"
                    checked={Boolean(option) && question.right_option === option}
                    onChange={() => handleCorrectOptionChange(qIndex, option)}
                  />
                  <input
                    type="text"
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder={`Option ${oIndex + 1}`}
                    value={option}
                    onChange={(e) => handleOptionChange(qIndex, oIndex, e.target.value)}
                  />
                  {question.options.length > 2 && (
                    <button
                      className="ml-2 text-red-200 hover:text-red-800"
                      onClick={() => handleRemoveOption(qIndex, oIndex)}
                    >
                      <CloseIcon sx={{ "&:hover": { color: "red" } }} />
                    </button>
                  )}
                </div>
              ))}
              {question.options.length < 5 && (
                <button
                  className="mt-2 text-green-600 hover:text-green-800 w-full"
                  onClick={() => handleAddOption(qIndex)}
                >
                  + Add Option
                </button>
              )}
              <button
                className="mt-2 text-red-600 hover:text-red-800"
                onClick={() => handleRemoveQuestion(qIndex)}
              >
                Remove Question
              </button>
            </div>
          ))}
        </div>

        <div className="flex w-full justify-center">
          <button
            className="mt-6 px-4 py-2 bg-[#EEC300] text-white rounded-md w-[200px]"
            onClick={handleAddQuestion}
          >
            Add Question
          </button>
        </div>
      </div>

      <div className="w-full text-right">
        <button
          className="mt-6 ml-4 px-4 py-2 bg-[#EEC300] text-white rounded-md w-[200px]"
          onClick={handleSave}
        >
          Save
        </button>
      </div>
    </>
  );
}
