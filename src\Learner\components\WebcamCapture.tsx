import GradientButton from "./GradientButton";
import React, { useRef, useEffect, useState } from "react";

interface WebcamCaptureProps {
  onStart: () => void;
  onStop: () => void;
  onUpload: (videoBlob: Blob) => void;
}

const WebcamCapture: React.FC<WebcamCaptureProps> = ({
  onStart,
  onStop,
  onUpload,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const recordedVideoRef = useRef<HTMLVideoElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const [recording, setRecording] = useState(false);
  const [videoBlob, setVideoBlob] = useState<Blob | null>(null);

  useEffect(() => {
    async function initWebcam() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          videoRef.current.play();
        }
      } catch (error) {
        console.error("Error accessing webcam: ", error);
      }
    }

    if (recording) {
      initWebcam();
    }

    return () => {
      if (videoRef.current && videoRef.current.srcObject) {
        const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
        tracks.forEach((track) => track.stop());
      }
    };
  }, [recording]);

  const startRecording = async () => {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          videoRef.current.play();
        }

        const options = { mimeType: "video/mp4; codecs=vp9" };
        const mediaRecorder = new MediaRecorder(stream, options);
        mediaRecorderRef.current = mediaRecorder;

        const chunks: Blob[] = [];
        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            chunks.push(event.data);
          }
        };

        mediaRecorder.onstop = () => {
          console.log("video is stopped");
          onStop();
          const blob = new Blob(chunks, { type: "video/mp4" });
          setVideoBlob(blob);
          mediaRecorder.stop()
        };

        mediaRecorder.start();
        setRecording(true);
        onStart();
      } catch (err) {
        console.error("Error accessing webcam: ", err);
      }
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setRecording(false);
      onStop();
    }
    if (videoRef.current && videoRef.current.srcObject) {
      (videoRef.current.srcObject as MediaStream)
        .getTracks()
        .forEach((track) => track.stop());
    }
  };

  // const handleDataAvailable = (event: BlobEvent) => {
  //   if (event.data.size > 0) {
  //     setVideoBlob(event.data);
  //   }
  // };

  const handleUpload = () => {
    if (videoBlob) {
      onUpload(videoBlob);
    }
  };

  return (
    <div className="text-center">
      {recording && (
        <video
          style={{ margin: "auto" }}
          ref={videoRef}
          width="640"
          height="480"
          autoPlay
        />
      )}
      {!recording && videoBlob && (
        <div>
          <video
            style={{ margin: "auto" }}
            ref={recordedVideoRef}
            width="640"
            height="480"
            controls
          >
            <source src={URL.createObjectURL(videoBlob)} type="video/mp4" />
          </video>
        </div>
      )}
      <div>
        {!recording && (
          <GradientButton
            text="Start Recording"
            color1="#E6635A"
            color2="#D64D44"
            onClick={startRecording}
          />
        )}
        {recording && (
          <GradientButton
            text="Stop Recording"
            color1="#E6635A"
            color2="#D64D44"
            onClick={stopRecording}
          />
        )}
        {videoBlob && (
          <GradientButton
            text="Upload"
            color1="#E6635A"
            color2="#D64D44"
            onClick={handleUpload}
          />
        )}
      </div>
    </div>
  );
};

export default WebcamCapture;
