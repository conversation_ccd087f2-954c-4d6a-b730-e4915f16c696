import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Spinner from "../../Learner/components/Spinner";
import showError from "../../Learner/scripts/showErrorDialog";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import { useCreateCourse } from "../hooks/useCreateCourse";

function CourseCreation() {
  const [courseName, setCourseName] = useState<string>("");
  const navigate = useNavigate();
  const mutation = useCreateCourse();

  const handleCreateCourse = () => {
    if (!courseName.trim()) {
      showError("Error", "Please enter a course name!");
      return;
    }

    mutation.mutate(
      {
        name: courseName,
        activities: [],
      },
      {
        onSuccess: () => {
          showSuccess("Success", "Course Created Successfully!");
          navigate("/instructor/course-management");
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
        },
      },
    );
  };

  return (
    <>
      <Spinner loading={mutation.isPending} />
      <div className="flex h-fit w-full flex-col gap-8 bg-[#FEFBF2] bg-opacity-70 p-14">
        <div className="flex flex-col gap-4">
          <h2 className="text-2xl font-semibold text-[#36537F]">
            Create New Course
          </h2>
          <p className="text-[#777777]">
            Enter a name for your new course. You can add activities to your
            course after creation.
          </p>
        </div>

        <div className="flex flex-col gap-6 rounded-lg bg-white p-8 shadow-md">
          <div className="flex flex-col gap-2">
            <label
              htmlFor="courseName"
              className="text-lg font-medium text-[#36537F]"
            >
              Course Name
            </label>
            <input
              type="text"
              id="courseName"
              value={courseName}
              onChange={(e) => setCourseName(e.target.value)}
              placeholder="Enter course name"
              className="w-full rounded-md border border-gray-300 p-3 focus:border-[#36537F] focus:outline-none"
            />
          </div>

          <div className="flex justify-end">
            <button
              onClick={handleCreateCourse}
              className="rounded-lg bg-[#36537F] px-6 py-3 text-lg font-medium text-white hover:bg-blue-600"
            >
              Create Course
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

export default CourseCreation;
