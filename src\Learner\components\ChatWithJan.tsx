import { ButtonHTMLAttributes, useState } from "react";
import Chat from "./Chat";

interface Props extends ButtonHTMLAttributes<HTMLButtonElement> {}
function ChatWithJan({ ...props }: Props) {
  const [hideChatBox, setHideChatBox] = useState(true);
  return (
    <>
      {hideChatBox ? (
        <></>
      ) : (
        <div className="w-full">
          <Chat />
        </div>
      )}

      <button
        {...props}
        onClick={() => setHideChatBox(!hideChatBox)}
        className="max-w-fit rounded bg-gradient-to-r from-[#2B3D59] to-[#375685] px-6 py-2 text-white hover:from-[#3D527A] hover:to-[#4A6FA0]"
      >
        <div className="flex flex-none items-baseline space-x-4">
          <svg width="20" height="18" viewBox="0 0 20 18" fill="none">
            <path
              d="M10 0C10.4822 0 10.9447 0.189642 11.2856 0.527208C11.6266 0.864773 11.8182 1.32261 11.8182 1.8C11.8182 2.466 11.4545 3.051 10.9091 3.357V4.5H11.8182C13.5059 4.5 15.1245 5.16375 16.318 6.34523C17.5114 7.52671 18.1818 9.12914 18.1818 10.8H19.0909C19.332 10.8 19.5632 10.8948 19.7337 11.0636C19.9042 11.2324 20 11.4613 20 11.7V14.4C20 14.6387 19.9042 14.8676 19.7337 15.0364C19.5632 15.2052 19.332 15.3 19.0909 15.3H18.1818V16.2C18.1818 16.6774 17.9903 17.1352 17.6493 17.4728C17.3083 17.8104 16.8458 18 16.3636 18H3.63636C3.15415 18 2.69169 17.8104 2.35071 17.4728C2.00974 17.1352 1.81818 16.6774 1.81818 16.2V15.3H0.909091C0.667985 15.3 0.436754 15.2052 0.266267 15.0364C0.0957789 14.8676 0 14.6387 0 14.4V11.7C0 11.4613 0.0957789 11.2324 0.266267 11.0636C0.436754 10.8948 0.667985 10.8 0.909091 10.8H1.81818C1.81818 9.12914 2.48863 7.52671 3.68205 6.34523C4.87546 5.16375 6.49408 4.5 8.18182 4.5H9.09091V3.357C8.54545 3.051 8.18182 2.466 8.18182 1.8C8.18182 1.32261 8.37338 0.864773 8.71435 0.527208C9.05533 0.189642 9.51779 0 10 0ZM5.90909 9.9C5.30633 9.9 4.72825 10.1371 4.30203 10.559C3.87581 10.981 3.63636 11.5533 3.63636 12.15C3.63636 12.7467 3.87581 13.319 4.30203 13.741C4.72825 14.1629 5.30633 14.4 5.90909 14.4C6.51186 14.4 7.08993 14.1629 7.51615 13.741C7.94237 13.319 8.18182 12.7467 8.18182 12.15C8.18182 11.5533 7.94237 10.981 7.51615 10.559C7.08993 10.1371 6.51186 9.9 5.90909 9.9ZM14.0909 9.9C13.4881 9.9 12.9101 10.1371 12.4838 10.559C12.0576 10.981 11.8182 11.5533 11.8182 12.15C11.8182 12.7467 12.0576 13.319 12.4838 13.741C12.9101 14.1629 13.4881 14.4 14.0909 14.4C14.6937 14.4 15.2718 14.1629 15.698 13.741C16.1242 13.319 16.3636 12.7467 16.3636 12.15C16.3636 11.5533 16.1242 10.981 15.698 10.559C15.2718 10.1371 14.6937 9.9 14.0909 9.9Z"
              fill="white"
            />
          </svg>
          <p>Chat with your Dignity Coach</p>
        </div>
      </button>
    </>
  );
}

export default ChatWithJan;
