import { useQuery } from "@tanstack/react-query";
import useAxios from "../../../hooks/axiosObject";
import { AxiosInstance } from "axios";

type ActivityRubric = {
  activityType: string;
  activityNumber: number;
  course: string;
  parameters: ActivityParameter[];
};

type ActivityParameter = {
  parameterId: string;
  parameterName: string;
  maxScore: number;
  weight: number;
  description?: string;
  performanceLevel?: "high" | "medium" | "low";
};

const getActivityRubrics = async (axiosObject: AxiosInstance) => {
  const response = await axiosObject.get<ActivityRubric[]>(
    "api/v1/activity/get/rubric",
  );
  return response.data;
};

function useActivityRubrics() {
  const axiosObject = useAxios();

  return useQuery<ActivityRubric[]>({
    queryKey: ["activityRubrics"],
    refetchOnWindowFocus: false,
    queryFn: () => getActivityRubrics(axiosObject),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
export default useActivityRubrics;
