import { useQuery } from '@tanstack/react-query';
import useAxios from '../../hooks/axiosObject';

import { CourseProgress } from '../../types/CourseProgress';
import { AxiosInstance } from 'axios';


// api call
const getCourseProgress = async (axiosObject: AxiosInstance): Promise<CourseProgress[]> => {
  const courses_progress = await axiosObject.get<CourseProgress[]>('/api/v1/course_progress/get/all');
  console.log(courses_progress)
  return courses_progress.data;
};

// user query
export function useGetCourseProgress() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<CourseProgress[]>({
    queryKey: ['courses_progress'],
    queryFn: () => getCourseProgress(axiosObject),
    staleTime: 1 * 60 * 3000,
    gcTime: 1 * 60 * 3000,
  });
};