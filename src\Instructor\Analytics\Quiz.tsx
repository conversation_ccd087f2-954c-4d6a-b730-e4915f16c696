import React, { useState, useEffect } from 'react';
import { useGetQuizAnalytics } from '../hooks/useGetQuizAnalytics';
import Spinner from '../../Learner/components/Spinner';
import { useGetActivityResult } from '../hooks/useGetActivityResult';
import QuizResult from './UserQuizResult';
import UserSearch from './UserSearch';
import { UserSearchResponse } from '../../types/User';

interface userInput {
  id: number;
  question: string;
  options: string[];
  index: number;
  selectedOption: string;
}

interface mcqResult {
  mcq_result: {
    id: number;
    result: boolean;
    right_option: string;
  }[];
  correct: number;
  incorrect: number;
  percentage: number;
}

const QuizTable: React.FC = () => {
  const [response, setResponse] = useState<boolean>(false);
  const [noResponse, setNoResponse] = useState<boolean>(false);
  const { data: quiz, isFetching } = useGetQuizAnalytics();
  const [userId, setUserId] = useState<string>("");
  const [searchType, setSearchType] = useState<string>("average");
  const [UserInput, setUserInput] = useState<userInput[]>([]);
  const [mcqResult, setmcqResult] = useState<mcqResult>(Object);
  const { data: result, isFetching: isFetchingResult } = useGetActivityResult(userId, "quiz");

  const handleSearchTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchType(e.target.value);
  };

  const handleSelectUser = (user: UserSearchResponse) => {
    setUserId(user.id.toLocaleString());
  };

  useEffect(() => {
    if (result && result.length > 0) {
      if (result[0].user_input) {
        setUserInput(result[0].user_input);
      }
      if (result[0].result) {
        setmcqResult(result[0].result);
      }
      setResponse(true);
      setNoResponse(false);
    } else {
      setNoResponse(true);
    }
  }, [result]);

  return (
    <>
      <Spinner loading={isFetching || isFetchingResult} />
      <div className="flex w-full flex-col items-center justify-center py-5">
        <div className="flex space-x-4 mb-4">
          <label>
            <input
              type="radio"
              value="average"
              checked={searchType === "average"}
              onChange={handleSearchTypeChange}
            />
            Overall Average
          </label>
          <label>
            <input
              type="radio"
              value="user"
              checked={searchType === "user"}
              onChange={handleSearchTypeChange}
            />
            Search By User
          </label>
        </div>
      </div>
      <div className="p-4 bg-white rounded-lg shadow-md">
        {searchType === "average" ? (
          <table className="w-full text-left table-auto">
            <thead>
              <tr className="bg-gray-100">
                <th className="p-2 font-medium">Metric</th>
                <th className="p-2 font-medium">Quantity</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="p-2">Average Score</td>
                <td className="p-2">{quiz?.data.average_score}</td>
              </tr>
              <tr className="border-b">
                <td className="p-2">Average time to completion</td>
                <td className="p-2">{quiz?.data.average_time_to_complete}</td>
              </tr>
              <tr className="border-b">
                <td className="p-2">Reply Times</td>
                <td className="p-2">{quiz?.data.total_replay_time}</td>
              </tr>
            </tbody>
          </table>
        ) : (
          <>
            <UserSearch onSelect={handleSelectUser} />
            {!noResponse ? (
              <QuizResult user_input={UserInput} result={mcqResult} />
            ) : (
              <>
                {userId ? (
                  <div className="flex w-full flex-col items-center justify-center space-y-20 bg-[#F7F6E3] py-10">
                    <p>No user result was found against this activity.</p>
                  </div>
                ) : null}
              </>
            )}
          </>
        )}
      </div>
    </>
  );
};

export default QuizTable;
