import React, { ReactNode } from "react";
import TopBar from "./components/TopBar";
import SideBar from "./components/SideBar";
import ContentBox from "./components/ContentBox";

interface Props {
  children?: ReactNode;
  heading: string;
  icon?: ReactNode;
}

function Layout(props: Props) {
  return (
    <div className="flex h-screen flex-col">
      <TopBar />
      <div className="flex">
        <SideBar />
        <ContentBox
          heading={props.heading}
          children={props.children}
          icon={props.icon}
        />
      </div>
    </div>
  );
}

export default Layout;
