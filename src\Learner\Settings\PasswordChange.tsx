import React, { useState } from "react";
import GradientButton from "../components/GradientButton";
import { useChangePassword } from "../hooks/useChangePassword";
import showSuccess from "../scripts/showSuccessDialog";
import showError from "../scripts/showErrorDialog";
import Spinner from "../components/Spinner";
import { AxiosError } from "axios";

const PasswordChange: React.FC = () => {
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [rePassword, setRePassword] = useState("");
  const mutation = useChangePassword();

  const handleSubmit = async () => {
    if (oldPassword && newPassword && rePassword) {
      if (newPassword === rePassword) {
        mutation.mutate(
          { oldPassword, newPassword },
          {
            onSuccess: (data) => {
              if (data && data.message) {
                if (data.success) {
                  showSuccess(data.message, "");
                } else {
                  showError(data.message, "");
                }
              }
            },
            onError: (error) => {
              showError("Failed", "Please Check Credentials and try again.");
              console.log("Submission failed. Please try again.");
            },
          },
        );
      } else {
        showError("Failed", "New password and Retyped password are not same.");
      }
    } else {
      showError("Failed", "Please enter valid values.");
    }
  };

  return (
    <>
      <Spinner loading={mutation.isPending} />
      <div className="rounded-lg bg-white shadow-sm">
        <div className="rounded-t-lg bg-yellow-50 p-6">
          <h2 className="mb-4 text-lg font-bold text-blue-800">Password</h2>
        </div>
        <div className="bg-white p-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="flex flex-col gap-y-2">
              <label className="text-base font-medium text-[#535353]">
                Current Password
              </label>
              <input
                type="password"
                className="rounded-md border border-gray-300 bg-white p-3 text-gray-900 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                onChange={(evt) => setOldPassword(evt.target.value)}
              />
            </div>
            <div className="flex flex-col gap-y-2">
              <label className="text-base font-medium text-[#535353]">
                New Password
              </label>
              <input
                type="password"
                className="rounded-md border border-gray-300 bg-white p-3 text-gray-900 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                onChange={(evt) => setNewPassword(evt.target.value)}
              />
            </div>
            <div className="flex flex-col gap-y-2">
              <label className="text-base font-medium text-[#535353]">
                Retype Password
              </label>
              <input
                type="password"
                className="rounded-md border border-gray-300 bg-white p-3 text-gray-900 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                onChange={(evt) => setRePassword(evt.target.value)}
              />
            </div>
          </div>
        </div>
        <div className="rounded-b-lg bg-gray-50 p-6">
          <GradientButton
            text="Change Password"
            color1="#2B3D59"
            color2="#375685"
            width="250px"
            onClick={handleSubmit}
          />
        </div>
      </div>
    </>
  );
};

export default PasswordChange;
