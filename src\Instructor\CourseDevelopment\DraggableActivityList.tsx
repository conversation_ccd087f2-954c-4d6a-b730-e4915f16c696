import React, { useRef } from "react";
import { useDrag, useDrop } from "react-dnd";
import { ActivityName } from "../../types/Activity";

// Arrow component for indicating flow between activities
const FlowArrow: React.FC = () => (
  <div className="flex justify-center py-2">
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="text-[#A3A3A3]"
    >
      <path
        d="M8 2L8 14M8 14L4 10M8 14L12 10"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </div>
);

// Define the item types for drag and drop
const ACTIVITY_ITEM = "activity";
const SIDEBAR_ACTIVITY = "sidebar_activity";

// Interface for the draggable item (existing activity)
interface DragItem {
  index: number;
  id: number;
  type: string;
}

// Interface for sidebar activity being dragged
interface SidebarDragItem {
  activityType: string;
  activityName: string;
  type: string;
}

// Drop zone component for inserting activities
interface DropZoneProps {
  index: number;
  onDropSidebarActivity: (
    activityType: string,
    activityName: string,
    insertIndex: number,
  ) => void;
}

const DropZone: React.FC<DropZoneProps> = ({
  index,
  onDropSidebarActivity,
}) => {
  const [{ isOver, canDrop }, drop] = useDrop<
    SidebarDragItem,
    void,
    { isOver: boolean; canDrop: boolean }
  >({
    accept: SIDEBAR_ACTIVITY,
    drop: (item) => {
      onDropSidebarActivity(item.activityType, item.activityName, index);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const isActive = isOver && canDrop;

  return (
    <div
      ref={drop}
      className={`transition-all duration-200 ${
        isActive
          ? "h-12 border-2 border-dashed border-blue-500 bg-blue-50"
          : "h-2"
      } ${canDrop && !isActive ? "h-6 border border-dashed border-gray-300" : ""}`}
    >
      {isActive && (
        <div className="flex h-full items-center justify-center text-sm text-blue-600">
          Drop activity here
        </div>
      )}
    </div>
  );
};

// Props for the DraggableActivity component
interface DraggableActivityProps {
  activity: ActivityName;
  index: number;
  moveActivity: (dragIndex: number, hoverIndex: number) => void;
  onSelectClick?: (index: number) => void;
}

// The draggable activity component
const DraggableActivity: React.FC<DraggableActivityProps> = ({
  activity,
  index,
  moveActivity,
  onSelectClick,
}) => {
  const ref = useRef<HTMLDivElement>(null);

  // Set up drag functionality
  const [{ isDragging }, drag] = useDrag({
    type: ACTIVITY_ITEM,
    item: { index, id: activity.id, type: ACTIVITY_ITEM },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  // Set up drop functionality
  const [{ handlerId }, drop] = useDrop<
    DragItem,
    void,
    { handlerId: string | symbol | null }
  >({
    accept: ACTIVITY_ITEM,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      const hoverClientY = clientOffset!.y - hoverBoundingRect.top;

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      // Time to actually perform the action
      moveActivity(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  // Connect the drag and drop refs
  drag(drop(ref));

  // Apply styles for the dragging state
  const opacity = isDragging ? 0.4 : 1;

  return (
    <div
      ref={ref}
      style={{ opacity }}
      className="mb-2 cursor-move"
      data-handler-id={handlerId}
      aria-label={`Drag to reorder ${activity.name}`}
      role="button"
      tabIndex={0}
      onClick={() => onSelectClick && onSelectClick(index)}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onSelectClick && onSelectClick(index);
        }
      }}
    >
      <div
        className={
          "h-fit w-full rounded px-4 py-2 text-center " +
          (activity.selected ? "bg-yellow-200" : "bg-[#DAE3F2]")
        }
      >
        <h1 className="text-sm font-semibold text-[#36537F]">
          {activity.name}
        </h1>
      </div>
    </div>
  );
};

// Props for the DraggableActivityList component
interface DraggableActivityListProps {
  activities: ActivityName[];
  onMoveActivity: (activities: ActivityName[]) => void;
  onSelectClick?: (index: number) => void;
  onAddActivity: (
    activityType: string,
    activityName: string,
    insertIndex: number,
  ) => void;
}

// The main component that renders the list of draggable activities
const DraggableActivityList: React.FC<DraggableActivityListProps> = ({
  activities,
  onMoveActivity,
  onSelectClick,
  onAddActivity,
}) => {
  // Function to move an activity from one position to another
  const moveActivity = (dragIndex: number, hoverIndex: number) => {
    const draggedActivity = activities[dragIndex];
    const newActivities = [...activities];

    // Remove the dragged item
    newActivities.splice(dragIndex, 1);

    // Insert it at the new position
    newActivities.splice(hoverIndex, 0, draggedActivity);

    // Update the parent component with the new order
    onMoveActivity(newActivities);
  };

  // Handle dropping a sidebar activity at a specific position
  const handleDropSidebarActivity = (
    activityType: string,
    activityName: string,
    insertIndex: number,
  ) => {
    onAddActivity(activityType, activityName, insertIndex);
  };

  return (
    <div className="w-full">
      {/* Drop zone at the beginning */}
      <DropZone index={0} onDropSidebarActivity={handleDropSidebarActivity} />

      {activities.map((activity, index) => (
        <React.Fragment key={activity.id}>
          <DraggableActivity
            activity={activity}
            index={index}
            moveActivity={moveActivity}
            onSelectClick={onSelectClick}
          />
          {/* Show arrow between activities, but not after the last one */}
          {index < activities.length - 1 && <FlowArrow />}
          {/* Drop zone after each activity */}
          <DropZone
            index={index + 1}
            onDropSidebarActivity={handleDropSidebarActivity}
          />
        </React.Fragment>
      ))}

      {/* If no activities, show a larger drop zone */}
      {activities.length === 0 && (
        <div className="flex h-32 items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50">
          <div className="text-center text-gray-500">
            <p className="text-lg font-medium">No activities yet</p>
            <p className="text-sm">
              Drag activities from the sidebar to get started
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default DraggableActivityList;
export { SIDEBAR_ACTIVITY };
export type { SidebarDragItem };
