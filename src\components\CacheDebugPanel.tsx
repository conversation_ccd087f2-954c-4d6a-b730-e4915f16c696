import React, { useState } from 'react';
import { useCacheManager } from '../hooks/useCacheManager';

interface CacheDebugPanelProps {
  isVisible?: boolean;
  onToggle?: () => void;
}

/**
 * Debug panel for cache management
 * Only use in development or for debugging purposes
 */
const CacheDebugPanel: React.FC<CacheDebugPanelProps> = ({ 
  isVisible = false, 
  onToggle 
}) => {
  const [status, setStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    clearAllCache,
    clearBrowserStorage,
    clearReactQueryCache,
    clearSpecificQueries,
    resetZustandStores,
    getCacheStatus,
    clearIndexedDB,
  } = useCacheManager();

  const handleClearAll = async () => {
    setIsLoading(true);
    try {
      await clearAllCache();
      alert('All cache cleared successfully (auth tokens preserved)');
      updateStatus();
    } catch (error) {
      alert('Error clearing cache: ' + error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearBrowserStorage = () => {
    clearBrowserStorage();
    alert('Browser storage cleared (auth tokens preserved)');
    updateStatus();
  };

  const handleClearReactQuery = () => {
    clearReactQueryCache();
    alert('React Query cache cleared');
    updateStatus();
  };

  const handleClearIndexedDB = async () => {
    setIsLoading(true);
    try {
      await clearIndexedDB();
      alert('IndexedDB cleared');
      updateStatus();
    } catch (error) {
      alert('Error clearing IndexedDB: ' + error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetStores = () => {
    resetZustandStores();
    alert('Zustand stores reset');
    updateStatus();
  };

  const handleClearSpecific = () => {
    const keys = prompt('Enter query keys to clear (comma-separated):');
    if (keys) {
      const keyArray = keys.split(',').map(k => k.trim());
      clearSpecificQueries(keyArray);
      alert(`Cleared queries: ${keyArray.join(', ')}`);
      updateStatus();
    }
  };

  const updateStatus = () => {
    const currentStatus = getCacheStatus();
    setStatus(currentStatus);
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-2 rounded-lg shadow-lg hover:bg-blue-600 z-50"
        title="Open Cache Debug Panel"
      >
        🗂️ Cache
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Cache Debug Panel</h3>
        <button
          onClick={onToggle}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      <div className="space-y-2 mb-4">
        <button
          onClick={handleClearAll}
          disabled={isLoading}
          className="w-full bg-red-500 text-white px-3 py-2 rounded hover:bg-red-600 disabled:opacity-50"
        >
          {isLoading ? 'Clearing...' : 'Clear All Cache (Keep Auth)'}
        </button>

        <button
          onClick={handleClearBrowserStorage}
          className="w-full bg-orange-500 text-white px-3 py-2 rounded hover:bg-orange-600"
        >
          Clear Browser Storage
        </button>

        <button
          onClick={handleClearReactQuery}
          className="w-full bg-blue-500 text-white px-3 py-2 rounded hover:bg-blue-600"
        >
          Clear React Query Cache
        </button>

        <button
          onClick={handleClearIndexedDB}
          disabled={isLoading}
          className="w-full bg-purple-500 text-white px-3 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          Clear IndexedDB
        </button>

        <button
          onClick={handleResetStores}
          className="w-full bg-green-500 text-white px-3 py-2 rounded hover:bg-green-600"
        >
          Reset Zustand Stores
        </button>

        <button
          onClick={handleClearSpecific}
          className="w-full bg-gray-500 text-white px-3 py-2 rounded hover:bg-gray-600"
        >
          Clear Specific Queries
        </button>
      </div>

      <div className="border-t pt-4">
        <button
          onClick={updateStatus}
          className="w-full bg-gray-200 text-gray-800 px-3 py-2 rounded hover:bg-gray-300 mb-2"
        >
          Refresh Cache Status
        </button>

        {status && (
          <div className="text-xs bg-gray-100 p-2 rounded">
            <pre>{JSON.stringify(status, null, 2)}</pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default CacheDebugPanel;
