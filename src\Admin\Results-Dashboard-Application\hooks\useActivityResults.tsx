import { useQuery } from "@tanstack/react-query";
import useAxios from "../../../hooks/axiosObject";
import { AxiosInstance } from "axios";

type ActivityResult = {
  studentId: string;
  studentName: string;
  cohort: string;
  course: string;
  activityType: string;
  activityNumber: number;
  score: number;
  maxScore: number;
  timeSpent: number;
  completedAt: string;
  organization?: string;
  parameters?: ParameterScore[];
};

type ParameterScore = {
  parameterId: string;
  parameterName: string;
  score: number;
  maxScore: number;
};

const getActivityResults = async (axiosObject: AxiosInstance) => {
  const response = await axiosObject.get<ActivityResult[]>(
    "api/v1/activity-progress/get/activity-results",
  );
  return response.data;
};

function useActivityResults() {
  const axiosObject = useAxios();

  return useQuery<ActivityResult[]>({
    queryKey: ["activityResults"],
    refetchOnWindowFocus: false,
    queryFn: () => getActivityResults(axiosObject),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export default useActivityResults;
