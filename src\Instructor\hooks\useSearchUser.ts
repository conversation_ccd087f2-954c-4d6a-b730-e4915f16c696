import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { UserSearchResponse } from "../../types/User";
import { AxiosInstance } from "axios";

// api call

const getUsers = async (
  axiosObject: AxiosInstance,
  query: string,
): Promise<UserSearchResponse[]> => {
  const data = {

  }
  const response = await axiosObject.get<UserSearchResponse[]>(
    "/api/v1/user/search", {
    params: { query },
  },
  );
  console.log(response);
  return response.data;
};

export function useSearchUsers(query: string) {
  const axiosObject = useAxios();
  return useQuery<UserSearchResponse[], Error>({
    queryKey: ["searchUsers", query],
    queryFn: () => getUsers(axiosObject, query),
    enabled: !!query, // Only run the query if there's a search query
    staleTime: 5 * 60 * 1000, // 5 minutes stale time (adjust as needed)
  }
  );
}
