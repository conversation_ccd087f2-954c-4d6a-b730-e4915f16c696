import { useQuery } from "@tanstack/react-query";
import useAxios from "../../../hooks/axiosObject";
import { AxiosInstance } from "axios";

type ActivitySummaryResponse = {
  summary: ActivitySummary[];
};

type ActivitySummary = {
  activityType: string;
  activityNumber: number;
  course: string;
  totalAttempts: number;
  averageScore: number;
  averageTimeSpent: number;
  completionRate: number;
};

const getActivitySummaries = async (axiosObject: AxiosInstance) => {
  const response = await axiosObject.get<ActivitySummaryResponse>(
    "api/v1/activity-progress/get/activity-summary",
  );
  return response.data.summary;
};

function useActivitySummaries() {
  const axiosObject = useAxios();

  return useQuery<ActivitySummary[]>({
    queryKey: ["activitySummaries"],
    refetchOnWindowFocus: false,
    queryFn: () => getActivitySummaries(axiosObject),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export default useActivitySummaries;
