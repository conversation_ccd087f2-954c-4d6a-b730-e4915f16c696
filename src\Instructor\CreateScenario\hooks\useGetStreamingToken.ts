import { useQuery } from "@tanstack/react-query";
import useAxios from "../../../hooks/axiosObject";

type StreamingTokenResponse = {
  success: boolean;
  message: string;
  data: {
    token: string;
  };
};

export const useGetStreamingToken = () => {
  const axiosInstance = useAxios();

  return useQuery({
    queryKey: ["streaming-token"],
    queryFn: async () => {
      const response = await axiosInstance.post<StreamingTokenResponse>(
        "/api/v1/heygen/streaming-token",
      );

      return response.data;
    },
    refetchOnWindowFocus: false,
    select: (data) => ({
      token: data.data.token,
    }),
  });
};
