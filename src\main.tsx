import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import { AuthProvider } from "./Learner/context/AuthProvider.tsx";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { CacheManager } from "./utils/cacheManager.ts";

const customRetry = (failureCount: number, error: any) => {
  if (
    error.response &&
    error.response.status === 401 &&
    error.response.data.message ===
      "invalid credentials, recheck password and email."
  ) {
    return false; // Do not retry on this specific error
  }
  return failureCount < 3; // Retry up to 3 times for other errors
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: customRetry,
    },
  },
});

// Set up cache manager with query client
CacheManager.setQueryClient(queryClient);

// Clear all cache except auth tokens on app initialization
CacheManager.initializeCacheClear();

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {/* <ReactQueryDevtools initialIsOpen={false} /> */}
        <App />
      </AuthProvider>
    </QueryClientProvider>
  </React.StrictMode>,
);
