interface Message {
  message: string;
  sender: string;
  created_at: Date;
}

interface MessageSend {
  conversation_id: string | null;
  course_id: string | null;
  activity_id: string | null;
  message: string;
}

interface Conversation {
  id: number;
  chatbot_response: Message;
}

interface MessageData {
  data: Conversation;
}

const messages: Message[] = [
  {
    message: "Hello! I am <PERSON>, your Digital Dignity Coach. What questions about Dignity do you have for me today?",
    sender: "user",
    created_at: new Date(),
  },
];

export { messages };
export type { Message, MessageSend, MessageData, Conversation };
