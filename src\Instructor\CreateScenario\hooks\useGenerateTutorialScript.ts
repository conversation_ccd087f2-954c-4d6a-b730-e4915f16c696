import { useMutation } from "@tanstack/react-query";
import useAxios from "../../../hooks/axiosObject";

type TutorialScriptInput = {
  learning_objectives: string;
  learner_profile: string;
};

type TutorialScriptResponse = {
  success: boolean;
  message: string;
  data: {
    tutorial_script: string;
  };
};

/**
 * Hook to generate a tutorial script using the API
 */
export const useGenerateTutorialScript = () => {
  const axiosInstance = useAxios();

  return useMutation({
    mutationFn: async (input: TutorialScriptInput) => {
      const response = await axiosInstance.post<TutorialScriptResponse>(
        "/api/v1/simulation/generate-tutorial",
        input,
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      return response.data;
    },
  });
};
