import React from "react";

interface UserUploadIconProps {
  selected?: boolean;
}

function UserUploadIcon({ selected = false }: UserUploadIconProps) {
  return (
    <svg
      width="16"
      height="15"
      viewBox="0 0 16 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.94167 7.5C4.04167 7.5279 3.30556 7.88504 2.73333 8.57143H1.61667C1.16111 8.57143 0.777778 8.45843 0.466667 8.23242C0.155556 8.00642 0 7.67578 0 7.24051C0 5.27065 0.344444 4.28571 1.03333 4.28571C1.06667 4.28571 1.1875 4.34431 1.39583 4.4615C1.60417 4.57868 1.875 4.69727 2.20833 4.81724C2.54167 4.93722 2.87222 4.99721 3.2 4.99721C3.57222 4.99721 3.94167 4.93304 4.30833 4.80469C4.28056 5.01116 4.26667 5.19531 4.26667 5.35714C4.26667 6.13281 4.49167 6.8471 4.94167 7.5ZM13.8667 12.832C13.8667 13.5017 13.6639 14.0304 13.2583 14.4182C12.8528 14.8061 12.3139 15 11.6417 15H4.35833C3.68611 15 3.14722 14.8061 2.74167 14.4182C2.33611 14.0304 2.13333 13.5017 2.13333 12.832C2.13333 12.5363 2.14306 12.2475 2.1625 11.9657C2.18194 11.6839 2.22083 11.3797 2.27917 11.0533C2.3375 10.7268 2.41111 10.4241 2.5 10.1451C2.58889 9.86607 2.70833 9.59403 2.85833 9.32896C3.00833 9.0639 3.18056 8.83789 3.375 8.65095C3.56944 8.46401 3.80694 8.31473 4.0875 8.20312C4.36806 8.09152 4.67778 8.03571 5.01667 8.03571C5.07222 8.03571 5.19167 8.0957 5.375 8.21568C5.55833 8.33566 5.76111 8.46959 5.98333 8.61747C6.20556 8.76535 6.50278 8.89927 6.875 9.01925C7.24722 9.13923 7.62222 9.19922 8 9.19922C8.37778 9.19922 8.75278 9.13923 9.125 9.01925C9.49722 8.89927 9.79445 8.76535 10.0167 8.61747C10.2389 8.46959 10.4417 8.33566 10.625 8.21568C10.8083 8.0957 10.9278 8.03571 10.9833 8.03571C11.3222 8.03571 11.6319 8.09152 11.9125 8.20312C12.1931 8.31473 12.4306 8.46401 12.625 8.65095C12.8194 8.83789 12.9917 9.0639 13.1417 9.32896C13.2917 9.59403 13.4111 9.86607 13.5 10.1451C13.5889 10.4241 13.6625 10.7268 13.7208 11.0533C13.7792 11.3797 13.8181 11.6839 13.8375 11.9657C13.8569 12.2475 13.8667 12.5363 13.8667 12.832ZM5.33333 2.14286C5.33333 2.73438 5.125 3.2394 4.70833 3.65792C4.29167 4.07645 3.78889 4.28571 3.2 4.28571C2.61111 4.28571 2.10833 4.07645 1.69167 3.65792C1.275 3.2394 1.06667 2.73438 1.06667 2.14286C1.06667 1.55134 1.275 1.04632 1.69167 0.62779C2.10833 0.209263 2.61111 0 3.2 0C3.78889 0 4.29167 0.209263 4.70833 0.62779C5.125 1.04632 5.33333 1.55134 5.33333 2.14286ZM11.2 5.35714C11.2 6.24442 10.8875 7.00195 10.2625 7.62974C9.6375 8.25753 8.88333 8.57143 8 8.57143C7.11667 8.57143 6.3625 8.25753 5.7375 7.62974C5.1125 7.00195 4.8 6.24442 4.8 5.35714C4.8 4.46987 5.1125 3.71233 5.7375 3.08454C6.3625 2.45675 7.11667 2.14286 8 2.14286C8.88333 2.14286 9.6375 2.45675 10.2625 3.08454C10.8875 3.71233 11.2 4.46987 11.2 5.35714ZM16 7.24051C16 7.67578 15.8444 8.00642 15.5333 8.23242C15.2222 8.45843 14.8389 8.57143 14.3833 8.57143H13.2667C12.6944 7.88504 11.9583 7.5279 11.0583 7.5C11.5083 6.8471 11.7333 6.13281 11.7333 5.35714C11.7333 5.19531 11.7194 5.01116 11.6917 4.80469C12.0583 4.93304 12.4278 4.99721 12.8 4.99721C13.1278 4.99721 13.4583 4.93722 13.7917 4.81724C14.125 4.69727 14.3958 4.57868 14.6042 4.4615C14.8125 4.34431 14.9333 4.28571 14.9667 4.28571C15.6556 4.28571 16 5.27065 16 7.24051ZM14.9333 2.14286C14.9333 2.73438 14.725 3.2394 14.3083 3.65792C13.8917 4.07645 13.3889 4.28571 12.8 4.28571C12.2111 4.28571 11.7083 4.07645 11.2917 3.65792C10.875 3.2394 10.6667 2.73438 10.6667 2.14286C10.6667 1.55134 10.875 1.04632 11.2917 0.62779C11.7083 0.209263 12.2111 0 12.8 0C13.3889 0 13.8917 0.209263 14.3083 0.62779C14.725 1.04632 14.9333 1.55134 14.9333 2.14286Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default UserUploadIcon;
