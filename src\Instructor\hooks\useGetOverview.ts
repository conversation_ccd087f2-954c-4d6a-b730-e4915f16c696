import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { OverviewResponse } from "../../types/Analytics";
import { AxiosInstance } from "axios";

// api call

const getAnalytics = async (
  axiosObject: AxiosInstance,
): Promise<OverviewResponse> => {
  const data = await axiosObject.get<OverviewResponse>(
    "/api/v1/analytics/instructor/overview",
  );
  console.log(data);
  return data.data;
};

export function useGetOverviewAnalytics() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<OverviewResponse>({
    queryKey: ["overview"],
    refetchOnWindowFocus: false,
    queryFn: () => getAnalytics(axiosObject),
  });
}
