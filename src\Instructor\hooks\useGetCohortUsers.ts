import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { AxiosInstance } from "axios";
import { UserPaginated } from "../../types/User";

const getCohortUsers = async (
  axiosObject: AxiosInstance,
  cohort_id: string,
  page: number,
  limit: number,
): Promise<UserPaginated> => {
  const users = await axiosObject.get<UserPaginated>(
    `/api/v1/user/get-by-cohort/${cohort_id}?page=${page}&size=${limit}`,
  );
  return users.data;
};

export function useGetCohortUsers(cohort_id: string, page: number, limit: number) {
  const axiosObject = useAxios();
  return useQuery<UserPaginated>({
    queryKey: ["users", cohort_id, page, limit],
    queryFn: () => getCohortUsers(axiosObject, cohort_id, page, limit),
    // keepPreviousData: true, // Keeps old data during fetching
    enabled: !!cohort_id,
  });
}
