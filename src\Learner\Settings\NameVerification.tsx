import React from "react";
import GradientButton from "../components/GradientButton";

interface NameVerificationProps {
  name: string;
}
const NameVerification: React.FC<NameVerificationProps> = ({ name }) => {
  return (
    <div className="rounded-lg bg-white shadow-sm">
      <div className="rounded-t-lg bg-yellow-50 p-6">
        <h2 className="mb-4 text-lg font-bold text-blue-800">
          Name Verification
        </h2>
      </div>
      <div className="bg-white p-6">
        <p className="mb-4 text-gray-700">
          Your name <span className="font-semibold text-gray-900">{name}</span>{" "}
          is verified. This is the name that will appear on your certificates.
        </p>
      </div>
      <div className="rounded-b-lg bg-gray-50 p-6">
        <GradientButton
          text="Request Name Change"
          color1="#2B3D59"
          color2="#375685"
          width="250px"
        />
      </div>
    </div>
  );
};

export default NameVerification;
