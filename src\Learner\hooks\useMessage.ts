import { useMutation } from '@tanstack/react-query';
import useAxios from '../../hooks/axiosObject';
import { Conversation, MessageData } from '../../types/Message';
import { AxiosInstance } from 'axios';


// api call
export async function postMessage(axiosObject: AxiosInstance, conversation_id: string | null, course_id: string | null,
  activity_id: string | null, message: string | null): Promise<Conversation> {
  const data = {
    conversation_id: conversation_id,
    course_id: course_id,
    activity_id: activity_id,
    message: message,
  }
  const response = await axiosObject.post<MessageData>('/api/v1/ai-conversation/message', data);
  console.log(response)
  return response.data.data;
};

export const usePostMessage = () => {
  const axiosInstance = useAxios();
  return useMutation({
    mutationFn: ({ conversationId, courseId, activityId, message }: {
      conversationId: string | null; courseId: string | null; activityId: string | null, message: string,
    }) => {
      return postMessage(axiosInstance, conversationId, courseId, activityId, message );
    },
  })
};