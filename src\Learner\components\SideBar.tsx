import { useState, useEffect } from "react";
import SideBarItem from "./SideBarItem";
import { useGetCourseProgress } from "../hooks/useGetCourseProgress";
import { useAuth } from "../context/AuthProvider";
import { useCourse } from "../context/CourseContext";
import { useNavigate } from "react-router-dom";
import { Activity } from "../../types/CourseProgress";
import Swal from "sweetalert2";
import Spinner from "./Spinner";
import MobileMenu from "./MobileMenu";

interface Props {
  selectedItem?: number;
  isMobileMenuOpen?: boolean;
  onMobileMenuClose?: () => void;
}

function splitAndCapitalize(str: string) {
  return str
    .split("_") // Split the string by underscores
    .map(
      (word: string) =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
    ) // Capitalize each word
    .join(" "); // Join the words back into a single string with spaces
}

function getLink(courseId: string, activity: Activity) {
  return `/my-learning/${activity.type}?course_id=${courseId}&activity_id=${activity.id}`;
}

const handleLogout = (logout: () => void) => {
  Swal.fire({
    icon: "question",
    title: "Logging Out ...",
    text: "Are you sure?",
    showCancelButton: true,
    confirmButtonText: "Yes",
    cancelButtonText: "No",
  }).then((result) => {
    if (result.isConfirmed) {
      logout();
    }
  });
};

function SideBar({ selectedItem, isMobileMenuOpen, onMobileMenuClose }: Props) {
  const [selectedIndex, setSelectedIndex] = useState<any>(
    selectedItem === null ? -1 : selectedItem!,
  );
  const { data: courses_progress, isFetching } = useGetCourseProgress();
  const { selectedCourseId, setSelectedCourseId } = useCourse();
  const { logout } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const course_list =
      courses_progress?.map((c, index) => `Course ${index + 1}`) || [];
    const pageList = [
      "home-screen",
      ...course_list,
      "completed-courses",
      "course-catalogue",
      "forum",
      "settings",
      "help-center",
    ];
    pageList.forEach((e) => {
      if (e && window.location.href.includes(e)) {
        setSelectedIndex(e);
      }
    });
  }, []);

  // Update selectedIndex when selectedCourseId changes
  useEffect(() => {
    if (selectedCourseId) {
      setSelectedIndex(`course_id=${selectedCourseId}`);
    }
  }, [selectedCourseId]);

  return (
    <>
      <Spinner loading={isFetching} />
      {/* Mobile Menu */}
      <MobileMenu
        isOpen={!!isMobileMenuOpen}
        onClose={onMobileMenuClose || (() => {})}
        selectedIndex={selectedIndex}
        setSelectedIndex={setSelectedIndex}
      />

      {/* Desktop Sidebar - hidden on mobile */}
      <div className="learner-sidebar hidden w-3/12 flex-col justify-start md:flex">
        <ul className="divide-y-2 divide-[#E6DDB3] px-4">
          <SideBarItem
            key={1}
            sideBarItem={{
              heading: "Home",
              iconPath: "/home-icon.png",
              children: [],
              index: "home-screen",
              selectedIndex: selectedIndex,
              onClickHandle: () => {
                setSelectedIndex("home-screen");
                navigate("/home-screen");
              },
            }}
          />
          {courses_progress?.map((cp, index) => (
            <SideBarItem
              key={`course_id=${cp.course_id}`}
              sideBarItem={{
                heading: `Course ${index + 1}`,
                iconPath: "/home-icon.png",
                children: cp.course.activities.map((a) => {
                  return {
                    name: splitAndCapitalize(a.name),
                    link: getLink(String(cp.course_id), a),
                    course_name: cp.course.name,
                    completed: a.completed,
                  };
                }),
                index: `course_id=${cp.course_id}`,
                selectedIndex: selectedIndex,
                onClickHandle: () => {
                  setSelectedIndex(`course_id=${cp.course_id}`);
                  setSelectedCourseId(cp.course_id);
                },
              }}
            />
          ))}
          <SideBarItem
            key={3}
            sideBarItem={{
              heading: "Completed Courses",
              iconPath: "/completed-courses-icon.png",
              children: [],
              index: "completed-courses",
              selectedIndex: selectedIndex,
              onClickHandle: () => {
                setSelectedIndex("completed-courses");
                navigate("/my-learning/completed-courses");
              },
            }}
          />
          {/* <SideBarItem
          key={4}
          sideBarItem={{
            heading: "Course Catalogue",
            iconPath:
              "/course-catalogue-icon.png",
            children: [],
            index: "course-catalogue",
            selectedIndex: selectedIndex,
            onClickHandle: () => {
              setSelectedIndex("course-catalogue");
              navigate('/my-learning/course-catalogue')
            },
          }}
        />
        <SideBarItem
          key={5}
          sideBarItem={{
            heading: "Forum",
            iconPath:
              "/forum-icon.png",
            children: [],
            index: "forum",
            selectedIndex: selectedIndex,
            onClickHandle: () => {
              setSelectedIndex("forum");
              navigate('/my-learning/forum')
            },
          }}
        /> */}
          <SideBarItem
            key={6}
            sideBarItem={{
              heading: "Settings",
              iconPath: "/settings-icon.png",
              children: [],
              index: "settings",
              selectedIndex: selectedIndex,
              onClickHandle: () => {
                setSelectedIndex("settings");
                navigate("/my-learning/settings");
              },
            }}
          />
          <SideBarItem
            key={7}
            sideBarItem={{
              heading: "Help Center",
              iconPath: "/help-center-icon.png",
              children: [],
              index: "help-center",
              selectedIndex: selectedIndex,
              onClickHandle: () => {
                setSelectedIndex("help-center");
                navigate("/my-learning/help-center");
              },
            }}
          />
          <SideBarItem
            key={8}
            sideBarItem={{
              heading: "Logout",
              iconPath: "/logout-icon.png",
              children: [],
              index: 8,
              selectedIndex: selectedIndex,
              onClickHandle: () => {
                handleLogout(logout);
              },
            }}
          />
        </ul>
      </div>
    </>
  );
}

export default SideBar;
