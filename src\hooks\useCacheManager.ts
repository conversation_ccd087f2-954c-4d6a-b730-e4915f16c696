import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { CacheManager } from '../utils/cacheManager';

/**
 * Hook for manual cache management
 * Provides functions to clear different types of cache
 */
export const useCacheManager = () => {
  const queryClient = useQueryClient();

  // Ensure cache manager has the current query client
  CacheManager.setQueryClient(queryClient);

  const clearAllCache = useCallback(async () => {
    await CacheManager.clearAllCacheExceptAuth();
  }, []);

  const clearBrowserStorage = useCallback(() => {
    CacheManager.clearBrowserStorage();
  }, []);

  const clearReactQueryCache = useCallback(() => {
    CacheManager.clearReactQueryCache();
  }, []);

  const clearSpecificQueries = useCallback((queryKeys: string[]) => {
    CacheManager.clearSpecificQueries(queryKeys);
  }, []);

  const resetZustandStores = useCallback(() => {
    CacheManager.resetZustandStores();
  }, []);

  const getCacheStatus = useCallback(() => {
    return CacheManager.getCacheStatus();
  }, []);

  const clearIndexedDB = useCallback(async () => {
    await CacheManager.clearIndexedDB();
  }, []);

  return {
    clearAllCache,
    clearBrowserStorage,
    clearReactQueryCache,
    clearSpecificQueries,
    resetZustandStores,
    getCacheStatus,
    clearIndexedDB,
  };
};

export default useCacheManager;
