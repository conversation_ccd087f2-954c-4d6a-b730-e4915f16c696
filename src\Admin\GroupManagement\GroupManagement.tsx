import React, { useState } from "react";
import FileUpload from "./FileUpload";
// import UserTable from "./UserTable";
import UserTable from "./PaginatedUsersTable";
import { useCreateCohort } from "../hooks/useCreateCohort";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import showError from "../../Learner/scripts/showErrorDialog";
import Spinner from "../../Learner/components/Spinner";

function GroupManagement() {
  const [cohort, setCohort] = useState<number | null>(null);
  const mutation = useCreateCohort();
  const handleFileUpload = (uploadedFile: File) => {
    console.log("File uploaded:", uploadedFile);
    mutation.mutate(
      {
        cohortFile: uploadedFile,
      },
      {
        onSuccess: (data) => {
          if (data) {
            if (data.data.cohort_id) {
              setCohort(data.data.cohort_id);
            }
          }
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  return (
    <>
      <Spinner loading={mutation.isPending} />
      <div className="flex flex-col gap-20">
        <FileUpload handleFileUpload={handleFileUpload} />
        {cohort? <UserTable cohortId={cohort} />: null}
      </div>
    </>
  );
}

export default GroupManagement;
