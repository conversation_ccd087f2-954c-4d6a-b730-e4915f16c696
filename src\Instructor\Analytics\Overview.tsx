import { MenuItem, Select, SelectChangeEvent } from "@mui/material";
import { Bar<PERSON><PERSON> } from "@mui/x-charts/BarChart";
import React, { useState } from "react";
import MetricCard from "../../Instructor/CourseDashboard/MetricCard";
import { useGetOverviewAnalytics } from "../../Instructor/hooks/useGetOverview";
import Spinner from "../../Learner/components/Spinner";
import { useGetCourseProgress } from "../../Admin/hooks/useGetCourseProgress";

const Overview: React.FC = () => {
  const { data: overview, isFetching } = useGetOverviewAnalytics();
  const { data: courseProgressData, isFetching: isFetchingCourses } =
    useGetCourseProgress();
  const [selectedCourse, setSelectedCourse] = useState<string>("1");
  const handleCourseChange = (event: SelectChangeEvent) => {
    setSelectedCourse(event.target.value);
  };

  return (
    <div className="p-4">
      <Spinner loading={isFetching} />
      <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-4">
        <MetricCard
          value={overview ? overview.data.total_courses : 0}
          label="Total Courses"
          icon={<i className="fas fa-book"></i>} // Adjust the icon as needed
          bgColor="bg-yellow-100"
        />
        <MetricCard
          value={overview ? overview.data.total_students : 0}
          label="Students"
          icon={<i className="fas fa-user-graduate"></i>} // Adjust the icon as needed
          bgColor="bg-green-100"
        />
        <MetricCard
          value={overview ? overview.data.total_students_completed_courses : 0}
          label="Completed"
          icon={<i className="fas fa-check-circle"></i>} // Adjust the icon as needed
          bgColor="bg-red-100"
        />
        <MetricCard
          value={overview ? overview.data.total_students_remaining_courses : 0}
          label="Remaining"
          icon={<i className="fas fa-clock"></i>} // Adjust the icon as needed
          bgColor="bg-blue-100"
        />
      </div>
      <div className="mb-4">
        <label htmlFor="course-select" className="mr-2">
          Select Course:
        </label>
        <Select
          value={selectedCourse}
          onChange={handleCourseChange}
          className="h-[36px] min-w-[150px] bg-[#DAE3F2] font-semibold text-[#36537F]"
        >
          {courseProgressData?.map((course) => (
            <MenuItem key={course.id} value={course.id.toString()}>
              {course.name}
            </MenuItem>
          ))}
        </Select>
      </div>
      <div>
        {overview ? (
          <BarChart
            yAxis={[
              { scaleType: "band", data: ["Total", "Completed", "Remaining"] },
            ]}
            series={[
              {
                data: [
                  overview.data.total_students,
                  overview.data.total_students_completed_courses,
                  overview.data.total_students_remaining_courses,
                ],
              },
            ]}
            width={1000}
            height={300}
            sx={{ margin: "10px 0" }}
            layout="horizontal"
          />
        ) : null}
      </div>
    </div>
  );
};

export default Overview;
