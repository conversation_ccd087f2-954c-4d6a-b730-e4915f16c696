import React from "react";

function SecurityManagementIcon() {
  return (
    <svg
      width="34"
      height="34"
      viewBox="0 0 34 34"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.983 0C7.599 0 0 7.616 0 17C0 26.384 7.599 34 16.983 34C26.384 34 34 26.384 34 17C34 7.616 26.384 0 16.983 0Z"
        fill="url(#paint0_linear_0_1)"
      />
      <path
        d="M17 24.2785C18.4146 23.8481 19.5958 22.9982 20.5437 21.7289C21.4917 20.4596 22.0458 19.0428 22.2062 17.4785H17V10.7L11.75 12.6367V17.0911C11.75 17.1916 11.7646 17.3207 11.7937 17.4785H17V24.2785ZM17 26C16.8979 26 16.8031 25.9928 16.7156 25.9785C16.6281 25.9641 16.5406 25.9426 16.4531 25.9139C14.4844 25.2684 12.9167 24.0742 11.75 22.3314C10.5833 20.5887 10 18.7128 10 16.7038V12.6367C10 12.2781 10.1059 11.9553 10.3176 11.6684C10.5294 11.3814 10.8027 11.1734 11.1375 11.0443L16.3875 9.10759C16.5917 9.03586 16.7958 9 17 9C17.2042 9 17.4083 9.03586 17.6125 9.10759L22.8625 11.0443C23.1979 11.1734 23.4715 11.3814 23.6832 11.6684C23.895 11.9553 24.0006 12.2781 24 12.6367V16.7038C24 18.7122 23.4167 20.5881 22.25 22.3314C21.0833 24.0748 19.5156 25.2689 17.5469 25.9139C17.4594 25.9426 17.3719 25.9641 17.2844 25.9785C17.1969 25.9928 17.1021 26 17 26Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_0_1"
          x1="-1.8999e-07"
          y1="17"
          x2="34"
          y2="17"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2B3D59" />
          <stop offset="1" stopColor="#375685" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default SecurityManagementIcon;
