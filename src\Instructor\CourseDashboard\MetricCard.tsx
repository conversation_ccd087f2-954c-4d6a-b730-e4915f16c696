import React from 'react';

interface MetricCardProps {
  value: number;
  label: string;
  icon: React.ReactNode;
  bgColor: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ value, label, icon, bgColor }) => {
  return (
    <div className={`flex items-center justify-between p-4 rounded-lg shadow-md ${bgColor}`}>
      <div>
        <div className="text-2xl font-bold">{value}</div>
        <div className="text-sm">{label}</div>
      </div>
      <div className="text-2xl">
        {icon}
      </div>
    </div>
  );
};

export default MetricCard;
