import React, { useState, useEffect } from "react";
import { useGenerateScenario } from "./hooks/useGenerateScenario";
import { useGenerateTutorialScript } from "./hooks/useGenerateTutorialScript";

type FormData = {
  avatarId: string;
  tutorialNature: string;
  targetAudience: string;
  learningOutcome: string;
  instructions?: string;
};

type TutorialFormData = {
  avatarId: string;
  targetAudience: string;
  learningOutcome: string;
  instructions?: string;
};

interface ScenarioSelectionProps {
  formData: FormData | TutorialFormData;
  activityType?: string; // Add activity type to determine which hook to use
  onBack: () => void;
  onNext: (selectedScenario: string) => void;
}

const ScenarioSelection: React.FC<ScenarioSelectionProps> = ({
  formData,
  activityType,
  onBack,
  onNext,
}) => {
  const [selectedScenario, setSelectedScenario] = useState<string>("");
  const [additionalInfo, setAdditionalInfo] = useState<string>("");
  const [scenarios, setScenarios] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  // Use the appropriate hook based on the activity type
  const isTutorial = activityType === "tutorial";

  // For regular scenario generation
  const scenarioQuery = useGenerateScenario({
    ...(formData as FormData),
    additionalInfo,
  });

  // For tutorial script generation
  const tutorialScriptMutation = useGenerateTutorialScript();

  // Effect to update scenarios when data changes
  useEffect(() => {
    if (scenarioQuery.data && !isTutorial) {
      setScenarios(scenarioQuery.data.scenarios);
    }
  }, [scenarioQuery.data, isTutorial]);

  // Handle regeneration of scenarios
  const handleRegenerate = React.useCallback(async () => {
    if (isTutorial) {
      // For tutorial activities, use the tutorial script generator
      setIsGenerating(true);
      try {
        const result = await tutorialScriptMutation.mutateAsync({
          learning_objectives: formData.targetAudience,
          learner_profile: formData.learningOutcome,
        });

        if (result.data && result.data.tutorial_script) {
          // Set the tutorial script as the only scenario
          setScenarios([result.data.tutorial_script]);
        }
      } catch (error) {
        console.error("Error generating tutorial script:", error);
      } finally {
        setIsGenerating(false);
      }
    } else {
      // For other activities, use the regular scenario generator
      scenarioQuery.refetch();
    }
  }, [
    isTutorial,
    formData.learningOutcome,
    formData.targetAudience,
    tutorialScriptMutation,
    scenarioQuery,
  ]);

  // Initial generation for tutorial scripts
  useEffect(() => {
    if (isTutorial && !scenarios.length && !isGenerating) {
      handleRegenerate();
    }
  }, [isTutorial, scenarios.length, isGenerating, handleRegenerate]);

  return (
    <div className="mx-auto max-w-4xl p-6">
      <h2 className="mb-6 text-2xl font-semibold">
        {activityType === "tutorial"
          ? "Review script before video is generated"
          : "Choose a Scenario"}
      </h2>

      {scenarioQuery.isLoading || isGenerating ? (
        <div className="flex h-64 items-center justify-center">
          <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="space-y-6">
          {scenarios.map((scenario, index) => (
            <div
              key={index}
              className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                selectedScenario === scenario
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-blue-300"
              }`}
              onClick={() => setSelectedScenario(scenario)}
            >
              <p className="text-gray-800">{scenario}</p>
            </div>
          ))}
        </div>
      )}

      <div className="mt-8 space-y-4">
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Additional Information for Regeneration
          </label>
          <textarea
            value={additionalInfo}
            onChange={(e) => setAdditionalInfo(e.target.value)}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={4}
            placeholder={
              activityType === "tutorial"
                ? "Provide any additional instructions for revising the script"
                : "Enter any additional information to generate new scenarios"
            }
          />
        </div>

        <button
          onClick={handleRegenerate}
          disabled={scenarioQuery.isLoading || isGenerating}
          className="w-full rounded-md border border-blue-600 px-4 py-2 text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {scenarioQuery.isLoading || isGenerating
            ? "Generating..."
            : activityType === "tutorial"
              ? "Regenerate Script"
              : "Regenerate Scenarios"}
        </button>
      </div>

      <div className="mt-8 flex justify-between">
        <button
          onClick={onBack}
          className="rounded-md border border-gray-300 px-6 py-2 text-gray-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Back
        </button>
        <button
          onClick={() => selectedScenario && onNext(selectedScenario)}
          disabled={!selectedScenario}
          className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default ScenarioSelection;
