import React from "react";

interface LogoutIconProps {
  selected?: boolean;
}

function LogoutIcon({ selected = false }: LogoutIconProps) {
  return (
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.89476 0C1.39224 0 0.910298 0.189642 0.554962 0.527208C0.199626 0.864773 0 1.32261 0 1.8V10.2C0 10.6774 0.199626 11.1352 0.554962 11.4728C0.910298 11.8104 1.39224 12 1.89476 12H5.68427C6.1868 12 6.66874 11.8104 7.02407 11.4728C7.37941 11.1352 7.57903 10.6774 7.57903 10.2V1.8C7.57903 1.32261 7.37941 0.864773 7.02407 0.527208C6.66874 0.189642 6.1868 0 5.68427 0H1.89476ZM8.39567 3.1758C8.51411 3.06332 8.67473 3.00013 8.84221 3.00013C9.00968 3.00013 9.1703 3.06332 9.28874 3.1758L11.8151 5.5758C11.9335 5.68832 12 5.8409 12 6C12 6.1591 11.9335 6.31168 11.8151 6.4242L9.28874 8.8242C9.16962 8.9335 9.01008 8.99397 8.84448 8.99261C8.67888 8.99124 8.52047 8.92814 8.40337 8.81689C8.28627 8.70565 8.21984 8.55516 8.2184 8.39784C8.21696 8.24052 8.28062 8.08896 8.39567 7.9758L9.8439 6.6H4.4211C4.2536 6.6 4.09295 6.53679 3.9745 6.42426C3.85606 6.31174 3.78952 6.15913 3.78952 6C3.78952 5.84087 3.85606 5.68826 3.9745 5.57574C4.09295 5.46321 4.2536 5.4 4.4211 5.4H9.8439L8.39567 4.0242C8.27727 3.91168 8.21075 3.7591 8.21075 3.6C8.21075 3.4409 8.27727 3.28832 8.39567 3.1758Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default LogoutIcon;
