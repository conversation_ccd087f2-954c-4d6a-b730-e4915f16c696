import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { PlungeResponse } from "../../types/Analytics";
import { AxiosInstance } from "axios";

// api call

const getAnalytics = async (
  axiosObject: AxiosInstance,
): Promise<PlungeResponse> => {
  const data = await axiosObject.get<PlungeResponse>(
    "/api/v1/analytics/instructor/plunge",
  );
  console.log(data);
  return data.data;
};

export function useGetPlungeAnalytics() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<PlungeResponse>({
    queryKey: ["plungeAnalytics"],
    refetchOnWindowFocus: false,
    queryFn: () => getAnalytics(axiosObject),
  });
}
