import React from "react";

interface RubricEvaluation {
  acknowledgementAndApology: {
    score: number;
    explanation: string;
  };
  transparencyAndExplanation: {
    score: number;
    explanation: string;
  };
  loyaltyRecognition: {
    score: number;
    explanation: string;
  };
  effortToRepairTrust: {
    score: number;
    explanation: string;
  };
  emotionalManagement: {
    score: number;
    explanation: string;
  };
  visionOfCare: {
    score: number;
    explanation: string;
  };
}

interface CustomerResponseData {
  rubricEvaluation: RubricEvaluation;
  totalScore: string;
  suggestionsForImprovement: string;
}

interface CustomerResponseComponentProps {
  responseData: CustomerResponseData;
}

const PandaResponseComponent: React.FC<CustomerResponseComponentProps> = ({
  responseData,
}) => {
  const renderRubricItem = (
    title: string,
    item: { score: number; explanation: string },
  ) => (
    <div className="mb-3">
      <div className="flex items-center justify-between">
        <strong>{title}:</strong>
        <span>{renderStars(item.score, 5)}</span>
      </div>
      <p className="mt-1 text-sm">{item.explanation}</p>
    </div>
  );

  const renderStars = (rating: number, maxRating: number) => {
    const fullStar = "★";
    const emptyStar = "☆";
    return (
      <>
        {[...Array(maxRating)].map((_, i) => (
          <span key={i} className="text-white">
            {i < rating ? fullStar : emptyStar}
          </span>
        ))}
      </>
    );
  };

  return (
    <div className="mx-2 rounded-[44px] bg-[#22409A] p-6 text-white shadow-lg">
      {/* Title */}
      <h2 className="mb-4 text-center text-xl font-semibold">Evaluation</h2>

      {/* Rubric Evaluation */}
      <div className="mb-4 text-left">
        <h3 className="mb-3 text-lg font-semibold">Evaluation Criteria</h3>
        {renderRubricItem(
          "Acknowledgement and Apology",
          responseData.rubricEvaluation.acknowledgementAndApology,
        )}
        {renderRubricItem(
          "Transparency and Explanation",
          responseData.rubricEvaluation.transparencyAndExplanation,
        )}
        {renderRubricItem(
          "Loyalty Recognition",
          responseData.rubricEvaluation.loyaltyRecognition,
        )}
        {renderRubricItem(
          "Effort to Repair Trust",
          responseData.rubricEvaluation.effortToRepairTrust,
        )}
        {renderRubricItem(
          "Emotional Management",
          responseData.rubricEvaluation.emotionalManagement,
        )}
        {renderRubricItem(
          "Vision of Care",
          responseData.rubricEvaluation.visionOfCare,
        )}
      </div>

      {/* Overall Score */}
      <div className="mb-4 flex items-center text-left text-lg">
        <strong>Total Score:</strong>
        <span className="ml-2">
          {responseData.totalScore.toString().replace("/30", "")}/30
        </span>
      </div>

      {/* Suggestions */}
      <div className="text-left">
        <h3 className="mb-2 text-lg font-semibold">
          Suggestions for Improvement
        </h3>
        <p className="text-sm">{responseData.suggestionsForImprovement}</p>
      </div>
    </div>
  );
};

export default PandaResponseComponent;
