import { useMutation } from '@tanstack/react-query';
import useAxios from '../../hooks/axiosObject';
import { changePasswordResponse } from '../../types/User';
import { AxiosInstance } from 'axios';


// api call
export async function changePassword(axiosObject: AxiosInstance, oldPassword: string, newPassword: string): Promise<changePasswordResponse> {
  const data = {
    old_password: oldPassword,
    new_password: newPassword,
  }
  const response = await axiosObject.patch<changePasswordResponse>('/api/v1/user/change_password', data);
  console.log(response)
  return response.data;
};

export const useChangePassword = () => {
  const axiosInstance = useAxios();
  return useMutation({
    mutationFn: ({ oldPassword, newPassword }: {
      oldPassword: string, newPassword: string
    }) => {
      return changePassword(axiosInstance, oldPassword, newPassword);
    },
  })
};