import { useLocation } from "react-router-dom";
import { useCourse } from "../context/CourseContext";

/**
 * Custom hook to get the course_id from either the URL query parameter or the selected course context
 * @returns The course_id as a string
 */
export function useCourseId(): string {
  const { selectedCourseId } = useCourse();
  const query = new URLSearchParams(useLocation().search);
  const urlCourseId = query.get("course_id") ?? "";
  
  // Prefer the URL parameter if it exists, otherwise use the selected course ID
  return urlCourseId || (selectedCourseId ? selectedCourseId.toString() : "");
}
