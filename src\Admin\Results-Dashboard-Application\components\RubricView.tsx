import { useState } from "react";
import { Card } from "./ui/card";
import { Badge } from "./ui/badge";
import { But<PERSON> } from "./ui/button";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  Legend,
  Cell,
} from "recharts";
import { generateParameterPerformanceData } from "../data/mockData";
import { ClipboardList, Download, TrendingUp, Users } from "lucide-react";
import useCohortSummaries from "../hooks/useCohortSummaries";
import useActivityRubrics from "../hooks/useActivityRubrics";
import useActivityResults from "../hooks/useActivityResults";

interface RubricViewProps {
  selectedCourse: string;
  onCourseChange: (course: string) => void;
  selectedTimePeriod: string;
}

export function RubricView({
  selectedCourse,
  onCourseChange,
  selectedTimePeriod,
}: RubricViewProps) {
  const [selectedActivity, setSelectedActivity] = useState("");
  const { data: mockCohortSummaries, isLoading } = useCohortSummaries();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const { data: mockActivityResults, isLoading: isResultsLoading } =
    useActivityResults();

  const { data: mockActivityRubrics, isLoading: isRubricLoading } =
    useActivityRubrics();

  if (isRubricLoading) {
    return <div>Loading...</div>;
  }

  if (isResultsLoading) {
    return <div>Loading...</div>;
  }

  // Get available courses
  const courses = [...new Set(mockCohortSummaries.map((c) => c.course))];

  // Get available activities for selected course
  const availableActivities = mockActivityRubrics
    .filter((r) => r.course === selectedCourse)
    .map(
      (r) =>
        `${r.activityType}${r.activityNumber > 1 ? ` ${r.activityNumber}` : ""}`,
    );

  // Auto-select first activity if none selected
  const currentActivity =
    selectedActivity && availableActivities.includes(selectedActivity)
      ? selectedActivity
      : availableActivities[0] || "";

  if (currentActivity !== selectedActivity) {
    setSelectedActivity(currentActivity);
  }

  // Parse activity type and number from activity string
  const parseActivity = (activityStr: string) => {
    if (activityStr.includes("AI Activity")) {
      const match = activityStr.match(/AI Activity\s?(\d+)?/);
      return {
        type: "AI Activity",
        number: match?.[1] ? parseInt(match[1]) : 1,
      };
    } else if (activityStr.includes("Quiz")) {
      const match = activityStr.match(/Quiz\s?(\d+)?/);
      return { type: "Quiz", number: match?.[1] ? parseInt(match[1]) : 1 };
    } else if (activityStr.includes("Vira Activity")) {
      return { type: "Vira Activity", number: 1 };
    }
    return { type: activityStr, number: 1 };
  };

  const { type: activityType, number: activityNumber } =
    parseActivity(currentActivity);

  // Get rubric for current activity
  const currentRubric = mockActivityRubrics.find(
    (r) =>
      r.course === selectedCourse &&
      r.activityType === activityType &&
      r.activityNumber === activityNumber,
  );

  // Get activity results for selected course and activity
  const activityResults = mockActivityResults.filter(
    (result) =>
      result.course === selectedCourse &&
      result.activityType === activityType &&
      result.activityNumber === activityNumber &&
      result.parameters &&
      result.parameters.length > 0,
  );

  // Get enhanced parameter performance data
  const parameterPerformanceData = generateParameterPerformanceData(
    selectedCourse,
    activityType,
    activityNumber,
    mockActivityRubrics,
    mockActivityResults,
  );

  // Generate data for vertical bar chart
  const getVerticalBarData = () => {
    return parameterPerformanceData.map((param) => ({
      parameter:
        param.parameterName.length > 20
          ? param.parameterName.substring(0, 17) + "..."
          : param.parameterName,
      fullName: param.parameterName,
      score: param.averageScore,
      studentCount: param.studentCount,
      level: param.performanceLevel,
    }));
  };

  // Generate trends over time
  const getTrendsData = () => {
    if (!currentRubric) return [];

    const weeks = ["Week 1", "Week 2", "Week 3", "Week 4", "Week 5", "Week 6"];

    return weeks.map((week, weekIndex) => {
      const dataPoint: any = { week };
      parameterPerformanceData.forEach((param, paramIndex) => {
        const baseScore = param.averageScore;
        const weekProgress = weekIndex * 2.5; // 2.5% improvement per week
        const randomVariation = (Math.random() - 0.5) * 4; // ±2% random variation
        const score = Math.min(
          95,
          Math.max(35, baseScore + weekProgress + randomVariation),
        );
        dataPoint[param.parameterName] = Math.round(score * 10) / 10;
      });
      return dataPoint;
    });
  };

  // Generate cohort comparison data
  const getCohortComparisonData = () => {
    if (!currentRubric) return [];

    const cohorts = [
      ...new Set(
        mockCohortSummaries
          .filter((c) => c.course === selectedCourse)
          .map((c) => c.cohort),
      ),
    ];

    return cohorts.map((cohort) => {
      const dataPoint: any = { cohort };

      parameterPerformanceData.forEach((param) => {
        const cohortData = param.cohortBreakdown.find(
          (cb) => cb.cohort === cohort,
        );
        if (cohortData) {
          dataPoint[param.parameterName] = cohortData.averageScore;
        } else {
          // Generate realistic fallback based on cohort overall performance
          const cohortSummary = mockCohortSummaries.find(
            (c) => c.cohort === cohort && c.course === selectedCourse,
          );
          const baseScore = cohortSummary ? cohortSummary.averageScore : 65;
          dataPoint[param.parameterName] =
            Math.round((baseScore + (Math.random() - 0.5) * 8) * 10) / 10;
        }
      });

      return dataPoint;
    });
  };

  // Generate results table data
  const getResultsTableData = () => {
    const tableData = activityResults.slice(0, 10).map((result) => {
      const row: any = { student: result.studentName, cohort: result.cohort };
      if (result.parameters) {
        result.parameters.forEach((param) => {
          row[param.parameterName] = `${param.score}/${param.maxScore}`;
        });
      }
      return row;
    });

    // If no real data, generate sample table data
    if (tableData.length === 0 && currentRubric) {
      const sampleStudents = [
        "Alice Johnson",
        "Bob Smith",
        "Charlie Brown",
        "Diana Lee",
        "Emma Wilson",
      ];
      const cohorts = mockCohortSummaries
        .filter((c) => c.course === selectedCourse)
        .map((c) => c.cohort);

      return sampleStudents.map((student, index) => {
        const row: any = {
          student,
          cohort: cohorts[index % cohorts.length] || "Cohort A",
        };
        currentRubric.parameters.forEach((param) => {
          const score = Math.round(
            param.maxScore * (0.6 + Math.random() * 0.35),
          );
          row[param.parameterName] = `${score}/${param.maxScore}`;
        });
        return row;
      });
    }

    return tableData;
  };

  const verticalBarData = getVerticalBarData();
  const trendsData = getTrendsData();
  const cohortComparisonData = getCohortComparisonData();
  const resultsTableData = getResultsTableData();

  const parameterColors = [
    "#3B82F6",
    "#10B981",
    "#F59E0B",
    "#EF4444",
    "#8B5CF6",
  ];

  const getPerformanceLevelColor = (level: string) => {
    switch (level) {
      case "excellent":
        return "#10B981";
      case "good":
        return "#3B82F6";
      case "satisfactory":
        return "#F59E0B";
      case "needs-improvement":
        return "#EF4444";
      default:
        return "#6B7280";
    }
  };

  return (
    <div className="space-y-6">
      {/* Course and Activity Selection */}
      <Card className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <ClipboardList className="text-primary h-5 w-5" />
            <h3>Select Course and Activity</h3>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="flex flex-col space-y-2">
            <label className="text-muted-foreground text-sm">Course</label>
            <select
              value={selectedCourse}
              onChange={(e) => onCourseChange(e.target.value)}
              className="bg-background rounded-md border px-4 py-2"
            >
              {courses.map((course) => (
                <option key={course} value={course}>
                  {course}
                </option>
              ))}
            </select>
          </div>
          <div className="flex flex-col space-y-2">
            <label className="text-muted-foreground text-sm">Activity</label>
            <select
              value={currentActivity}
              onChange={(e) => setSelectedActivity(e.target.value)}
              className="bg-background rounded-md border px-4 py-2"
              disabled={availableActivities.length === 0}
            >
              {availableActivities.length > 0 ? (
                availableActivities.map((activity) => (
                  <option key={activity} value={activity}>
                    {activity}
                  </option>
                ))
              ) : (
                <option value="">No activities available</option>
              )}
            </select>
          </div>
        </div>
      </Card>

      {/* Parameter Performance Chart */}
      <Card className="p-6">
        <h3 className="mb-4 flex items-center justify-between">
          Parameter Performance
          <Badge variant="outline" className="text-xs">
            {parameterPerformanceData.length} parameters
          </Badge>
        </h3>

        {parameterPerformanceData.length > 0 ? (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart
              data={verticalBarData}
              margin={{ top: 20, right: 30, left: 20, bottom: 80 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="parameter"
                angle={-45}
                textAnchor="end"
                height={80}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
                tick={{ fontSize: 12 }}
              />
              <Tooltip
                formatter={(value: any, name: any, props: any) => [
                  `${value}%`,
                  `Average Score (${props.payload?.studentCount || 0} students)`,
                ]}
                labelFormatter={(label: any, payload: any) => {
                  const data = payload?.[0]?.payload;
                  return data?.fullName || label;
                }}
                contentStyle={{
                  backgroundColor: "white",
                  border: "1px solid #ccc",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
              />
              <Bar dataKey="score" radius={[4, 4, 0, 0]} minPointSize={10}>
                {verticalBarData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={getPerformanceLevelColor(entry.level)}
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        ) : (
          <div className="text-muted-foreground flex h-[400px] items-center justify-center">
            <div className="text-center">
              <ClipboardList className="mx-auto mb-2 h-12 w-12 opacity-50" />
              <div>No parameter data available</div>
              <div className="mt-1 text-xs">
                Select a different course or activity
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Parameter Trends and Cohort Comparison */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Parameter Trends Over Time */}
        <Card className="p-6">
          <h3 className="mb-4">Parameter Trends Over Time</h3>
          {trendsData.length > 0 && currentRubric ? (
            <ResponsiveContainer width="100%" height={350}>
              <LineChart
                data={trendsData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis dataKey="week" />
                <YAxis
                  domain={[40, 100]}
                  tickFormatter={(value) => `${value}%`}
                />
                <Tooltip formatter={(value: any) => [`${value}%`, ""]} />
                <Legend />
                {parameterPerformanceData.slice(0, 4).map((param, index) => (
                  <Line
                    key={param.parameterId}
                    type="monotone"
                    dataKey={param.parameterName}
                    stroke={parameterColors[index % parameterColors.length]}
                    strokeWidth={3}
                    dot={{ r: 4, strokeWidth: 2 }}
                    activeDot={{ r: 6, strokeWidth: 2 }}
                  />
                ))}
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="text-muted-foreground flex h-[350px] items-center justify-center">
              <div className="text-center">
                <TrendingUp className="mx-auto mb-2 h-12 w-12 opacity-50" />
                <div>No trends data available</div>
              </div>
            </div>
          )}
        </Card>

        {/* Cohort Comparison */}
        <Card className="p-6">
          <h3 className="mb-4">Cohort Comparison by Parameters</h3>
          {cohortComparisonData.length > 0 && currentRubric ? (
            <ResponsiveContainer width="100%" height={350}>
              <BarChart
                data={cohortComparisonData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis dataKey="cohort" />
                <YAxis
                  domain={[30, 100]}
                  tickFormatter={(value) => `${value}%`}
                />
                <Tooltip formatter={(value: any) => [`${value}%`, ""]} />
                <Legend />
                {parameterPerformanceData.slice(0, 4).map((param, index) => (
                  <Bar
                    key={param.parameterId}
                    dataKey={param.parameterName}
                    fill={parameterColors[index % parameterColors.length]}
                    radius={[2, 2, 0, 0]}
                  />
                ))}
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="text-muted-foreground flex h-[350px] items-center justify-center">
              <div className="text-center">
                <Users className="mx-auto mb-2 h-12 w-12 opacity-50" />
                <div>No cohort comparison data available</div>
              </div>
            </div>
          )}
        </Card>
      </div>

      {/* Results Data Table */}
      <Card className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <h3>Detailed Results Data</h3>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
        </div>
        {resultsTableData.length > 0 && currentRubric ? (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="p-3 text-left font-medium">Student</th>
                  <th className="p-3 text-left font-medium">Cohort</th>
                  {currentRubric.parameters.map((param) => (
                    <th
                      key={param.parameterId}
                      className="p-3 text-left font-medium"
                    >
                      <div>{param.parameterName}</div>
                      <div className="text-muted-foreground text-xs font-normal">
                        (Max: {param.maxScore})
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {resultsTableData.map((row, index) => (
                  <tr key={index} className="hover:bg-secondary/20 border-b">
                    <td className="p-3 font-medium">{row.student}</td>
                    <td className="p-3">
                      <Badge variant="outline" className="text-xs">
                        {row.cohort}
                      </Badge>
                    </td>
                    {currentRubric.parameters.map((param) => (
                      <td key={param.parameterId} className="p-3">
                        <Badge variant="outline" className="font-mono text-xs">
                          {row[param.parameterName] || "N/A"}
                        </Badge>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-muted-foreground py-8 text-center">
            <div>No results data available for the selected activity</div>
            <div className="mt-2 text-xs">
              Data will appear when students complete assessments
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
