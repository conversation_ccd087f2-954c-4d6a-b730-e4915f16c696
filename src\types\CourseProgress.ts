interface Activity {
  id: number;
  name: string;
  type: string;
  sequence: number;
  completed?: boolean;
}

interface Course {
  id: number;
  name: string;
  activities: Activity[];
}

interface CourseProgress {
  course: Course;
  course_id: number;
  progress_percentage: number;
}

const courses_progress: CourseProgress[] = [];

export { courses_progress };
export type { CourseProgress, Course, Activity };
