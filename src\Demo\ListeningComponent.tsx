import React from "react";

interface FacialExpressions {
  calmAndApproachableExpression: string;
  engagedListening: string;
  noSignsOfFrustrationOrAnnoyance: string;
  supportiveGestures: string;
  openAndRelaxedFacialFeatures: string;
}

interface Feedback {
  positiveAreas: string;
  improvementSuggestions: string;
}

interface ListeningData {
  facialExpressions: FacialExpressions;
  overallScore: number;
  feedback: Feedback;
}

interface ListeningComponentProps {
  listeningData: ListeningData;
}

const ListeningComponent: React.FC<ListeningComponentProps> = ({
  listeningData,
}) => {
  const { facialExpressions, overallScore, feedback } = listeningData;

  const renderYesNo = (label: string, value: string) => (
    <li>
      <strong>{label}:</strong> {value}
    </li>
  );

  const renderStars = (rating: number) => {
    const fullStar = "★";
    const emptyStar = "☆";
    return (
      <>
        {[...Array(5)].map((_, i) => (
          <span key={i} className="text-white">
            {i < rating ? fullStar : emptyStar}
          </span>
        ))}
      </>
    );
  };

  return (
    <div className="bg-[#22409A] text-white p-6 rounded-[44px] mx-2 shadow-lg">
      {/* Title */}
      <h2 className="text-xl font-semibold text-center mb-4">Listening</h2>

      {/* Facial Expressions */}
      <div className="text-left mb-4">
        <h3 className="text-lg font-semibold mb-2">Facial Expressions</h3>
        <ul className="list-none space-y-1">
          {renderYesNo(
            "Calm and Approachable Expression",
            facialExpressions.calmAndApproachableExpression
          )}
          {renderYesNo(
            "Engaged Listening",
            facialExpressions.engagedListening
          )}
          {renderYesNo(
            "No Signs of Frustration or Annoyance",
            facialExpressions.noSignsOfFrustrationOrAnnoyance
          )}
          {renderYesNo(
            "Supportive Gestures",
            facialExpressions.supportiveGestures
          )}
          {renderYesNo(
            "Open and Relaxed Facial Features",
            facialExpressions.openAndRelaxedFacialFeatures
          )}
        </ul>
      </div>

      {/* Overall Score */}
      <div className="text-left text-lg mb-4">
        <strong>Overall Score:</strong> {renderStars(overallScore)}
      </div>

      {/* Feedback */}
      {feedback.positiveAreas && (
        <div className="text-left mb-4">
          <h3 className="text-lg font-semibold mb-2">You did well at:</h3>
          <p>{feedback.positiveAreas}</p>
        </div>
      )}

      {feedback.improvementSuggestions && (
        <div className="text-left">
          <h3 className="text-lg font-semibold mb-2">Suggestions for Improvement:</h3>
          <p>{feedback.improvementSuggestions}</p>
        </div>
      )}
    </div>
  );
};

export default ListeningComponent;
