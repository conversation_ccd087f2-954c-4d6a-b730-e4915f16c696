import React from "react";

interface RubricEvaluation {
  empathyAndApology: {
    score: number;
    explanation: string;
  };
  constructiveResponse: {
    score: number;
    explanation: string;
  };
  toneAndOwnership: {
    score: number;
    explanation: string;
  };
  opportunityCreation: {
    score: number;
    explanation: string;
  };
}

interface CustomerResponseData {
  rubricEvaluation: RubricEvaluation;
  totalScore: string;
  suggestionsForImprovement: string;
}

interface CustomerResponseComponentProps {
  responseData: CustomerResponseData;
}

const FoodResponseComponent: React.FC<CustomerResponseComponentProps> = ({
  responseData,
}) => {
  const renderRubricItem = (
    title: string,
    item: { score: number; explanation: string },
  ) => (
    <div className="mb-3">
      <div className="flex items-center justify-between">
        <strong>{title}:</strong>
        <span>{renderStars(item.score, 5)}</span>
      </div>
      <p className="mt-1 text-sm">{item.explanation}</p>
    </div>
  );

  const renderStars = (rating: number, maxRating: number) => {
    const fullStar = "★";
    const emptyStar = "☆";
    return (
      <>
        {[...Array(maxRating)].map((_, i) => (
          <span key={i} className="text-white">
            {i < rating ? fullStar : emptyStar}
          </span>
        ))}
      </>
    );
  };

  return (
    <div className="mx-2 rounded-[44px] bg-[#22409A] p-6 text-white shadow-lg">
      {/* Title */}
      <h2 className="mb-4 text-center text-xl font-semibold">Evaluation</h2>

      {/* Rubric Evaluation */}
      <div className="mb-4 text-left">
        <h3 className="mb-3 text-lg font-semibold">Evaluation Criteria</h3>
        {renderRubricItem(
          "Empathy and Apology",
          responseData.rubricEvaluation.empathyAndApology,
        )}
        {renderRubricItem(
          "Constructive Response",
          responseData.rubricEvaluation.constructiveResponse,
        )}
        {renderRubricItem(
          "Tone and Ownership",
          responseData.rubricEvaluation.toneAndOwnership,
        )}
        {renderRubricItem(
          "Opportunity Creation",
          responseData.rubricEvaluation.opportunityCreation,
        )}
      </div>

      {/* Overall Score */}
      <div className="mb-4 flex items-center text-left text-lg">
        <strong>Total Score:</strong>
        <span className="ml-2">
          {responseData.totalScore.toString().replace("/20", "")}/20
        </span>
      </div>

      {/* Suggestions */}
      <div className="text-left">
        <h3 className="mb-2 text-lg font-semibold">
          Suggestions for Improvement
        </h3>
        <p className="text-sm">{responseData.suggestionsForImprovement}</p>
      </div>
    </div>
  );
};

export default FoodResponseComponent;
