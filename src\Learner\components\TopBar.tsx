import { useState } from "react";
import "../components/ResponsiveStyles.css";

interface TopBarProps {
  onMenuToggle?: () => void;
}

function TopBar({ onMenuToggle }: TopBarProps) {
  const [showSearch, setShowSearch] = useState(false);

  return (
    <div className="learner-top-bar mb-6 mt-6 flex w-full items-center justify-between px-4 md:mb-12 md:mt-12 md:px-14">
      {/* Mobile menu button - only visible on small screens */}
      <button
        className="mr-2 text-gray-600 md:hidden"
        onClick={onMenuToggle}
        aria-label="Toggle menu"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 6h16M4 12h16M4 18h16"
          />
        </svg>
      </button>

      <div className="flex items-center">
        <img
          className="mr-2 h-14 w-28 md:mr-3 md:h-20 md:w-44"
          src="/GEM-LOGO.png"
          alt="GEM Logo"
        />
      </div>

      {/* Search bar - hidden on mobile by default */}
      <div className="hidden md:flex">
        <input
          type="text"
          className="h-12 w-full rounded-[4px] bg-[#F5F5F5] p-3 md:w-[800px]"
          placeholder="&#xF002; Search"
          onChange={() => {}}
        />
      </div>

      {/* Mobile search toggle */}
      <div className="flex items-center">
        <button
          className="mr-4 text-gray-600 md:hidden"
          onClick={() => setShowSearch(!showSearch)}
          aria-label="Toggle search"
        >
          <i className="fa fa-search text-xl"></i>
        </button>

        <a href="/my-learning/settings">
          <img
            className="h-8 w-8 rounded-full md:h-12 md:w-12"
            src="/user-icon.png"
            alt="User profile"
          />
        </a>
      </div>

      {/* Mobile search bar - shown when toggle is clicked */}
      {showSearch && (
        <div className="absolute left-0 right-0 top-20 z-10 px-4 md:hidden">
          <input
            type="text"
            className="h-12 w-full rounded-[4px] bg-[#F5F5F5] p-3 shadow-md"
            placeholder="&#xF002; Search"
            onChange={() => {}}
          />
        </div>
      )}
    </div>
  );
}

export default TopBar;
