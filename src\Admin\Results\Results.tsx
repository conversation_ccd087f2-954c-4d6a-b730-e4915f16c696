import {
  MenuItem,
  Select,
  SelectChangeEvent,
  TablePagination,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import Spinner from "../../Learner/components/Spinner";
import showError from "../../Learner/scripts/showErrorDialog";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import { useDownloadResults } from "../hooks/useDownloadResults";
import { useGetCourseProgress } from "../hooks/useGetCourseProgress";
import { useGetEnrolledUsers } from "../hooks/useGetEnrolledUsers";

const Results: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedCourse, setSelectedCourse] = useState<string>(
    location.state?.selectedCourse || "1",
  );
  const [page, setPage] = useState<number>(location.state?.page || 0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(
    location.state?.rowsPerPage || 10,
  );

  const { data: courseProgressData, isFetching: isFetchingCourses } =
    useGetCourseProgress();
  const { data: enrolledUsersData, isFetching: isFetchingEnrolledUsers } =
    useGetEnrolledUsers(selectedCourse, page + 1, rowsPerPage); // API is 1-based
  const downloadMutation = useDownloadResults();

  useEffect(() => {
    if (location.state) {
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  // Handle page change
  const handleChangePage = (
    _event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number,
  ) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0); // Reset to first page when changing rows per page
  };

  // Combine all loading states
  const isLoading =
    isFetchingCourses || isFetchingEnrolledUsers || downloadMutation.isPending;

  const handleCourseChange = (event: SelectChangeEvent) => {
    setSelectedCourse(event.target.value);
    setPage(0); // Reset to first page when changing course
  };

  const handleDownload = async () => {
    try {
      const blob = await downloadMutation.mutateAsync(selectedCourse);

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `organizational-results${selectedCourse !== "all" ? `-course-${selectedCourse}` : ""}.csv`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      showSuccess("Success", "Results downloaded successfully!");
    } catch (error) {
      console.error("Download failed:", error);
      showError(
        "Download Failed",
        "There was an error downloading the results. Please try again.",
      );
    }
  };

  const handleViewDetailedResults = (userId: string, courseId: string) => {
    navigate(`/admin/results/user/${userId}/course/${courseId}`, {
      state: {
        page,
        rowsPerPage,
        selectedCourse,
      },
    });
  };

  return (
    <>
      <Spinner loading={isLoading} />
      <div className="rounded-lg bg-white p-6 shadow">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center">
            <h2 className="mr-4 text-xl font-semibold">Results</h2>
            <div className="flex items-center">
              <span className="mr-2 text-gray-700">Select Course:</span>
              <Select
                value={selectedCourse}
                onChange={handleCourseChange}
                className="h-[36px] min-w-[150px] bg-[#DAE3F2] font-semibold text-[#36537F]"
              >
                {courseProgressData?.map((course) => (
                  <MenuItem key={course.id} value={course.id.toString()}>
                    {course.name}
                  </MenuItem>
                ))}
              </Select>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full bg-white">
            <thead className="bg-gray-100">
              <tr>
                <th className="px-4 py-2 text-left">Name</th>
                <th className="px-4 py-2 text-left">Id</th>
                <th className="px-4 py-2 text-left">Email</th>
                <th className="px-4 py-2 text-left">Course</th>
                <th className="px-4 py-2 text-left">View Detailed Results</th>
              </tr>
            </thead>
            <tbody>
              {enrolledUsersData?.items
                .filter((user) => user.role_id === 4) // Only show learners (role_id === 4)
                .map((user, index) => {
                  // Find the course progress data for this user if available
                  const courseProgress = courseProgressData?.find(
                    (cp) => cp.id.toString() === selectedCourse,
                  );

                  return (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="px-4 py-2">{`${user.name} ${user.last_name || ""}`}</td>
                      <td className="px-4 py-2">{user.id}</td>
                      <td className="px-4 py-2">{user.email}</td>
                      <td className="px-4 py-2">
                        {courseProgress?.name || "Unknown Course"}
                      </td>
                      <td className="px-4 py-2">
                        <button
                          onClick={() =>
                            handleViewDetailedResults(
                              user.id?.toString() || "",
                              selectedCourse,
                            )
                          }
                          className="rounded bg-[#36537F] px-2 py-1 text-white hover:bg-blue-700"
                        >
                          <svg
                            width="24"
                            height="18"
                            viewBox="0 0 32 23"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <rect
                              width="32"
                              height="23"
                              rx="2"
                              fill="url(#paint0_linear_143_1569)"
                            />
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M20.1979 6C19.9612 6 19.7269 6.04273 19.5082 6.12575C19.2896 6.20876 19.0909 6.33044 18.9236 6.48384L15.3068 9.79918C15.0319 10.051 14.844 10.372 14.7675 10.7214L14.4333 12.2549C14.392 12.4447 14.4568 12.6409 14.6062 12.7778C14.7555 12.9146 14.9696 12.974 15.1766 12.936L16.849 12.6292C17.23 12.5593 17.5799 12.3879 17.8551 12.1356L21.4722 8.82001C21.6395 8.66662 21.7723 8.48451 21.8628 8.28409C21.9534 8.08367 22 7.86886 22 7.65193C22 7.43499 21.9534 7.22018 21.8628 7.01976C21.7723 6.81934 21.6395 6.63723 21.4722 6.48384C21.3048 6.33044 21.1062 6.20876 20.8875 6.12575C20.6689 6.04273 20.4345 6 20.1979 6ZM19.9916 7.1955C20.057 7.17067 20.1271 7.15789 20.1979 7.15789C20.2687 7.15789 20.3387 7.17067 20.4041 7.1955C20.4695 7.22033 20.5289 7.25672 20.579 7.30259C20.629 7.34847 20.6687 7.40293 20.6958 7.46287C20.7229 7.52281 20.7368 7.58705 20.7368 7.65193C20.7368 7.7168 20.7229 7.78105 20.6958 7.84098C20.6687 7.90092 20.629 7.95538 20.579 8.00126L20.0068 8.52579L19.2444 7.82728L19.8168 7.30259C19.8668 7.25672 19.9262 7.22033 19.9916 7.1955ZM18.3512 8.64604L19.1136 9.34454L16.9619 11.3169C16.8632 11.4073 16.7381 11.4687 16.6012 11.4938L15.8576 11.6302L16.006 10.9492C16.0335 10.824 16.1008 10.7088 16.1994 10.6185L18.3512 8.64604Z"
                              fill="white"
                            />
                            <path
                              d="M11.4482 8.48536C11.5666 8.37679 11.7272 8.31579 11.8948 8.31579H13.7895C14.1383 8.31579 14.4211 8.05659 14.4211 7.73684C14.4211 7.4171 14.1383 7.15789 13.7895 7.15789H11.8948C11.3922 7.15789 10.9103 7.34088 10.555 7.6666C10.1996 7.99233 10 8.4341 10 8.89474V15.2632C10 15.7238 10.1996 16.1656 10.555 16.4913C10.9103 16.817 11.3922 17 11.8948 17H18.8422C19.3447 17 19.8266 16.817 20.182 16.4913C20.5373 16.1656 20.7369 15.7238 20.7369 15.2632V13.5263C20.7369 13.2066 20.4542 12.9474 20.1054 12.9474C19.7565 12.9474 19.4738 13.2066 19.4738 13.5263V15.2632C19.4738 15.4167 19.4072 15.564 19.2888 15.6725C19.1703 15.7811 19.0097 15.8421 18.8422 15.8421H11.8948C11.7272 15.8421 11.5666 15.7811 11.4482 15.6725C11.3297 15.564 11.2632 15.4167 11.2632 15.2632V8.89474C11.2632 8.74119 11.3297 8.59393 11.4482 8.48536Z"
                              fill="white"
                            />
                            <defs>
                              <linearGradient
                                id="paint0_linear_143_1569"
                                x1="-1.78814e-07"
                                y1="11.5"
                                x2="32"
                                y2="11.5"
                                gradientUnits="userSpaceOnUse"
                              >
                                <stop stopColor="#2B3D59" />
                                <stop offset="1" stopColor="#375685" />
                              </linearGradient>
                            </defs>
                          </svg>
                        </button>
                      </td>
                    </tr>
                  );
                })}
            </tbody>
          </table>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={enrolledUsersData?.total || 0}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="Users per page:"
          />

          <button
            onClick={handleDownload}
            className="rounded bg-[#36537F] px-4 py-2 text-white hover:bg-blue-700"
            disabled={downloadMutation.isPending}
          >
            Download Organizational Results
          </button>
        </div>
      </div>
    </>
  );
};

export default Results;
