import { ButtonHTMLAttributes, ReactNode } from "react";

interface Props extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
}

export default function RedButton({ children, ...props }: Props) {
  return (
    <button
      {...props}
      className="mx-auto h-11 w-full max-w-xs rounded-lg bg-gradient-to-r from-[#E6635A] to-[#D64D44] text-center text-[#f5f5f5] hover:from-[#D64D44] hover:to-[#E6635A] md:w-60"
    >
      {children}
    </button>
  );
}
