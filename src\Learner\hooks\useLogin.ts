import { useQueryClient, useMutation } from '@tanstack/react-query';
import axios from 'axios';

// login mutation
export function useLogin() {
  // get the query client
  const queryClient = useQueryClient();
  // create the mutation
  return useMutation({
    mutationFn: (data) => {
      return axios.post('/api/user/login', data);
    },
    onSuccess: (user) => {
      queryClient.setQueryData(['user'], user); 
    },
  })
};