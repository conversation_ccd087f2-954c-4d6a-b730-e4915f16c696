import { ReactNode } from "react";
import { useLocation } from "react-router-dom";

interface Props {
  children?: ReactNode;
  heading: string;
  icon?: ReactNode;
}

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

function ContentBox({ children, heading, icon }: Props) {
  const query = useQueryParam();
  const activity_id = query.get("activity_id") ?? "";

  return (
    <div className="flex h-fit w-full flex-col space-y-9 bg-white bg-opacity-10 px-[62px]">
      {/* <span className="flex items-center space-x-5">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path
            d="M0.888889 8.88889H6.22222C6.71111 8.88889 7.11111 8.48889 7.11111 8V0.888889C7.11111 0.4 6.71111 0 6.22222 0H0.888889C0.4 0 0 0.4 0 0.888889V8C0 8.48889 0.4 8.88889 0.888889 8.88889ZM0.888889 16H6.22222C6.71111 16 7.11111 15.6 7.11111 15.1111V11.5556C7.11111 11.0667 6.71111 10.6667 6.22222 10.6667H0.888889C0.4 10.6667 0 11.0667 0 11.5556V15.1111C0 15.6 0.4 16 0.888889 16ZM9.77778 16H15.1111C15.6 16 16 15.6 16 15.1111V8C16 7.51111 15.6 7.11111 15.1111 7.11111H9.77778C9.28889 7.11111 8.88889 7.51111 8.88889 8V15.1111C8.88889 15.6 9.28889 16 9.77778 16ZM8.88889 0.888889V4.44444C8.88889 4.93333 9.28889 5.33333 9.77778 5.33333H15.1111C15.6 5.33333 16 4.93333 16 4.44444V0.888889C16 0.4 15.6 0 15.1111 0H9.77778C9.28889 0 8.88889 0.4 8.88889 0.888889Z"
            fill="#9A9A9A"
          />
        </svg>
        <h1 className="text-[18px] font-normal text-[#8C8C8C]">
          <span className="text-[18px] font-semibold text-[#9A9A9A]">
            Skillseed
          </span>
          / {heading}
        </h1>
      </span> */}
      <span className="flex items-center space-x-7">
        {icon ? icon : <img src="/practice-activity-logo.png"></img>}

        <h1 className="text-3xl font-bold text-[#1c1c1c]">{heading}</h1>
      </span>
      <div
        key={activity_id}
        className="flex max-h-fit min-h-[85%] w-[90%] flex-col space-y-14 rounded-xl px-14 py-8"
      >
        {children}
      </div>
    </div>
  );
}

export default ContentBox;
