import { useRef, useEffect } from "react";
import { Message } from "../../types/Message";
import "./DotFlashing.css";

interface ChatContentProps {
  messages: Message[];
  isLoading?: boolean;
}

const ChatContent = ({ messages, isLoading }: ChatContentProps) => {
  const lastDivRef = useRef<HTMLDivElement>(null);

  const scrollToLastDiv = () => {
    lastDivRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (isLoading) {
      scrollToLastDiv();
    }
  }, [isLoading]);

  return (
    <div className="h-64 max-h-64 overflow-auto bg-[#F7F6E3] px-6 py-1">
      {messages.map((message: Message, index: number) => (
        <div
          key={index}
          className={`flex w-full flex-row py-2 ${
            message.sender == "user" ? "justify-end" : "justify-start"
          }`}
        >
          <div
            className={`${message.sender == "user" ? "order-2" : "order-1"}`}
          ></div>
          <div className="flex flex-col">
            <div
              className={`flex w-fit flex-col rounded-lg ${message.sender == "user" ? "bg-white" : "bg-[#36537F] bg-opacity-20"} px-2 py-3 text-white ${
                message.sender == "user" ? "order-1 mr-2" : "order-2 ml-2"
              }`}
            >
              <span className="text-xl font-medium text-[#454545]">
                {message.message}
              </span>
            </div>
            {!(message.sender == "user") ? (
              <svg
                className="order-2 ml-8 pb-4"
                width="29"
                height="38"
                viewBox="0 0 29 38"
                fill="none"
              >
                <path d="M0.519863 0V38L28.9409 0H0.519863Z" fill="#D0D5CF" />
              </svg>
            ) : (
              <svg
                width="30"
                height="38"
                viewBox="0 0 30 38"
                fill="none"
                className="order-2 mr-8 place-self-end pb-4"
              >
                <path d="M29.1383 0V38L0.717285 0H29.1383Z" fill="white" />
              </svg>
            )}
          </div>
        </div>
      ))}
      {isLoading ? (
        <>
          <div className="order-2 ml-2 flex w-24 flex-col rounded-lg bg-[#36537F] bg-opacity-20 px-2 py-3 text-center">
            <div className="col-3 self-center">
              <div className="snippet" data-title="dot-flashing">
                <div className="stage">
                  <div className="dot-flashing"></div>
                </div>
              </div>
            </div>
          </div>
          <svg
            className="order-2 ml-8 pb-4"
            width="29"
            height="38"
            viewBox="0 0 29 38"
            fill="none"
          >
            <path d="M0.519863 0V38L28.9409 0H0.519863Z" fill="#D0D5CF" />
          </svg>
        </>
      ) : null}
      <div ref={lastDivRef} />
    </div>
  );
};

export default ChatContent;
