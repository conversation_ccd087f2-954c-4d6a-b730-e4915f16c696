import { useEffect, useRef, useState } from "react";
import RedButton from "../components/RedButton";
import { useLocation } from "react-router-dom";
import { usePostActivityProgress } from "../hooks/usePostActivityProgress";
import { useGetActivity } from "../hooks/useGetActivity";
import GradientButton from "../components/GradientButton";
import Spinner from "../components/Spinner";
import { useNavigate } from "react-router-dom";
import NextButton from "../components/NextButton";
import CloseIcon from "@mui/icons-material/Close";
import showError from "../scripts/showErrorDialog";
import showSuccess from "../scripts/showSuccessDialog";
import "../components/VideoStyle.css";

interface TimeTag {
  time: string;
  tag: string;
}

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

function convertToJSON(input: string | undefined) {
  const lines = input?.split("\n");

  const result: TimeTag[] = [];

  lines?.forEach((line) => {
    const [min, sec, message] = line.split(":");

    // Trim any leading or trailing whitespace
    if (sec && message) {
      result.push({
        time: `${min.trim()}:${sec.trim()}`,
        tag: message.trim(),
      });
    }
  });

  return JSON.stringify(result);
}

function PlungeActivity() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [textAreaDisabled, setTextAreaDisabled] = useState<boolean>(true);
  const [timeTags, setTimeTags] = useState<TimeTag[]>([]);
  const [timeTag, setTimeTag] = useState<TimeTag>();
  const query = useQueryParam();
  const course_id = query.get("course_id") ?? "";
  const activity_id = query.get("activity_id") ?? "";
  const {
    data: activity,
    isLoading,
    isFetching,
    isError,
  } = useGetActivity(activity_id);
  const mutation = usePostActivityProgress();
  const [nextActivityLink, setnextActivityLink] = useState("");
  const [instructions, setInstructions] = useState("");
  const [begin, setBegin] = useState<boolean>(true);
  const [tagVideo, setTagVideo] = useState<boolean>(false);
  const [screenNumber, setScreenNumber] = useState<number>(5);
  const navigate = useNavigate();

  const redirectToLink = (nextActivityLink: string) => {
    navigate("/my-learning" + nextActivityLink);
  };

  const handleSubmit = async () => {
    if (timeTags.map((e) => e.tag).includes("")) {
      showError("Error", "Please fill all tags.");
    } else if (timeTags.length == 0) {
      showError("Error", "Please add tags.");
    } else if (timeTags.length < 3) {
      showError("Error", "Please add atleast 3 tags.");
    } else {
      mutation.mutate(
        {
          courseId: course_id,
          activityId: activity_id,
          userInput: JSON.stringify(timeTags),
        },
        {
          onSuccess: (data) => {
            if (data && data.next_activity_link) {
              setnextActivityLink(data.next_activity_link);
            }
            showSuccess("Success", "Plunge Activity successfully completed.");
          },
          onError: () => {
            showError("Submission failed", "Please try again.");
            console.log("Submission failed. Please try again.");
          },
        },
      );
    }
  };

  const handleChange = (value: string, index: number) => {
    const updatedTags = timeTags.map((t, i) =>
      i === index ? { ...t, tag: value } : t,
    );
    setTimeTags(updatedTags);
  };

  const insertTag = () => {
    if (videoRef.current) {
      setTextAreaDisabled(!textAreaDisabled);
      const video = videoRef.current;
      // const textarea = textareaRef.current;

      // Pause the video
      video.pause();

      setTagVideo(!tagVideo);

      // Get the current timestamp
      const currentTime = video.currentTime;
      const formattedTime = new Date(currentTime * 1000)
        .toISOString()
        .substr(14, 5); // MM:SS format

      setTimeTag({ time: formattedTime, tag: "" });

      // console.log([...timeTags, { time: formattedTime, tag: "" }]);
      // setTimeTags([...timeTags, { time: formattedTime, tag: "" }]);

      // // Insert the timestamp into the textarea
      // textarea.value += `\n${formattedTime} : \t`;

      // // Focus the textarea and move the cursor to the end
      // textarea.focus();
      // textarea.selectionStart = textarea.selectionEnd = textarea.value.length;
    }
  };

  const handleTagChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newTag = event.target.value;
    if (newTag)
      setTimeTag((prevTimeTag) =>
        prevTimeTag
          ? {
              ...prevTimeTag, // Spread previous timeTag to keep existing fields
              tag: newTag, // Update only the tag field
            }
          : prevTimeTag,
      );
    else {
      setTimeTag((prevTimeTag) =>
        prevTimeTag ? { ...prevTimeTag, tag: "" } : prevTimeTag,
      );
    }
  };

  const startVideo = () => {
    if (videoRef.current) {
      const video = videoRef.current;

      video.play();
    }
  };

  const beginVideo = () => {
    setBegin(false);
    startVideo();
  };

  const deleteTag = (index: number) => {
    const updatedTags: TimeTag[] = [];
    timeTags.forEach((t, i) => {
      if (i !== index) {
        updatedTags.push(t);
      }
    });
    setTimeTags(updatedTags);
  };

  const handleContextMenu = (event: React.MouseEvent<HTMLVideoElement>) => {
    event.preventDefault(); // Prevents the right-click menu from opening
  };

  const saveTag = () => {
    if (timeTag) {
      if (!timeTag.time) showError("Error", "Please Enter Tag Time");
      else if (!timeTag.tag) showError("Error", "Please Enter Tag Text");
      else {
        setTagVideo(!tagVideo);
        startVideo();
        setTimeTags([...timeTags, timeTag]);
      }
    }
  };

  function shiftScreen() {
    if (screenNumber < 4) {
      setScreenNumber(screenNumber + 1);
    } else if (screenNumber === 4) {
      setScreenNumber(screenNumber + 1);
    } else if (Boolean(nextActivityLink)) {
      redirectToLink(nextActivityLink);
    }
  }

  return (
    <>
      <Spinner
        loading={
          (isLoading || isFetching || mutation.isPending) &&
          !(isError || mutation.isError)
        }
      />
      {activity && (
        <>
          {screenNumber === 5 ? (
            <>
              <div className="flex w-full flex-col items-center gap-4 self-start px-4 md:gap-6 md:px-0">
                <div className="w-full max-w-3xl text-lg font-medium text-white md:text-xl">
                  {activity?.instructions && (
                    <p
                      className="mb-4"
                      dangerouslySetInnerHTML={{
                        __html: activity.instructions,
                      }}
                    />
                  )}
                </div>
                <div className="m-auto flex w-full max-w-3xl flex-col items-center justify-center gap-4 md:flex-row md:gap-0">
                  {/* Video Section */}
                  <div className="relative flex w-full bg-slate-300 p-2 md:mr-8 md:w-auto md:p-3">
                    {activity?.video ? (
                      <video
                        key={activity.video}
                        ref={videoRef}
                        src={activity.video}
                        controls
                        controlsList="nodownload"
                        onContextMenu={handleContextMenu}
                        className="hide-seekbar-tutorial h-auto w-full max-w-full"
                      />
                    ) : null}
                  </div>

                  {/* Right-side box for tag count */}
                  <div className="md:w-42 flex w-full flex-col items-center justify-center rounded border border-gray-300 bg-gray-100 p-4 align-middle shadow-md md:h-40">
                    <h2 className="text-center font-bold text-gray-700">
                      Number of Actions <br /> Behaviors/Words <br /> Found
                    </h2>
                    <span className="text-2xl font-semibold text-blue-600">
                      {timeTags.length}
                    </span>
                  </div>
                </div>

                {begin ? (
                  <div className="w-full max-w-3xl text-lg font-medium text-white md:text-xl">
                    {activity?.instructions_below && (
                      <p
                        className="mb-4 list-inside list-decimal pl-4"
                        dangerouslySetInnerHTML={{
                          __html: activity.instructions_below,
                        }}
                      />
                    )}
                  </div>
                ) : (
                  <h1 className="w-full max-w-3xl px-4 text-center text-lg font-medium text-white md:px-0 md:text-xl">
                    Let's tag and describe at least 3 actions/behaviours /words
                    of the volunteer that made you feel uncomfortable or
                    offended. You will be able to progress to the next segment
                    after you have described 3 of such moments!
                  </h1>
                )}
                {tagVideo ? (
                  <div className="!mt-3 flex w-full max-w-3xl flex-col items-center justify-center gap-2 px-4 md:flex-row md:gap-0 md:px-0">
                    <h2 className="w-full content-center text-center text-2xl text-white md:mr-2 md:w-2/12 md:text-4xl">
                      <strong>{timeTag?.time}:</strong>
                    </h2>
                    <input
                      className="w-full border-2 border-black px-3 py-2 text-black md:mr-4 md:w-3/12 md:py-1"
                      type="text"
                      value={timeTag?.tag}
                      onChange={handleTagChange}
                      placeholder="Enter tag description"
                    />
                    <GradientButton
                      text="Save Tag"
                      color1="#EEC300"
                      color2="#EEC300"
                      textColor="black"
                      width="100%"
                      onClick={saveTag}
                    />
                  </div>
                ) : (
                  <>
                    {begin ? (
                      <RedButton onClick={beginVideo}>Begin</RedButton>
                    ) : (
                      <RedButton onClick={insertTag}>Tag</RedButton>
                    )}
                  </>
                )}
              </div>

              {/* {timeTags.map((t, index) => (
              <div key={index} className="!mt-3 flex w-full justify-center">
                <h2 className="mr-6 w-3/12 text-center text-4xl">
                  <strong>{t.time}:</strong>
                </h2>
                <input
                  className="mr-4 w-3/12 border-2 border-black px-3"
                  type="text"
                  value={t.tag}
                  onChange={(e) => handleChange(e.target.value, index)}
                />
                <button
                  className="text-bold align-center"
                  onClick={() => deleteTag(index)}
                >
                  <CloseIcon sx={{ "&:hover": { color: "red" } }} />
                </button>
              </div>
            ))} */}
              <div className="w-full max-w-3xl px-4 text-center md:px-0">
                <GradientButton
                  text="Submit"
                  color1="#2B3D59"
                  color2="#375685"
                  disabled={mutation.isSuccess}
                  onClick={handleSubmit}
                  width="100%"
                />
              </div>
            </>
          ) : null}
          {mutation.isSuccess ? (
            <div className="mx-auto max-w-3xl px-4 text-center md:px-0">
              <h1 className="text-lg text-white md:text-xl">
                Thank you, your response has been submitted. Please move on to
                the next section by clicking the yellow "Continue" button on the
                right.
              </h1>
            </div>
          ) : null}
          <NextButton
            onClick={shiftScreen}
            display={screenNumber < 4 || Boolean(nextActivityLink)}
          />
        </>
      )}
    </>
  );
}

export default PlungeActivity;
