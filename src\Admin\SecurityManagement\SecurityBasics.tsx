import React from "react";
import SecurityBasicsIcon from "../Icons/SecurityBasicsIcon";

const securityBasicsCardData = [
  {
    icon: (
      <svg
        width="49"
        height="49"
        viewBox="0 0 49 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle cx="24.5" cy="24.5" r="24.5" fill="url(#paint0_linear_0_2)" />
        <path
          d="M16 24.5C16 24.195 16.1185 23.9025 16.3295 23.6868C16.5405 23.4712 16.8266 23.35 17.125 23.35H25.6638L23.0763 20.7165C22.9714 20.6093 22.8882 20.482 22.8314 20.3419C22.7746 20.2018 22.7454 20.0516 22.7454 19.9C22.7454 19.7484 22.7746 19.5982 22.8314 19.4581C22.8882 19.318 22.9714 19.1907 23.0763 19.0835C23.1811 18.9763 23.3057 18.8912 23.4427 18.8332C23.5798 18.7752 23.7267 18.7453 23.875 18.7453C24.0233 18.7453 24.1702 18.7752 24.3073 18.8332C24.4443 18.8912 24.5689 18.9763 24.6738 19.0835L29.1737 23.6835C29.2762 23.7929 29.3565 23.9218 29.41 24.063C29.5225 24.343 29.5225 24.657 29.41 24.937C29.3565 25.0782 29.2762 25.2071 29.1737 25.3165L24.6738 29.9165C24.5692 30.0243 24.4447 30.1098 24.3076 30.1682C24.1706 30.2266 24.0235 30.2567 23.875 30.2567C23.7265 30.2567 23.5794 30.2266 23.4424 30.1682C23.3053 30.1098 23.1808 30.0243 23.0763 29.9165C22.9708 29.8096 22.8871 29.6824 22.83 29.5423C22.7729 29.4021 22.7435 29.2518 22.7435 29.1C22.7435 28.9482 22.7729 28.7979 22.83 28.6577C22.8871 28.5176 22.9708 28.3904 23.0763 28.2835L25.6638 25.65H17.125C16.8266 25.65 16.5405 25.5288 16.3295 25.3132C16.1185 25.0975 16 24.805 16 24.5ZM19.375 13H30.625C31.5201 13 32.3786 13.3635 33.0115 14.0105C33.6444 14.6575 34 15.535 34 16.45V32.55C34 33.465 33.6444 34.3425 33.0115 34.9895C32.3786 35.6365 31.5201 36 30.625 36H19.375C18.4799 36 17.6215 35.6365 16.9885 34.9895C16.3556 34.3425 16 33.465 16 32.55V29.1C16 28.795 16.1185 28.5025 16.3295 28.2868C16.5405 28.0712 16.8266 27.95 17.125 27.95C17.4234 27.95 17.7095 28.0712 17.9205 28.2868C18.1315 28.5025 18.25 28.795 18.25 29.1V32.55C18.25 32.855 18.3685 33.1475 18.5795 33.3632C18.7905 33.5788 19.0766 33.7 19.375 33.7H30.625C30.9234 33.7 31.2095 33.5788 31.4205 33.3632C31.6315 33.1475 31.75 32.855 31.75 32.55V16.45C31.75 16.145 31.6315 15.8525 31.4205 15.6368C31.2095 15.4212 30.9234 15.3 30.625 15.3H19.375C19.0766 15.3 18.7905 15.4212 18.5795 15.6368C18.3685 15.8525 18.25 16.145 18.25 16.45V19.9C18.25 20.205 18.1315 20.4975 17.9205 20.7132C17.7095 20.9288 17.4234 21.05 17.125 21.05C16.8266 21.05 16.5405 20.9288 16.3295 20.7132C16.1185 20.4975 16 20.205 16 19.9V16.45C16 15.535 16.3556 14.6575 16.9885 14.0105C17.6215 13.3635 18.4799 13 19.375 13Z"
          fill="white"
        />
        <defs>
          <linearGradient
            id="paint0_linear_0_2"
            x1="0"
            y1="24.5"
            x2="49"
            y2="24.5"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#D0B120" />
            <stop offset="1" stopColor="#B19C3A" />
          </linearGradient>
        </defs>
      </svg>
    ),
    title: "Sign-in Activity",
    desc: "See when and where you’ve signed in and tell us if something looks unusual.",
    buttonText: "View My Activity",
  },
  {
    icon: (
      <svg
        width="49"
        height="49"
        viewBox="0 0 49 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle cx="24.5" cy="24.5" r="24.5" fill="url(#paint0_linear_0_2)" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M31.625 19.3333C32.2548 19.3333 32.859 19.5842 33.3043 20.0307C33.7498 20.4772 34 21.0828 34 21.7143V33.619C34 34.2505 33.7498 34.8561 33.3043 35.3026C32.859 35.7492 32.2548 36 31.625 36H17.375C16.7452 36 16.141 35.7492 15.6957 35.3026C15.2502 34.8561 15 34.2505 15 33.619V21.7143C15 21.0828 15.2502 20.4772 15.6957 20.0307C16.141 19.5842 16.7452 19.3333 17.375 19.3333H18.5625V16.9524C18.5625 15.3737 19.188 13.8597 20.3016 12.7434C21.415 11.6271 22.9253 11 24.5 11C25.2797 11 26.0518 11.154 26.7722 11.4531C27.4925 11.7523 28.1472 12.1907 28.6984 12.7434C29.2499 13.2962 29.6872 13.9524 29.9855 14.6745C30.284 15.3967 30.4375 16.1707 30.4375 16.9524V19.3333H31.625ZM24.5 13.381C23.5552 13.381 22.649 13.7573 21.9809 14.427C21.3128 15.0968 20.9375 16.0052 20.9375 16.9524V19.3333H28.0625V16.9524C28.0625 16.0052 27.6872 15.0968 27.0191 14.427C26.351 13.7573 25.4448 13.381 24.5 13.381ZM19.75 30.0476V31.4365H29.25V30.0476H19.75ZM20.3268 27.7063L21.2089 28.2272L21.7857 27.1855L22.3624 28.2272L23.2447 27.7063L22.6679 26.6647H23.8214V25.623H22.6679L23.2447 24.6161L22.3624 24.0952L21.7857 25.1022L21.2089 24.0952L20.3268 24.6161L20.9036 25.623H19.75V26.6647H20.9036L20.3268 27.7063ZM25.7553 27.7063L26.6376 28.2272L27.2143 27.1855L27.7911 28.2272L28.6732 27.7063L28.0964 26.6647H29.25V25.623H28.0964L28.6732 24.6161L27.7911 24.0952L27.2143 25.1022L26.6376 24.0952L25.7553 24.6161L26.3321 25.623H25.1786V26.6647H26.3321L25.7553 27.7063Z"
          fill="white"
        />
        <defs>
          <linearGradient
            id="paint0_linear_0_2"
            x1="0"
            y1="24.5"
            x2="49"
            y2="24.5"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#D0B120" />
            <stop offset="1" stopColor="#B19C3A" />
          </linearGradient>
        </defs>
      </svg>
    ),
    title: "Password Security",
    desc: "Help keep your account safer by using a stronger password.",
    buttonText: "Change my password",
  },
  {
    icon: (
      <svg
        width="49"
        height="49"
        viewBox="0 0 49 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle cx="24.5" cy="24.5" r="24.5" fill="url(#paint0_linear_0_2)" />
        <path
          d="M15.3125 33.6281C16.4937 32.5128 17.8666 31.6347 19.4311 30.9937C20.9956 30.3528 22.6853 30.0319 24.5 30.0311C26.3147 30.0303 28.0048 30.3511 29.5702 30.9937C31.1356 31.6364 32.508 32.514 33.6875 33.6268V17.0752H15.3125V33.6281ZM24.5 26.5376C23.3993 26.5376 22.4678 26.1713 21.7057 25.4387C20.9436 24.7061 20.5625 23.8107 20.5625 22.7526C20.5625 21.6945 20.9436 20.7991 21.7057 20.0665C22.4678 19.3339 23.3993 18.9676 24.5 18.9676C25.6007 18.9676 26.5322 19.3339 27.2943 20.0665C28.0564 20.7991 28.4375 21.6945 28.4375 22.7526C28.4375 23.8107 28.0564 24.7061 27.2943 25.4387C26.5322 26.1713 25.6007 26.5376 24.5 26.5376ZM14 36V15.8135H18.4428V13H19.8564V15.8135H29.246V13H30.5585V15.8135H35V36H14Z"
          fill="white"
        />
        <defs>
          <linearGradient
            id="paint0_linear_0_1"
            x1="0"
            y1="24.5"
            x2="49"
            y2="24.5"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#D0B120" />
            <stop offset="1" stopColor="#B19C3A" />
          </linearGradient>
        </defs>
      </svg>
    ),
    title: "Security Contact Info",
    desc: "We’ll use this info to contact you if you ever forget your password.",
    buttonText: "Update my info",
  },
  {
    icon: (
      <svg
        width="49"
        height="49"
        viewBox="0 0 49 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle cx="24.5" cy="24.5" r="24.5" fill="url(#paint0_linear_0_2)" />
        <path
          d="M31.3684 25.6364C31.7621 25.6364 32.1442 25.6818 32.5263 25.7386V22.2273C32.5263 21.6245 32.2823 21.0464 31.848 20.6202C31.4137 20.194 30.8247 19.9545 30.2105 19.9545H29.0526V17.6818C29.0526 14.5455 26.4589 12 23.2632 12C20.0674 12 17.4737 14.5455 17.4737 17.6818V19.9545H16.3158C15.7016 19.9545 15.1126 20.194 14.6783 20.6202C14.244 21.0464 14 21.6245 14 22.2273V33.5909C14 34.8523 15.0305 35.8636 16.3158 35.8636H25.3589C24.7684 34.8636 24.4211 33.7045 24.4211 32.4545C24.4211 28.6932 27.5358 25.6364 31.3684 25.6364ZM19.7895 17.6818C19.7895 15.7955 21.3411 14.2727 23.2632 14.2727C25.1853 14.2727 26.7368 15.7955 26.7368 17.6818V19.9545H19.7895V17.6818ZM23.2632 30.1818C22.8051 30.1818 22.3574 30.0485 21.9766 29.7988C21.5957 29.5491 21.2989 29.1941 21.1236 28.7788C20.9484 28.3635 20.9025 27.9066 20.9919 27.4657C21.0812 27.0248 21.3018 26.6199 21.6256 26.302C21.9495 25.9842 22.3622 25.7677 22.8114 25.68C23.2606 25.5923 23.7262 25.6373 24.1494 25.8094C24.5725 25.9814 24.9342 26.2727 25.1887 26.6464C25.4431 27.0202 25.5789 27.4596 25.5789 27.9091C25.5789 29.1705 24.5484 30.1818 23.2632 30.1818ZM36 31.3182V33.5909H32.5263V37H30.2105V33.5909H26.7368V31.3182H30.2105V27.9091H32.5263V31.3182H36Z"
          fill="white"
        />
        <defs>
          <linearGradient
            id="paint0_linear_0_1"
            x1="0"
            y1="24.5"
            x2="49"
            y2="24.5"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#D0B120" />
            <stop offset="1" stopColor="#B19C3A" />
          </linearGradient>
        </defs>
      </svg>
    ),
    title: "More Security Options",
    desc: "Try the latest security options to help keep your account safe.",
    buttonText: "Explore",
  },
];

const SecurityBasicsCard = ({
  icon,
  title,
  desc,
  buttonText,
}: {
  icon: React.ReactNode;
  title: string;
  desc: string;
  buttonText: string;
}) => (
  <div className="relative h-[275px] w-[267px] cursor-pointer rounded">
    <div className="absolute inset-0 left-[133px] z-10 inline-block h-[10px] w-6/12 rounded bg-gradient-to-r from-[#2B3D59] to-[#375685]"></div>
    <div className="absolute top-[6px] z-20 flex h-[269px] w-full flex-col items-center justify-between rounded-lg bg-[#F5F5F5] p-7">
      {icon}
      <h1 className="text-lg font-semibold text-[#353434]">{title}</h1>
      <p className="text-sm font-normal text-[#979595]"> {desc}</p>
      <button className="h-[31px] w-[187px] rounded bg-gradient-to-r from-[#2B3D59] to-[#375685] text-center text-sm font-medium text-white hover:from-[#375685] hover:to-[#2B3D59]">
        {buttonText}
      </button>
    </div>
  </div>
);

function SecurityBasics() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex gap-16">
        <h1 className="text-2xl font-medium text-[#6F6F6F]">Security Basics</h1>
        <SecurityBasicsIcon />
      </div>
      <h1 className="text-base font-normal text-[#7A7A7A]">
        Manage your password, protect your account and view additional security
        resources.
      </h1>
      <div className="grid grid-cols-3 gap-4">
        {securityBasicsCardData.map((item, idx) => (
          <SecurityBasicsCard {...item} key={"security-basic-card-" + idx} />
        ))}
      </div>
    </div>
  );
}

export default SecurityBasics;
