import { useMutation } from '@tanstack/react-query';
import useAxios from '../../hooks/axiosObject';
import { User } from '../../types/User';
import { AxiosInstance } from 'axios';


// api call
export async function updateUser(axiosObject: AxiosInstance, user: User): Promise<User> {
  const response = await axiosObject.patch<User>('/api/v1/user/update', user);
  console.log(response)
  return response.data;
};

export const useUpdateUser = () => {
  const axiosInstance = useAxios();
  return useMutation({
    mutationFn: ({ user }: {
      user: User
    }) => {
      return updateUser(axiosInstance, user);
    },
  })
};