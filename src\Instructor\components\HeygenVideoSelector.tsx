import React, { useState, useEffect } from "react";
import {
  useListHeygenVideos,
  HeygenVideoWithUrl,
} from "../hooks/useHeygenVideos";
import Spinner from "../../Learner/components/Spinner";

interface HeygenVideoSelectorProps {
  onSelectVideo: (videoUrl: string, videoId: number) => void;
  selectedVideoId?: number;
}

const HeygenVideoSelector: React.FC<HeygenVideoSelectorProps> = ({
  onSelectVideo,
  selectedVideoId,
}) => {
  const { data: videos, isLoading, error, refetch } = useListHeygenVideos();
  const [previewVideo, setPreviewVideo] = useState<HeygenVideoWithUrl | null>(
    null,
  );

  // Effect to handle when selectedVideoId changes
  useEffect(() => {
    if (selectedVideoId) {
      console.log(
        "HeygenVideoSelector: Selected video ID changed to",
        selectedVideoId,
      );

      // Refetch the videos to ensure we have the latest data
      refetch();

      // After render, scroll to the selected video
      setTimeout(() => {
        const selectedElement = document.getElementById(
          `heygen-video-${selectedVideoId}`,
        );
        if (selectedElement) {
          selectedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }, 100);
    }
  }, [selectedVideoId, refetch]);

  const handleSelectVideo = (video: HeygenVideoWithUrl) => {
    console.log("Selecting Heygen video in selector:", {
      id: video.id,
      name: video.name,
      url: video.video_url,
    });
    onSelectVideo(video.video_url, video.id);
    setPreviewVideo(null);
  };

  const handlePreviewVideo = (video: HeygenVideoWithUrl) => {
    setPreviewVideo(video);
  };

  const closePreview = () => {
    setPreviewVideo(null);
  };

  if (isLoading) {
    return <Spinner loading={true} />;
  }

  if (error) {
    return (
      <div className="p-4 text-red-600">
        Error loading videos. Please try again.
      </div>
    );
  }

  if (!videos || videos.length === 0) {
    return (
      <div className="p-4 text-gray-600">
        <p>You don't have any saved generated videos yet.</p>
        <p className="mt-2">
          Create videos in the Scenario Creation section and they will appear.
        </p>
      </div>
    );
  }

  return (
    <div className="mt-4">
      <h3 className="mb-4 text-lg font-medium">Your Generated Videos</h3>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
        {videos.map((video) => (
          <div
            key={video.id}
            id={`heygen-video-${video.id}`}
            className={`relative overflow-hidden rounded-lg border ${
              selectedVideoId === video.id
                ? "border-blue-500 ring-2 ring-blue-500"
                : "border-gray-200"
            }`}
          >
            <div className="p-4">
              <h4 className="mb-2 font-medium">{video.name}</h4>
              <p className="text-sm text-gray-500">
                Created: {new Date(video.created_at).toLocaleDateString()}
              </p>

              <div className="mt-4 flex space-x-2">
                <button
                  onClick={() => handlePreviewVideo(video)}
                  className="rounded bg-gray-100 px-3 py-1 text-sm text-gray-700 hover:bg-gray-200"
                >
                  Preview
                </button>
                <button
                  onClick={() => handleSelectVideo(video)}
                  className={`rounded px-3 py-1 text-sm ${
                    selectedVideoId === video.id
                      ? "bg-blue-100 text-blue-700"
                      : "bg-blue-500 text-white hover:bg-blue-600"
                  }`}
                >
                  {selectedVideoId === video.id ? "Selected" : "Select"}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {previewVideo && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
          <div className="relative max-h-[90vh] max-w-4xl overflow-hidden rounded-lg bg-white p-4">
            <button
              onClick={closePreview}
              className="absolute right-4 top-4 rounded-full bg-gray-200 p-2 text-gray-700 hover:bg-gray-300"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            <h3 className="mb-4 text-xl font-medium">{previewVideo.name}</h3>

            <div className="overflow-hidden rounded-lg bg-black">
              <video
                controls
                className="max-h-[70vh] w-full"
                src={previewVideo.video_url}
              >
                Your browser does not support the video tag.
              </video>
            </div>

            <div className="mt-4 flex justify-end">
              <button
                onClick={() => handleSelectVideo(previewVideo)}
                className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
              >
                Select This Video
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HeygenVideoSelector;
