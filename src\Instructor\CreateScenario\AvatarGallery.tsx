import React, { useEffect, useRef } from "react";
import <PERSON> from "../../assets/vids/Ann.jpg";
import <PERSON> from "../../assets/vids/<PERSON>.jpg";
import <PERSON> from "../../assets/vids/<PERSON>.jpg";
import <PERSON><PERSON><PERSON> from "../../assets/vids/Elenora.jpg";
import <PERSON> from "../../assets/vids/Dexter.jpg";
import <PERSON> from "../../assets/vids/Judy.jpg";
import June from "../../assets/vids/June.jpg";
import <PERSON> from "../../assets/vids/Silas.jpg";
import <PERSON> from "../../assets/vids/Wayne.jpg";
import <PERSON><PERSON> from "../../assets/vids/Amina.jpg";
import <PERSON> from "../../assets/vids/Graham.jpg";
import <PERSON> from "../../assets/vids/Pedro.jpg";
import <PERSON> from "../../assets/vids/<PERSON>.jpg";
import <PERSON><PERSON><PERSON> from "../../assets/vids/Alessandra.jpg";

const AVATARS = [
  {
    avatar_id: "<PERSON>_Therapist_public",
    name: "<PERSON>",
  },
  {
    avatar_id: "<PERSON><PERSON>rapist_public",
    name: "<PERSON>",
  },
  {
    avatar_id: "Bryan_FitnessCoach_public",
    name: "Bryan",
  },
  {
    avatar_id: "Elenora_IT_Sitting_public",
    name: "Elenora",
  },

  // New avatars
  {
    avatar_id: "Dexter_Lawyer_Sitting_public",
    name: "Dexter",
  },
  {
    avatar_id: "Judy_Teacher_Sitting2_public",
    name: "Judy",
  },
  {
    avatar_id: "June_HR_public",
    name: "June",
  },
  {
    avatar_id: "SilasHR_public",
    name: "Silas",
  },
  {
    avatar_id: "Wayne_20240711",
    name: "Wayne",
  },
  {
    avatar_id: "Amina_Chair_Sitting_public",
    name: "Amina",
  },
  {
    avatar_id: "Alessandra_Chair_Sitting_public",
    name: "Alessandra",
  },
  {
    avatar_id: "Graham_Black_Suit_public",
    name: "Graham",
  },
  {
    avatar_id: "Pedro_Chair_Sitting_public",
    name: "Pedro",
  },
  {
    avatar_id: "Marianne_Chair_Sitting_public",
    name: "Marianne",
  },
];

const videoMap: { [key: string]: string } = {
  Ann: Ann,
  Shawn: Shawn,
  Bryan: Bryan,
  Elenora: Elenora,
  Dexter: Dexter,
  Judy: Judy,
  June: June,
  Silas: Silas,
  Wayne: Wayne,
  Amina: Amina,
  Graham: Graham,
  Pedro: Pedro,
  Marianne: Marianne,
  Alessandra: Alessandra,
};

interface CardProps {
  children: React.ReactNode;
  isPressable?: boolean;
  isHoverable?: boolean;
  onPress?: () => void;
  className?: string;
}

const Card: React.FC<CardProps> = ({
  children,
  isPressable,
  isHoverable,
  onPress,
  className = "",
}) => {
  const baseClasses = "rounded-lg overflow-hidden";
  const hoverClasses = isHoverable ? "hover:opacity-80 transition-opacity" : "";
  const pressableClasses = isPressable ? "cursor-pointer" : "";

  return (
    <div
      className={`${baseClasses} ${hoverClasses} ${pressableClasses} ${className}`}
      onClick={isPressable ? onPress : undefined}
    >
      {children}
    </div>
  );
};

interface CardBodyProps {
  children: React.ReactNode;
  className?: string;
}

const CardBody: React.FC<CardBodyProps> = ({ children, className = "" }) => {
  return <div className={className}>{children}</div>;
};

interface AvatarGalleryProps {
  selectedAvatarId: string;
  onSelect: (avatarId: string) => void;
}

export default function AvatarGallery({
  selectedAvatarId,
  onSelect,
}: AvatarGalleryProps) {
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([]);

  useEffect(() => {
    videoRefs.current = videoRefs.current.slice(0, AVATARS.length);

    videoRefs.current.forEach((videoRef) => {
      if (videoRef) {
        const playPromise = videoRef.play();
        if (playPromise !== undefined) {
          playPromise.catch((error) => {
            console.log("Video play error:", error);
          });
        }
      }
    });
  }, []);

  return (
    <>
      <h2 className="mb-4 text-4xl font-bold text-gray-800">Choose Avatar</h2>
      <div className="h-[500px] overflow-y-scroll">
        <div className="grid grid-cols-2 gap-4">
          {AVATARS.map((avatar, index) => (
            <Card
              key={avatar.avatar_id}
              isPressable
              isHoverable
              onPress={() => onSelect(avatar.avatar_id)}
              className={`${
                selectedAvatarId === avatar.avatar_id
                  ? "border-4 border-blue-400"
                  : ""
              } w-full bg-black/20`}
            >
              <CardBody className="p-0">
                <div className="relative aspect-video w-full overflow-hidden">
                  <img src={videoMap[avatar.name]} />
                  <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-2 text-center text-white">
                    {avatar.name}
                  </div>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    </>
  );
}
