import React, { useEffect, useState } from 'react';
import UserSearch from './UserSearch';
import { UserSearchResponse } from '../../types/User';
import { useGetActivityResult } from '../hooks/useGetActivityResult';
import Spinner from '../../Learner/components/Spinner';

const FeedbackAnalytics: React.FC = () => {
  const [noResponse, setNoResponse] = useState<boolean>(false);
  const [userId, setUserId] = useState<string>("");
  const { data: result, isFetching } = useGetActivityResult(userId, "feedback");

  const handleSelectUser = (user: UserSearchResponse) => {
    setUserId(user.id.toLocaleString());
  };

  useEffect(() => {
    if (result && result.length > 0) {
      setNoResponse(false);
    } else {
      setNoResponse(true);
    }
  }, [result]);

  useEffect(() => {
    setNoResponse(false);
  }, [userId])

  return (
    <>
      <Spinner loading={isFetching} />
      <UserSearch onSelect={handleSelectUser} />
      {!noResponse ? (
        <div className="flex w-full flex-col px-10 justify-center bg-[#F7F6E3] py-10">
          {result?.map(each => {
            return (
              each.user_input.map((qa: { question: string, answer: string }) => (
                <>
                  <h2 className="text-lg font-semibold text-gray-800 mb-1">Question:</h2>
                  <p className="text-base text-gray-600 mb-1">{qa.question}</p>
                  <h2 className="text-lg font-semibold text-gray-800">Answer:</h2>
                  <p className="text-base text-gray-600 mb-4">{qa.answer}</p>
                </>
              ))
            )
          })}
        </div>
      ) : (
        <div className="flex w-full flex-col items-center justify-center space-y-20 bg-[#F7F6E3] py-10">
          <p>This user did not fill any Feedback.</p>
        </div>
      )}
    </>
  );
};

export default FeedbackAnalytics;
