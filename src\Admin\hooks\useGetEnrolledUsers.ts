import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { AxiosInstance } from "axios";
import { User } from "../../types/User";

interface EnrolledUsersResponse {
  items: User[];
  total: number;
  page: number;
  size: number;
}

// API call to get enrolled users for a course
const getEnrolledUsers = async (
  axiosObject: AxiosInstance,
  courseId: string,
  page: number,
  size: number
): Promise<EnrolledUsersResponse> => {
  const response = await axiosObject.get<EnrolledUsersResponse>(
    `/api/v1/course_progress/enrolled-users/${courseId}?page=${page}&size=${size}`
  );
  console.log("Enrolled users response:", response.data);
  return response.data;
};

export function useGetEnrolledUsers(courseId: string, page: number = 1, size: number = 50) {
  const axiosObject = useAxios();
  
  return useQuery<EnrolledUsersResponse>({
    queryKey: ["enrolledUsers", courseId, page, size],
    refetchOnWindowFocus: false,
    queryFn: () => getEnrolledUsers(axiosObject, courseId, page, size),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!courseId,
  });
}
