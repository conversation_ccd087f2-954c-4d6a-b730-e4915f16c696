import React, { useEffect, useState } from 'react';
import { Select, MenuItem, Button, SelectChangeEvent } from '@mui/material';
import UserSearch from './UserSearch';
import { UserSearchResponse } from '../../types/User';
import { useGetActivityResult } from '../hooks/useGetActivityResult';
import { useGetActivityResultByCohort } from '../hooks/useGetActivityResultByCohort';
import Spinner from '../../Learner/components/Spinner';
import ListeningComponent from '../../Learner/AIActivity/ListeningComponent';
import RespondingComponent from '../../Learner/AIActivity/RespondingComponent';
import { useGetCohorts } from '../hooks/useGetCohorts';

interface ResponseData {
  facial_expressions: number;
  eye_contact: number;
  body_movement_and_posture: number;
  gestures: number;
  tone_and_manner_of_speech: number;
  choice_of_words: number;
}

const AIDashboard: React.FC = () => {
  const [response, setResponse] = useState<boolean>(false);
  const [noResponse, setNoResponse] = useState<boolean>(false);
  const [userId, setUserId] = useState<string>("");
  const [searchType, setSearchType] = useState<string>("user");
  const [cohort, setCohort] = useState<string>("");
  const { data: result, isFetching } = useGetActivityResult(userId, "boss_challenge");
  const { data: cohortResult, isFetching: isFetchingCohortResult } = useGetActivityResultByCohort(cohort, "boss_challenge");

  const { data: cohorts, isFetching: isFetchingCohort } = useGetCohorts();

  const [respondingData, setRespondingData] = useState<ResponseData | null>(null);
  const [listeningData, setListeningData] = useState<ResponseData | null>(null);
  const [youDidWellAtListening, setyouDidWellAtListening] = useState<string[]>([]);
  const [youDidWellAtResponding, setyouDidWellAtResponding] = useState<string[]>([]);
  const [youCanImproveOnListening, setyouCanImproveOnListening] = useState<string[]>([]);
  const [youCanImproveOnResponding, setyouCanImproveOnResponding] = useState<string[]>([]);

  const handleSelectUser = (user: UserSearchResponse) => {
    setUserId(user.id.toLocaleString());
  };

  useEffect(() => {
    if (result && result.length > 0) {
      if (result[0].result["listening"]) {
        loadListeningData(result[0].result["listening"]);
      }
      if (result[0].result["responding"]) {
        loadRespondingData(result[0].result["responding"]);
      }
      setResponse(true);
      setNoResponse(false);
    } else {
      setNoResponse(true);
    }
  }, [result]);

  useEffect(() => {
    if (result && result.length > 0) {
      if (result[0].result["listening"]) {
        loadListeningData(result[0].result["listening"]);
      }
      if (result[0].result["responding"]) {
        loadRespondingData(result[0].result["responding"]);
      }
      setResponse(true);
      setNoResponse(false);
    } else {
      setNoResponse(true);
    }
  }, [result]);

  useEffect(() => {
    console.log(cohortResult)
    if (cohortResult) {
      if (cohortResult.result["listening"]) {
        setListeningData(cohortResult.result["listening"]["listening"]);
      }
      if (cohortResult.result["responding"]) {
        setRespondingData(cohortResult.result["responding"]["responding"]);
      }
      setResponse(true);
      setNoResponse(false);
    } else {
      setNoResponse(true);
    }
  }, [cohortResult]);

  useEffect(() => {
    setNoResponse(false);
  }, [userId]);

  const loadListeningData = (response: any) => {
    if (response && typeof response == "object") {
      setListeningData(response["listening"]);
      setyouDidWellAtListening(response["you_did_well_at_the_following"]);
      setyouCanImproveOnListening(response["you_can_improve_by_focusing_on_the_following"]);
    }
  };

  const loadRespondingData = (response: any) => {
    if (response && typeof response == "object") {
      setRespondingData(response["responding"]);
      setyouDidWellAtResponding(response["you_did_well_at_the_following"]);
      setyouCanImproveOnResponding(response["you_can_improve_by_focusing_on_the_following"]);
    }
  };

  const handleSearchTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchType(e.target.value);
  };

  const handleSelect = (event: SelectChangeEvent) => {
    console.log(event.target.value)
    setCohort(event.target.value)
  };

  return (
    <>
      <Spinner loading={isFetching || isFetchingCohort || isFetchingCohortResult} />
      <div className="flex w-full flex-col items-center justify-center py-5">
        <div className="flex space-x-4 mb-4">
          <label>
            <input
              type="radio"
              value="user"
              checked={searchType === "user"}
              onChange={handleSearchTypeChange}
            />
            Search By User
          </label>
          <label>
            <input
              type="radio"
              value="cohort"
              checked={searchType === "cohort"}
              onChange={handleSearchTypeChange}
            />
            Search By Cohort
          </label>
        </div>
      </div>

      {searchType === "user" ? (
        <>
          <UserSearch onSelect={handleSelectUser} />
          {!noResponse ? (
            <div className="flex w-full flex-col items-center justify-center space-y-20 bg-[#F7F6E3] py-10">
              {response ? (
                <div className="grid grid-cols-2 gap-4 w-full max-w-7xl text-center">
                  {listeningData ? (
                    <ListeningComponent
                      listeningData={listeningData}
                      youDidWellAt={youDidWellAtListening}
                      youCanImproveOn={youCanImproveOnListening}
                    />
                  ) : null}
                  {respondingData ? (
                    <RespondingComponent
                      respondingData={respondingData}
                      youDidWellAt={youDidWellAtResponding}
                      youCanImproveOn={youCanImproveOnResponding}
                    />
                  ) : null}
                </div>
              ) : null}
            </div>
          ) : (
            <div className="flex w-full flex-col items-center justify-center space-y-20 bg-[#F7F6E3] py-10">
              <p>No user result was found against this activity.</p>
            </div>
          )}
        </>
      ) : (
        <>
          <div className="flex justify-between items-center mb-4">
            <div>
              <label htmlFor="cohort-select" className="mr-2">Select Cohort :</label>
              {cohorts ? (
                <Select id="cohort-select" defaultValue={cohorts[0].id.toLocaleString()} onChange={handleSelect}>
                  {cohorts && cohorts.map(c => (
                    <MenuItem key={c.id} value={c.id}>{c.name}</MenuItem>
                  ))}
                </Select>
              ) : null}
            </div>
          </div>
          <div className="flex w-full flex-col items-center justify-center space-y-20 bg-[#F7F6E3] py-10">
            {response ? (
              <div className="grid grid-cols-2 gap-4 w-full max-w-7xl text-center">
                {listeningData ? (
                  <ListeningComponent
                    listeningData={listeningData}
                    youDidWellAt={youDidWellAtListening}
                    youCanImproveOn={youCanImproveOnListening}
                  />
                ) : null}
                {respondingData ? (
                  <RespondingComponent
                    respondingData={respondingData}
                    youDidWellAt={youDidWellAtResponding}
                    youCanImproveOn={youCanImproveOnResponding}
                  />
                ) : null}
              </div>
            ) : null}
          </div>
        </>
      )}
    </>
  );
};

export default AIDashboard;
