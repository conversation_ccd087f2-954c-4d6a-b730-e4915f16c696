import { useState } from "react";
import { Question } from "../hooks/useQuestion";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";

interface QuestionCardProps {
  q: Question;
  disable?: boolean;
  onSelect: (q: Question, selectedOption: string) => void;
}
export default function QuestionCard({
  q,
  onSelect,
  disable = false,
}: QuestionCardProps) {
  const [selectedOptions, setSelectedOptions] = useState<string>("");
  const { index, result, id, options, question } = q;
  const handleSelect = (option: string) => {
    setSelectedOptions(option);
    onSelect(q, option);
  };

  return (
    <div className="flex flex-col gap-4">
      <h1 className="flex items-center gap-2 text-xl font-semibold text-white">
        Question {index}
        {result === true ? (
          <CheckCircleIcon style={{ color: "lightgreen" }} />
        ) : (
          <></>
        )}
        {result === false ? (
          <CancelIcon style={{ color: "lightcoral" }} />
        ) : (
          <></>
        )}
      </h1>
      <h1 className="text-xl font-normal text-white">{question}</h1>
      <>
        {options.map((option, index) => (
          <div
            key={`option-number-${index}`}
            className="flex items-center gap-1"
          >
            <input
              disabled={disable}
              type="checkbox"
              className="size-5 w-1/12 rounded border-4 border-black text-left accent-white"
              checked={selectedOptions.includes(option)}
              onChange={() => handleSelect(option)}
            />
            <label
              className={
                "w-11/12 text-white " +
                (q.right_option && option == q.right_option
                  ? "font-bold text-yellow-300"
                  : "")
              }
            >
              {option}
            </label>
          </div>
        ))}
      </>
    </div>
  );
}
