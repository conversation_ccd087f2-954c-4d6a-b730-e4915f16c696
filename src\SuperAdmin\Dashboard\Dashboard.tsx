import DashboardCard from "./components/DashboardCard";
import { DashboardItems } from "./constants/consts";

function Dashboard() {
  return (
    <div className="grid grid-cols-2 gap-4">
      {DashboardItems.map((item, index) => (
        <DashboardCard
          key={index}
          title={item.title}
          icon={item.icon}
          onClick={item.onClick}
        />
      ))}
    </div>
  );
}

export default Dashboard;
