import { Card } from "./ui/card";
import { Badge } from "./ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Composed<PERSON>hart,
  <PERSON><PERSON>hart,
  Line,
  Legend,
} from "recharts";
import { GraduationCap, CheckCircle, Clock, TrendingUp } from "lucide-react";
import useCohortSummaries from "../hooks/useCohortSummaries";
import useActivitySummaries from "../hooks/useActivitySummaries";
import useActivityResults from "../hooks/useActivityResults";

interface ActivityViewProps {
  selectedCourse: string;
  onCourseChange: (course: string) => void;
  selectedTimePeriod: string;
}

export function ActivityView({
  selectedCourse,
  onCourseChange,
  selectedTimePeriod,
}: ActivityViewProps) {
  // Helper function to ensure valid numeric values
  const ensureValidNumber = (value: any): number => {
    const num = Number(value);
    return isNaN(num) || !isFinite(num) ? 0 : num;
  };
  const { data: mockCohortSummaries, isLoading } = useCohortSummaries();
  const { data: mockActivitySummaries, isLoading: isActivityLoading } =
    useActivitySummaries();
  const { data: mockActivityResults, isLoading: isResultsLoading } =
    useActivityResults();

  if (isLoading || isActivityLoading || isResultsLoading) {
    return <div>Loading...</div>;
  }

  // Filter data based on selected course
  const filteredActivitySummaries = mockActivitySummaries.filter(
    (activity) => activity.course === selectedCourse,
  );
  const filteredActivityResults = mockActivityResults.filter(
    (result) => result.course === selectedCourse,
  );
  const filteredCohortSummaries = mockCohortSummaries.filter(
    (cohort) => cohort.course === selectedCourse,
  );

  const courses = [...new Set(mockCohortSummaries.map((c) => c.course))];

  // Prepare cohort score overview data
  const prepareCohortScoreData = () => {
    const cohortData = filteredCohortSummaries.map((cohort) => {
      const cohortEntry: any = {
        name: cohort.cohort,
      };

      // Add activity scores (normalized to 0-10 scale)
      Object.entries(cohort.activities).forEach(([activityKey, activity]) => {
        cohortEntry[activityKey] = ensureValidNumber(activity.avg) / 10; // Convert percentage to 0-10 scale
      });

      return cohortEntry;
    });

    return cohortData;
  };

  const cohortScoreData = prepareCohortScoreData();

  // Get all unique activities for the selected course to determine colors
  const allActivities = [
    ...new Set(
      filteredCohortSummaries.flatMap((cohort) =>
        Object.keys(cohort.activities),
      ),
    ),
  ];

  // Define colors matching the image
  const activityColors: { [key: string]: string } = {
    "AI Activity": "#FF7A7A", // Orange/salmon for AI1
    "AI Activity 1": "#FF7A7A", // Orange/salmon for AI1
    "AI Activity 2": "#FFB366", // Lighter orange for AI2
    Quiz: "#F4D03F", // Yellow/beige for Quiz
    "Quiz 1": "#F4D03F", // Yellow/beige for Quiz 1
    "Quiz 2": "#F4D03F", // Yellow/beige for Quiz 2
    "Vira Activity": "#5D6D7E", // Dark gray for Vira
  };

  // Chart colors for weekly progress
  const progressColors = [
    "#3B82F6",
    "#10B981",
    "#F59E0B",
    "#EF4444",
    "#8B5CF6",
  ];

  // Prepare completion rate data for visualization
  const prepareCompletionRateData = () => {
    return filteredActivitySummaries.map((activity) => {
      const completionRate = ensureValidNumber(activity.completionRate);
      const activityName = `${activity.activityType}${activity.activityNumber > 1 ? ` ${activity.activityNumber}` : ""}`;

      return {
        name: activityName,
        fullName: activityName,
        completionRate: completionRate,
        totalAttempts: activity.totalAttempts,
      };
    });
  };

  const completionRateData = prepareCompletionRateData();

  // Generate weekly progress data for activities
  const generateWeeklyProgressData = () => {
    // Generate data for 13 weeks (approximately 3 months)
    const weeks = Array.from({ length: 13 }, (_, i) => `Week ${i + 1}`);

    return weeks.map((week, weekIndex) => {
      const weekData: any = { week };

      // For each activity in the selected course, generate progressive improvement
      filteredActivitySummaries.forEach((activity, activityIndex) => {
        const activityName = `${activity.activityType}${activity.activityNumber > 1 ? ` ${activity.activityNumber}` : ""}`;

        // Start with a base score that varies by activity type and course
        let baseScore = 60;
        if (selectedCourse === "Course 1") baseScore = 70;
        else if (selectedCourse === "Course 2") baseScore = 55;
        else if (selectedCourse === "Course 3") baseScore = 58;

        // Add activity-specific variation
        if (activity.activityType === "Quiz") baseScore += 5;
        else if (activity.activityType === "Vira Activity") baseScore += 3;
        else if (activity.activityType === "AI Activity") baseScore -= 2;

        // Progressive improvement over weeks with some variance
        const weeklyImprovement = 1.8; // 1.8% improvement per week on average
        const randomVariance = (Math.random() - 0.5) * 6; // ±3% random variation
        const seasonalEffect = Math.sin((weekIndex / 13) * Math.PI) * 3; // Slight seasonal variation

        const score = Math.min(
          95,
          Math.max(
            35,
            baseScore +
              weekIndex * weeklyImprovement +
              randomVariance +
              seasonalEffect,
          ),
        );

        weekData[activityName] = Math.round(score * 10) / 10;
      });

      return weekData;
    });
  };

  const weeklyProgressData = generateWeeklyProgressData();

  // Generate bell curve data for activities
  const generateActivityBellCurve = (
    activityType: string,
    activityNumber?: number,
  ) => {
    let activityScores = filteredActivityResults
      .filter((r) => r.activityType === activityType)
      .map((r) => r.score);

    if (activityNumber) {
      activityScores = filteredActivityResults
        .filter(
          (r) =>
            r.activityType === activityType &&
            r.activityNumber === activityNumber,
        )
        .map((r) => r.score);
    }

    if (activityScores.length === 0) return [];

    const mean =
      activityScores.reduce((sum, score) => sum + score, 0) /
      activityScores.length;
    const variance =
      activityScores.reduce(
        (sum, score) => sum + Math.pow(score - mean, 2),
        0,
      ) / activityScores.length;
    const stdDev = Math.sqrt(variance);

    // Create histogram data
    const histogramData = [];
    for (let i = 0; i <= 100; i += 10) {
      const count = activityScores.filter(
        (score) => score >= i && score < i + 10,
      ).length;
      const normalValue =
        (1 / (stdDev * Math.sqrt(2 * Math.PI))) *
        Math.exp(-0.5 * Math.pow((i + 5 - mean) / stdDev, 2));

      histogramData.push({
        range: `${i}-${i + 9}`,
        actualCount: count,
        normalCurve: normalValue * activityScores.length * 10, // Scale to match data
        midPoint: i + 5,
      });
    }

    return histogramData;
  };

  // Prepare safe chart data with validation
  const chartData = filteredActivitySummaries.map((a) => ({
    name: `${a.activityType}${a.activityNumber > 1 ? ` ${a.activityNumber}` : ""}`,
    averageScore: ensureValidNumber(a.averageScore),
    course: a.course,
  }));

  return (
    <div className="space-y-6">
      {/* Course Selection */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <GraduationCap className="text-primary h-5 w-5" />
            <h3>Select Course</h3>
          </div>
          <select
            value={selectedCourse}
            onChange={(e) => onCourseChange(e.target.value)}
            className="bg-background min-w-[200px] rounded-md border px-4 py-2"
          >
            {courses.map((course) => (
              <option key={course} value={course}>
                {course}
              </option>
            ))}
          </select>
        </div>
      </Card>

      {/* Top Row: Activity Performance + Completion Rates */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Activity Performance Comparison */}
        <Card className="p-6">
          <h3 className="mb-4">Activity Performance Comparison</h3>
          {chartData.length > 0 ? (
            <ResponsiveContainer width="100%" height={350}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  tick={{ fontSize: 12 }}
                />
                <YAxis
                  domain={[0, 100]}
                  tickFormatter={(value) => `${value}%`}
                />
                <Tooltip
                  formatter={(value) => [
                    `${ensureValidNumber(value).toFixed(1)}%`,
                    "Average Score",
                  ]}
                />
                <Bar
                  dataKey="averageScore"
                  fill="#8884d8"
                  name="Average Score %"
                />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="text-muted-foreground flex h-[350px] items-center justify-center">
              No activity data available for {selectedCourse}
            </div>
          )}
        </Card>

        {/* Activity Completion Rates */}
        <Card className="p-6">
          <h3 className="mb-4 flex items-center space-x-2">
            <CheckCircle className="text-primary h-5 w-5" />
            <span>Activity Completion Rates</span>
          </h3>
          {completionRateData.length > 0 ? (
            <ResponsiveContainer width="100%" height={350}>
              <BarChart
                data={completionRateData}
                margin={{ top: 20, right: 30, left: 20, bottom: 80 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  tick={{ fontSize: 12 }}
                />
                <YAxis
                  domain={[0, 100]}
                  tickFormatter={(value) => `${value}%`}
                  tick={{ fontSize: 12 }}
                />
                <Tooltip
                  formatter={(value: any, name: any, props: any) => [
                    `${value}%`,
                    `Completion Rate (${props.payload?.totalAttempts || 0} attempts)`,
                  ]}
                  labelStyle={{ color: "#333", fontWeight: "bold" }}
                  contentStyle={{
                    backgroundColor: "white",
                    border: "1px solid #ccc",
                    borderRadius: "6px",
                    fontSize: "14px",
                  }}
                />
                <Bar
                  dataKey="completionRate"
                  fill="#10B981"
                  radius={[4, 4, 0, 0]}
                  minPointSize={8}
                />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="text-muted-foreground flex h-[350px] items-center justify-center">
              <div className="text-center">
                <Clock className="mx-auto mb-2 h-12 w-12 opacity-50" />
                <div>No completion data available</div>
                <div className="mt-1 text-xs">Select a different course</div>
              </div>
            </div>
          )}
        </Card>
      </div>

      {/* Bottom Row: Weekly Progress + Cohort Score Overview */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Weekly Progress */}
        <Card className="p-6">
          <h3 className="mb-4 flex items-center space-x-2">
            <TrendingUp className="text-primary h-5 w-5" />
            <span>Weekly Progress</span>
          </h3>
          {weeklyProgressData.length > 0 &&
          filteredActivitySummaries.length > 0 ? (
            <ResponsiveContainer width="100%" height={350}>
              <LineChart
                data={weeklyProgressData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis dataKey="week" tick={{ fontSize: 12 }} />
                <YAxis
                  domain={[30, 100]}
                  tickFormatter={(value) => `${value}%`}
                  tick={{ fontSize: 12 }}
                />
                <Tooltip
                  formatter={(value: any, name: any) => [`${value}%`, name]}
                  labelStyle={{ color: "#333", fontWeight: "bold" }}
                  contentStyle={{
                    backgroundColor: "white",
                    border: "1px solid #ccc",
                    borderRadius: "6px",
                    fontSize: "14px",
                  }}
                />
                <Legend />
                {filteredActivitySummaries.map((activity, index) => {
                  const activityName = `${activity.activityType}${activity.activityNumber > 1 ? ` ${activity.activityNumber}` : ""}`;
                  return (
                    <Line
                      key={activityName}
                      type="monotone"
                      dataKey={activityName}
                      stroke={progressColors[index % progressColors.length]}
                      strokeWidth={3}
                      dot={{ r: 4, strokeWidth: 2 }}
                      activeDot={{ r: 6, strokeWidth: 2 }}
                    />
                  );
                })}
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="text-muted-foreground flex h-[350px] items-center justify-center">
              <div className="text-center">
                <TrendingUp className="mx-auto mb-2 h-12 w-12 opacity-50" />
                <div>No weekly progress data available</div>
                <div className="mt-1 text-xs">Select a different course</div>
              </div>
            </div>
          )}
        </Card>

        {/* Cohort Score Overview */}
        <Card className="p-6">
          <div className="space-y-4">
            <div>
              <h3 className="mb-2">Cohort Score Overview</h3>
              <p className="text-muted-foreground mb-4 text-sm">
                How do different cohorts compare with each other in the
                activities in this course?
              </p>
            </div>

            {cohortScoreData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={cohortScoreData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis
                    domain={[0, 10]}
                    label={{
                      value: "Average Score",
                      angle: -90,
                      position: "insideLeft",
                    }}
                  />
                  <Tooltip
                    formatter={(value, name) => [
                      `${Number(value).toFixed(1)}`,
                      name,
                    ]}
                  />
                  {allActivities.map((activity, index) => (
                    <Bar
                      key={activity}
                      dataKey={activity}
                      fill={activityColors[activity] || "#8884d8"}
                      name={activity}
                    />
                  ))}
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-muted-foreground flex h-[300px] items-center justify-center">
                No cohort data available for {selectedCourse}
              </div>
            )}

            {/* Legend */}
            <div className="flex flex-wrap justify-center gap-4">
              {allActivities.map((activity) => (
                <div key={activity} className="flex items-center space-x-2">
                  <div
                    className="h-4 w-4 rounded"
                    style={{
                      backgroundColor: activityColors[activity] || "#8884d8",
                    }}
                  />
                  <span className="text-muted-foreground text-sm">
                    {activity
                      .replace("Activity ", "")
                      .replace("Activity", "AI")}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Activity Summary Cards */}
      <Card className="p-6">
        <div className="mb-4">
          <h3>Activity Details</h3>
          <p className="text-muted-foreground text-sm">
            Showing all {filteredActivitySummaries.length} activities for{" "}
            {selectedCourse}
          </p>
        </div>
        {filteredActivitySummaries.length > 0 ? (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
            {filteredActivitySummaries.map((activity, index) => (
              <Card
                key={`${activity.activityType}-${activity.course}-${activity.activityNumber}-${index}`}
                className="border-2 p-4"
              >
                <div className="flex flex-col space-y-2">
                  <h4 className="text-base">
                    {activity.activityType}
                    {activity.activityNumber > 1 &&
                      ` ${activity.activityNumber}`}
                  </h4>
                  <Badge variant="outline" className="w-fit text-xs">
                    {activity.course}
                  </Badge>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground text-xs">
                        Attempts:
                      </span>
                      <div className="font-semibold">
                        {ensureValidNumber(activity.totalAttempts)}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground text-xs">
                        Avg Score:
                      </span>
                      <div className="font-semibold">
                        {ensureValidNumber(activity.averageScore).toFixed(1)}%
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground text-xs">
                        Avg Time:
                      </span>
                      <div className="font-semibold">
                        {ensureValidNumber(activity.averageTimeSpent).toFixed(
                          0,
                        )}
                        m
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground text-xs">
                        Completion:
                      </span>
                      <div className="font-semibold text-green-600">
                        {ensureValidNumber(activity.completionRate).toFixed(1)}%
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-muted-foreground py-8 text-center">
            No activity data available for {selectedCourse}
          </div>
        )}
      </Card>

      {/* Bell Curve Analysis for Each Activity */}
      {filteredActivitySummaries.length > 0 && (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {filteredActivitySummaries.map((activity, index) => {
            const bellCurveData = generateActivityBellCurve(
              activity.activityType,
              activity.activityNumber,
            );
            const activityScores = filteredActivityResults
              .filter(
                (r) =>
                  r.activityType === activity.activityType &&
                  r.activityNumber === activity.activityNumber,
              )
              .map((r) => r.score);
            const mean =
              activityScores.length > 0
                ? activityScores.reduce((sum, score) => sum + score, 0) /
                  activityScores.length
                : 0;
            const stdDev =
              activityScores.length > 0
                ? Math.sqrt(
                    activityScores.reduce(
                      (sum, score) => sum + Math.pow(score - mean, 2),
                      0,
                    ) / activityScores.length,
                  )
                : 0;

            return (
              <Card
                key={`${activity.activityType}-${activity.course}-${activity.activityNumber}-${index}`}
                className="p-6"
              >
                <div className="space-y-4">
                  <div>
                    <h3>
                      {activity.activityType}
                      {activity.activityNumber > 1 &&
                        ` ${activity.activityNumber}`}{" "}
                      - Score Distribution
                    </h3>
                    <Badge variant="outline" className="mb-2">
                      {activity.course}
                    </Badge>
                    <div className="text-muted-foreground text-sm">
                      Mean: {ensureValidNumber(mean).toFixed(1)}% | Std Dev:{" "}
                      {ensureValidNumber(stdDev).toFixed(1)} | Normal
                      Distribution Overlay
                    </div>
                  </div>

                  {bellCurveData.length > 0 ? (
                    <ResponsiveContainer width="100%" height={250}>
                      <ComposedChart data={bellCurveData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="range" />
                        <YAxis />
                        <Tooltip
                          formatter={(value, name) => [
                            Number(value).toFixed(1),
                            name === "actualCount"
                              ? "Actual Students"
                              : "Normal Distribution",
                          ]}
                        />
                        <Bar
                          dataKey="actualCount"
                          fill="#8884d8"
                          opacity={0.7}
                          name="Actual Students"
                        />
                      </ComposedChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="text-muted-foreground flex h-[250px] items-center justify-center">
                      No data available for distribution analysis
                    </div>
                  )}
                </div>
              </Card>
            );
          })}
        </div>
      )}

      {/* Detailed Activity Results Table */}
      <Card className="p-6">
        <h3 className="mb-4">Individual Activity Results</h3>
        <div className="overflow-x-auto">
          {filteredActivityResults.length > 0 ? (
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="p-2 text-left">Student</th>
                  <th className="p-2 text-left">Cohort</th>
                  <th className="p-2 text-left">Course</th>
                  <th className="p-2 text-left">Activity</th>
                  <th className="p-2 text-left">Score</th>
                  <th className="p-2 text-left">Time Spent</th>
                  <th className="p-2 text-left">Completed</th>
                </tr>
              </thead>
              <tbody>
                {filteredActivityResults.map((result, index) => (
                  <tr
                    key={`${result.studentId}-${result.activityType}-${result.activityNumber}-${index}`}
                    className="border-b"
                  >
                    <td className="p-2">{result.studentName}</td>
                    <td className="p-2">{result.cohort}</td>
                    <td className="p-2">
                      <Badge variant="outline">{result.course}</Badge>
                    </td>
                    <td className="p-2">
                      <Badge variant="secondary">
                        {result.activityType}
                        {result.activityNumber > 1 &&
                          ` ${result.activityNumber}`}
                      </Badge>
                    </td>
                    <td className="p-2">
                      <Badge
                        variant={
                          result.score >= 90
                            ? "default"
                            : result.score >= 80
                              ? "secondary"
                              : "outline"
                        }
                      >
                        {result.score}/{result.maxScore}
                      </Badge>
                    </td>
                    <td className="p-2">{result.timeSpent}m</td>
                    <td className="p-2">{result.completedAt}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-muted-foreground py-8 text-center">
              No individual results available for {selectedCourse}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
