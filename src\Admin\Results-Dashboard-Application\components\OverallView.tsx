import { Download, GraduationCap } from "lucide-react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import useCohortSummaries from "../hooks/useCohortSummaries";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { Card } from "./ui/card";

const COLORS = ["#81C784", "#FFB74D"]; // Green for completed, Orange for pending

interface OverallViewProps {
  selectedCourse: string;
  onCourseChange: (course: string) => void;
  selectedTimePeriod: string;
}

export function OverallView({
  selectedCourse,
  onCourseChange,
  selectedTimePeriod,
}: OverallViewProps) {
  // Helper function to ensure valid numeric values
  const ensureValidNumber = (value: any): number => {
    const num = Number(value);
    return isNaN(num) || !isFinite(num) ? 0 : num;
  };

  const { data: cohortSummaries, isLoading } = useCohortSummaries();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Filter data based on selected course
  const filteredCohortSummaries = cohortSummaries.filter(
    (cohort) => cohort.course === selectedCourse,
  );

  const courses = [...new Set(cohortSummaries.map((c) => c.course))];

  // Prepare chart data for vertical bar chart - simplified and robust
  const prepareChartData = () => {
    console.log("=== Chart Data Debug ===");
    console.log("Selected course:", selectedCourse);
    console.log("Filtered cohort summaries:", filteredCohortSummaries);

    if (filteredCohortSummaries.length === 0) {
      console.log("No cohort summaries found");
      return [];
    }

    const chartData = filteredCohortSummaries
      .map((cohort, index) => {
        const score = ensureValidNumber(cohort.averageScore);
        const dataPoint = {
          cohort: cohort.cohort,
          averageScore: score,
          totalStudents: ensureValidNumber(cohort.totalStudents),
          completionRate: ensureValidNumber(cohort.completionRate),
        };
        console.log(`Cohort ${index + 1}:`, dataPoint);
        return dataPoint;
      })
      .filter((item) => item.averageScore > 0) // Remove invalid entries
      .sort((a, b) => b.averageScore - a.averageScore); // Sort by score descending

    console.log("Final chart data:", chartData);
    console.log("Chart data length:", chartData.length);
    console.log("=== End Debug ===");

    return chartData;
  };

  const chartData = prepareChartData();

  // Prepare weekly progress data
  const prepareWeeklyProgressData = () => {
    if (filteredCohortSummaries.length === 0) {
      return [];
    }

    const weeklyData = [];
    for (let week = 1; week <= 13; week++) {
      let progress = 0;
      if (week <= 3) {
        progress = week * 3; // Slow start: 3%, 6%, 9%
      } else if (week <= 6) {
        progress = 9 + (week - 3) * 5; // 14%, 19%, 24%
      } else if (week <= 10) {
        progress = 24 + (week - 6) * 8; // 32%, 40%, 48%, 56%
      } else {
        progress = 56 + (week - 10) * 14; // 70%, 84%, 98%
      }

      progress = Math.min(progress, 100);

      weeklyData.push({
        week,
        "Cohort Progress": progress,
      });
    }

    return weeklyData;
  };

  const weeklyProgressData = prepareWeeklyProgressData();

  // Prepare cohort completion rates chart data
  const prepareCohortCompletionData = () => {
    if (filteredCohortSummaries.length === 0) {
      return [];
    }

    return filteredCohortSummaries
      .map((cohort) => ({
        cohort: cohort.cohort,
        completionRate: ensureValidNumber(cohort.completionRate),
        totalStudents: ensureValidNumber(cohort.totalStudents),
        averageScore: ensureValidNumber(cohort.averageScore),
      }))
      .sort((a, b) => b.completionRate - a.completionRate); // Sort by completion rate descending
  };

  const cohortCompletionData = prepareCohortCompletionData();

  // Download function for cohort results
  const handleDownload = () => {
    const csvContent = [
      [
        "Cohort",
        "Course",
        "Students",
        "Avg Score (%)",
        "Completion Rate (%)",
        "Activities",
      ],
      ...filteredCohortSummaries.map((cohort) => [
        cohort.cohort || "Unknown",
        cohort.course || selectedCourse,
        ensureValidNumber(cohort.totalStudents).toString(),
        ensureValidNumber(cohort.averageScore).toFixed(1),
        ensureValidNumber(cohort.completionRate).toFixed(1),
        cohort.activities
          ? Object.entries(cohort.activities)
              .map(
                ([key, activity]) =>
                  `${key}: ${ensureValidNumber(activity.avg).toFixed(1)}% (${ensureValidNumber(activity.completed)})`,
              )
              .join("; ")
          : "No activities",
      ]),
    ]
      .map((row) => row.map((cell) => `"${cell}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `cohort-results-${selectedCourse.toLowerCase().replace(" ", "-")}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Calculate safe summary statistics
  const totalCohorts = filteredCohortSummaries.length;
  const totalStudents = filteredCohortSummaries.reduce(
    (sum, cohort) => sum + ensureValidNumber(cohort.totalStudents),
    0,
  );
  const averageScore =
    totalCohorts > 0
      ? filteredCohortSummaries.reduce(
          (sum, cohort) => sum + ensureValidNumber(cohort.averageScore),
          0,
        ) / totalCohorts
      : 0;
  const averageCompletionRate =
    totalCohorts > 0
      ? filteredCohortSummaries.reduce(
          (sum, cohort) => sum + ensureValidNumber(cohort.completionRate),
          0,
        ) / totalCohorts
      : 0;

  return (
    <div className="space-y-6">
      {/* Course Selection */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <GraduationCap className="text-primary h-5 w-5" />
            <h3>Select Course</h3>
          </div>
          <select
            value={selectedCourse}
            onChange={(e) => onCourseChange(e.target.value)}
            className="bg-background min-w-[200px] rounded-md border px-4 py-2"
          >
            {courses.map((course) => (
              <option key={course} value={course}>
                {course}
              </option>
            ))}
          </select>
        </div>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card className="p-6">
          <div className="flex flex-col">
            <span className="text-muted-foreground text-sm">Total Cohorts</span>
            <span className="text-2xl font-bold">{totalCohorts}</span>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex flex-col">
            <span className="text-muted-foreground text-sm">
              Total Students
            </span>
            <span className="text-2xl font-bold">{totalStudents}</span>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex flex-col">
            <span className="text-muted-foreground text-sm">Average Score</span>
            <span className="text-2xl font-bold">
              {averageScore.toFixed(1)}%
            </span>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex flex-col">
            <span className="text-muted-foreground text-sm">
              Avg Completion Rate
            </span>
            <span className="text-2xl font-bold">
              {averageCompletionRate.toFixed(1)}%
            </span>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Fixed Vertical Cohort Performance Chart */}
        <Card className="p-6">
          <h3 className="mb-4">Cohort Performance Overview</h3>
          {chartData.length > 0 ? (
            <div className="w-full">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={chartData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="cohort"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    interval={0}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis
                    domain={[0, 100]}
                    tickFormatter={(value) => `${value}%`}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip
                    formatter={(value: any, name: any) => [
                      `${Number(value).toFixed(1)}%`,
                      "Average Score",
                    ]}
                    labelFormatter={(label) => `Cohort: ${label}`}
                    contentStyle={{
                      backgroundColor: "white",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      fontSize: "14px",
                    }}
                  />
                  <Bar
                    dataKey="averageScore"
                    fill="#3B82F6"
                    name="Average Score"
                    radius={[4, 4, 0, 0]}
                    minPointSize={5}
                  />
                </BarChart>
              </ResponsiveContainer>

              {/* Status information */}
              <div className="text-muted-foreground mt-2 text-xs">
                Showing {chartData.length} cohorts for {selectedCourse}
              </div>
            </div>
          ) : (
            <div className="text-muted-foreground flex h-[300px] flex-col items-center justify-center">
              <div>No cohort data available for {selectedCourse}</div>
              <div className="mt-2 text-xs">
                Available courses: {courses.join(", ")}
              </div>
            </div>
          )}
        </Card>

        {/* Cohort Completion Rates Chart */}
        <Card className="p-6">
          <h3 className="mb-4">Cohort Completion Rates</h3>
          {cohortCompletionData.length > 0 ? (
            <div className="w-full">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={cohortCompletionData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="cohort"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    interval={0}
                    tick={{ fontSize: 12 }}
                    axisLine={{ stroke: "#e0e0e0" }}
                    tickLine={{ stroke: "#e0e0e0" }}
                  />
                  <YAxis
                    domain={[0, 100]}
                    tickFormatter={(value) => `${value}%`}
                    tick={{ fontSize: 12 }}
                    axisLine={{ stroke: "#e0e0e0" }}
                    tickLine={{ stroke: "#e0e0e0" }}
                    label={{
                      value: "Completion %",
                      angle: -90,
                      position: "insideLeft",
                      style: {
                        textAnchor: "middle",
                        fontSize: "12px",
                        fill: "#666",
                      },
                    }}
                  />
                  <Tooltip
                    formatter={(value: any, name: any) => [
                      `${Number(value).toFixed(1)}%`,
                      "Completion Rate",
                    ]}
                    labelFormatter={(label) => `Cohort: ${label}`}
                    contentStyle={{
                      backgroundColor: "white",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      fontSize: "14px",
                    }}
                  />
                  <Bar
                    dataKey="completionRate"
                    fill="#10B981"
                    name="Completion Rate"
                    radius={[4, 4, 0, 0]}
                    minPointSize={5}
                  />
                </BarChart>
              </ResponsiveContainer>

              {/* Status information */}
              <div className="text-muted-foreground mt-2 text-xs">
                Completion rates across {cohortCompletionData.length} cohorts in{" "}
                {selectedCourse}
              </div>
            </div>
          ) : (
            <div className="text-muted-foreground flex h-[300px] flex-col items-center justify-center">
              <div>No completion data available for {selectedCourse}</div>
              <div className="mt-2 text-xs">
                Available courses: {courses.join(", ")}
              </div>
            </div>
          )}
        </Card>
      </div>

      {/* Weekly Progress Chart */}
      <Card className="p-6">
        <h3 className="mb-4">Weekly Progress</h3>
        {weeklyProgressData.length > 0 ? (
          <>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart
                data={weeklyProgressData}
                margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="week"
                  label={{
                    value: "Week",
                    position: "insideBottom",
                    offset: -5,
                  }}
                />
                <YAxis
                  domain={[0, 120]}
                  tickFormatter={(value) => `${value}%`}
                  label={{
                    value: "Progress",
                    angle: -90,
                    position: "insideLeft",
                  }}
                />
                <Tooltip
                  formatter={(value) => [
                    `${Number(value).toFixed(0)}%`,
                    "Progress",
                  ]}
                  labelFormatter={(label) => `Week ${label}`}
                />
                <Line
                  type="monotone"
                  dataKey="Cohort Progress"
                  stroke="#8B4513"
                  strokeWidth={3}
                  dot={{ fill: "#8B4513", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>

            {/* Legend */}
            <div className="mt-4 flex items-center justify-center">
              <div className="flex items-center space-x-2">
                <div
                  className="h-4 w-4 rounded-full"
                  style={{ backgroundColor: "#8B4513" }}
                ></div>
                <span className="text-muted-foreground text-sm">
                  Cohort Progress
                </span>
              </div>
            </div>
          </>
        ) : (
          <div className="text-muted-foreground flex h-[300px] items-center justify-center">
            No weekly progress data available for {selectedCourse}
          </div>
        )}
      </Card>

      {/* Detailed Cohort Table with Download and Percentages */}
      <Card className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <h3>Detailed Cohort Results</h3>
          <Button
            onClick={handleDownload}
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
            disabled={filteredCohortSummaries.length === 0}
          >
            <Download className="h-4 w-4" />
            <span>Download</span>
          </Button>
        </div>
        <div className="overflow-x-auto">
          {filteredCohortSummaries.length > 0 ? (
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="p-2 text-left">Cohort</th>
                  <th className="p-2 text-left">Course</th>
                  <th className="p-2 text-left">Students</th>
                  <th className="p-2 text-left">Avg Score (%)</th>
                  <th className="p-2 text-left">Completion Rate (%)</th>
                  <th className="p-2 text-left">Activities (%)</th>
                </tr>
              </thead>
              <tbody>
                {filteredCohortSummaries.map((cohort, index) => (
                  <tr
                    key={`${cohort.cohort}-${cohort.course}-${index}`}
                    className="border-b"
                  >
                    <td className="p-2">{cohort.cohort || "Unknown"}</td>
                    <td className="p-2">
                      <Badge variant="outline">
                        {cohort.course || selectedCourse}
                      </Badge>
                    </td>
                    <td className="p-2">
                      {ensureValidNumber(cohort.totalStudents)}
                    </td>
                    <td className="p-2">
                      <Badge
                        variant={
                          ensureValidNumber(cohort.averageScore) >= 90
                            ? "default"
                            : ensureValidNumber(cohort.averageScore) >= 80
                              ? "secondary"
                              : "outline"
                        }
                      >
                        {ensureValidNumber(cohort.averageScore).toFixed(1)}%
                      </Badge>
                    </td>
                    <td className="p-2">
                      <Badge
                        variant={
                          ensureValidNumber(cohort.completionRate) >= 90
                            ? "default"
                            : ensureValidNumber(cohort.completionRate) >= 70
                              ? "secondary"
                              : "outline"
                        }
                      >
                        {ensureValidNumber(cohort.completionRate).toFixed(1)}%
                      </Badge>
                    </td>
                    <td className="p-2">
                      <div className="flex flex-wrap gap-1">
                        {cohort.activities &&
                          Object.entries(cohort.activities).map(
                            ([activityKey, activity]) => (
                              <Badge
                                key={activityKey}
                                variant="secondary"
                                className="text-xs"
                              >
                                {activityKey}:{" "}
                                {ensureValidNumber(activity.avg).toFixed(1)}%
                              </Badge>
                            ),
                          )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-muted-foreground py-8 text-center">
              No cohort data available for {selectedCourse}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
