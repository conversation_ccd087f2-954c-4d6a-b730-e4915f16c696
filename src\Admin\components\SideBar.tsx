import { ReactNode, useState } from "react";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import { useAuth } from "../../Learner/context/AuthProvider";
import DashboardIcon from "../Icons/DashboardIcon";
import OverviewIcon from "../Icons/OverviewIcon";
import UserIcon from "../Icons/UserIcon";
import LogoutIcon from "../Icons/LogoutIcon";
import CourseEnrollmentIcon from "../Icons/CourseEnrollmentIcon";
import ResultsIcon from "../Icons/ResultsIcon";

interface sideBarItem {
  name: string;
  icon: ReactNode;
  children?: string[];
  selected?: boolean;
}

export const sideBarItems: sideBarItem[] = [
  {
    name: "Dashboard",
    icon: <DashboardIcon />,
  },
  {
    name: "Overview",
    icon: <OverviewIcon />,
  },
  {
    name: "Users",
    icon: <UserIcon />,
    children: ["User-Management", "User-Upload"],
  },
  {
    name: "Course-Enrollment",
    icon: <CourseEnrollmentIcon />,
  },
  {
    name: "Results",
    icon: <ResultsIcon />,
  },
  // {
  //   name: "Role-Selection",
  //   icon: (
  //     <svg
  //       width="17"
  //       height="17"
  //       viewBox="0 0 17 17"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         fillRule="evenodd"
  //         clipRule="evenodd"
  //         d="M5.12061 13.5C5.07886 13.748 5.05688 13.998 5.05566 14.25C5.05688 14.502 5.07886 14.752 5.12061 15H1.44434C1.06177 14.998 0.695068 14.8398 0.424316 14.5586C0.153809 14.2773 0.0012207 13.8984 0 13.5V3C0.0012207 2.60352 0.153809 2.22266 0.424316 1.94141C0.695068 1.66016 1.06177 1.50195 1.44434 1.5H4.46338C4.61133 1.0625 4.88672 0.681641 5.25146 0.414062C5.61646 0.144531 6.05273 0 6.5 0C6.94727 0 7.38354 0.144531 7.74854 0.414062C8.11328 0.681641 8.38867 1.0625 8.53662 1.5H11.5557C11.9382 1.50195 12.3049 1.66016 12.5757 1.94141C12.8462 2.22266 12.9988 2.60352 13 3V9.75H9.38892C8.72461 9.75 8.06958 9.91016 7.4751 10.2188C7.15283 10.1602 6.8269 10.1289 6.5 10.125C5.05566 10.125 2.16675 10.9492 2.16675 12.4492V13.5H5.12061ZM6.90137 1.62695C6.78247 1.54492 6.64282 1.5 6.5 1.5C6.30859 1.5 6.12524 1.58008 5.98999 1.7207C5.85474 1.86133 5.77832 2.05078 5.77783 2.25C5.77783 2.39844 5.82007 2.54297 5.89941 2.66602C5.97876 2.79102 6.09155 2.88672 6.22363 2.94336C6.35547 3 6.50073 3.01562 6.64087 2.98633C6.78101 2.95703 6.90967 2.88477 7.01074 2.78125C7.11157 2.67578 7.18042 2.54102 7.20825 2.39648C7.23633 2.25 7.22192 2.09961 7.16724 1.96289C7.11255 1.82617 7.02002 1.70898 6.90137 1.62695ZM7.70386 4.87891C7.34741 4.63281 6.92847 4.5 6.5 4.5C6.21533 4.5 5.93335 4.55859 5.67041 4.66992C5.40747 4.7832 5.16846 4.94922 4.96704 5.1582C4.76587 5.36719 4.6062 5.61523 4.49756 5.88867C4.38867 6.16211 4.33301 6.45508 4.33325 6.75C4.33325 7.19531 4.46045 7.63086 4.69849 8C4.93652 8.36914 5.2749 8.6582 5.6709 8.82812C5.88867 8.92188 6.11865 8.97852 6.35156 8.99414C6.54175 9.00781 6.73364 8.99609 6.92261 8.95703C7.34302 8.86914 7.729 8.65625 8.03198 8.3418C8.33496 8.02734 8.5415 7.625 8.625 7.18945C8.70874 6.75195 8.66577 6.30078 8.50171 5.88867C8.33765 5.47852 8.06006 5.12695 7.70386 4.87891Z"
  //         fill="#A3A3A3"
  //       />
  //       <path d="M13 13.5H10.1111V15H13V13.5Z" fill="#A3A3A3" />
  //       <path
  //         d="M9.85718 15.5H11.2856V17H9.85718C9.09937 17 8.37256 16.6836 7.83691 16.1211C7.30103 15.5586 7 14.7949 7 14C7 13.2051 7.30103 12.4414 7.83691 11.8789C8.18164 11.5176 8.60571 11.2578 9.06616 11.1191C9.32104 11.041 9.58716 11 9.85718 11H11.2856V12.5H9.85718C9.65894 12.5 9.46509 12.543 9.28687 12.625C9.12427 12.6992 8.97485 12.8047 8.84692 12.9395C8.5791 13.2207 8.42847 13.6016 8.42847 14C8.42847 14.3984 8.5791 14.7793 8.84692 15.0605C9.11499 15.3418 9.47827 15.5 9.85718 15.5Z"
  //         fill="#A3A3A3"
  //       />
  //       <path
  //         d="M16.7288 15.2754C16.9058 14.8809 17 14.4453 17 14C17 13.2051 16.699 12.4414 16.1631 11.8789C15.6274 11.3164 14.9006 11 14.1428 11H12.7144V12.5H14.1428C14.5217 12.5 14.885 12.6582 15.1531 12.9395C15.4209 13.2207 15.5715 13.6016 15.5715 14C15.5715 14.3984 15.4209 14.7793 15.1531 15.0605C14.885 15.3418 14.5217 15.5 14.1428 15.5H12.7144V17H14.1428C14.4458 17 14.7439 16.9492 15.0269 16.8516C15.4514 16.707 15.8416 16.459 16.1631 16.1211C16.3992 15.873 16.5896 15.5859 16.7288 15.2754Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  // {
  //   name: "System-Health-Monitoring",
  //   icon: (
  //     <svg
  //       width="14"
  //       height="14"
  //       viewBox="0 0 14 14"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         fillRule="evenodd"
  //         clipRule="evenodd"
  //         d="M6.09014 0.302877C6.42678 0.104744 6.81276 0 7.20625 0C7.59975 0 7.98573 0.104744 8.32237 0.302877L9.11495 0.774341C10.227 1.43537 11.4935 1.58256 12.7658 1.73089C13.1771 1.77895 13.5888 1.82662 13.9961 1.89208C13.9961 1.89208 14.0051 2.58603 13.9957 2.90085L13.9144 5.60666C13.8444 7.92727 12.7833 10.1276 10.9632 11.7266C10.1044 12.4807 9.21612 13.1622 8.19553 13.7282C7.89471 13.8936 7.55749 13.9863 7.2122 13.9986C6.8669 14.0109 6.52361 13.9425 6.21115 13.799C5.03258 13.2541 3.98903 12.5632 3.03614 11.7266C1.21596 10.1276 0.154902 7.92727 0.0852547 5.60666L0.00432326 2.90955C-0.00540408 2.5902 0.00432326 1.89208 0.00432326 1.89208C0.335053 1.85424 0.672008 1.82284 1.01169 1.79143C2.51087 1.65294 4.05985 1.50992 5.33063 0.754665L6.09014 0.302877ZM6.21815 6.05428V3.40561H7.77453V6.05428H10.4982V7.56781H7.77453V10.2165H6.21815V7.56781H3.49449V6.05428H6.21815Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  // {
  //   name: "Configuration-and-Settings",
  //   icon: (
  //     <svg
  //       width="17"
  //       height="17"
  //       viewBox="0 0 17 17"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M9.84534 17H6.75261C6.53419 17 6.35072 16.847 6.31578 16.643L5.99253 14.3905C5.44212 14.178 4.97035 13.889 4.51605 13.549L2.34065 14.4075C2.14844 14.4755 1.91255 14.4075 1.80772 14.2205L0.0604051 11.2795C0.00693347 11.1919 -0.0119172 11.0884 0.00737818 10.9883C0.0266736 10.8881 0.0827938 10.7983 0.165244 10.7355L2.00866 9.3245L1.9475 8.5L2.00866 7.65L0.165244 6.2645C0.0827938 6.20171 0.0266736 6.11186 0.00737818 6.01174C-0.0119172 5.91161 0.00693347 5.80808 0.0604051 5.7205L1.80772 2.7795C1.91255 2.5925 2.14844 2.516 2.34065 2.5925L4.51605 3.4425C4.97035 3.111 5.44212 2.822 5.99253 2.6095L6.31578 0.357C6.35072 0.153 6.53419 0 6.75261 0H10.2472C10.4656 0 10.6491 0.153 10.6841 0.357L11.0073 2.6095C11.5577 2.822 12.0295 3.111 12.4838 3.4425L14.6592 2.5925C14.8514 2.516 15.0873 2.5925 15.1921 2.7795L16.9394 5.7205C17.053 5.9075 17.0006 6.137 16.8346 6.2645L14.9912 7.65L15.0523 8.5V8.6615C14.6155 8.5595 14.1787 8.5 13.7418 8.5C13.5933 8.5 13.4448 8.5 13.305 8.5255C13.305 7.9985 13.2177 7.4715 13.0429 6.97L14.8863 5.6525L14.2311 4.5475L12.1256 5.4315C11.4391 4.65827 10.5079 4.12902 9.47841 3.927L9.15516 1.7H7.84467L7.52142 3.9185C6.47304 4.131 5.54696 4.675 4.87425 5.4315L2.76874 4.5475L2.1135 5.6525L3.95691 6.97C3.60745 7.96167 3.60745 9.03833 3.95691 10.03L2.10476 11.356L2.76 12.461L4.88298 11.577C5.5557 12.325 6.47304 12.869 7.51268 13.073L7.83594 15.3H8.8057C9.03285 15.9375 9.37357 16.507 9.84534 17ZM8.81443 11.866C8.70959 11.9 8.60475 11.9 8.49992 11.9C6.56914 11.9 5.00529 10.3785 5.00529 8.5C5.00529 6.6215 6.56914 5.1 8.49992 5.1C10.4307 5.1 11.9945 6.6215 11.9945 8.5C11.9945 8.602 11.9945 8.704 11.9596 8.806C11.2383 9.06306 10.5832 9.46999 10.0401 9.99845C9.4969 10.5269 9.07864 11.1642 8.81443 11.866ZM10.2472 8.5C10.2472 7.565 9.46967 6.8 8.49992 6.8C7.53016 6.8 6.75261 7.565 6.75261 8.5C6.75261 9.435 7.53889 10.2 8.49992 10.2C9.46094 10.2 10.2472 9.4435 10.2472 8.5ZM11.1209 11.05H16.3628V16.15H11.1209V11.05Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  // {
  //   name: "Data-Backup-and-Recovery",
  //   icon: (
  //     <svg
  //       width="17"
  //       height="16"
  //       viewBox="0 0 17 16"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M0 11.9357V14.9839C0 15.2534 0.108542 15.5119 0.301749 15.7024C0.494955 15.893 0.757 16 1.03024 16H15.4535C15.7268 16 15.9888 15.893 16.182 15.7024C16.3752 15.5119 16.4838 15.2534 16.4838 14.9839V11.9357H0ZM14.4233 14.4759H12.3628V13.4598H14.4233V14.4759Z"
  //         fill="#A3A3A3"
  //       />
  //       <path
  //         d="M5.66629 4.7826L7.72676 2.7911V8.88751C7.72676 9.02225 7.78104 9.15147 7.87764 9.24675C7.97424 9.34202 8.10526 9.39555 8.24188 9.39555C8.3785 9.39555 8.50952 9.34202 8.60613 9.24675C8.70273 9.15147 8.757 9.02225 8.757 8.88751V2.7911L9.51422 3.53283L10.0293 2.62345L8.24188 0.840253L4.95028 4.06119C4.87051 4.15801 4.82965 4.2804 4.83549 4.40502C4.84133 4.52965 4.89346 4.64779 4.98195 4.73695C5.07043 4.8261 5.18909 4.88003 5.3153 4.88844C5.4415 4.89686 5.56644 4.85919 5.66629 4.7826Z"
  //         fill="#A3A3A3"
  //       />
  //       <path
  //         d="M15.2372 7.56665H10.4208C10.2041 7.56265 9.98964 7.52138 9.78724 7.44472V8.88754C9.78724 9.29176 9.62442 9.67942 9.33461 9.96524C9.0448 10.2511 8.65174 10.4116 8.24188 10.4116C7.83203 10.4116 7.43896 10.2511 7.14915 9.96524C6.85934 9.67942 6.69653 9.29176 6.69653 8.88754V5.83933H2.61165C2.39677 5.83098 2.18498 5.89192 2.00837 6.01293C1.83176 6.13394 1.69996 6.30842 1.63292 6.50994C0.443001 9.74104 0.123628 10.6301 0.0360581 10.9197H16.4477C16.3756 10.6606 16.1129 9.93917 15.2372 7.56665Z"
  //         fill="#A3A3A3"
  //       />
  //       <path
  //         d="M12.8007 0.32207L9.8542 5.33129C9.78565 5.43052 9.7463 5.54654 9.7405 5.66647C9.73471 5.78641 9.7627 5.90559 9.82137 6.01081C9.88005 6.11602 9.96711 6.20316 10.0729 6.26256C10.1787 6.32196 10.2991 6.35131 10.4208 6.34735H16.3189C16.4406 6.35131 16.561 6.32196 16.6668 6.26256C16.7726 6.20316 16.8597 6.11602 16.9184 6.01081C16.9771 5.90559 17.005 5.78641 16.9993 5.66647C16.9935 5.54654 16.9541 5.43052 16.8856 5.33129L13.9391 0.32207C13.881 0.224048 13.7979 0.14274 13.698 0.0862355C13.5981 0.0297312 13.485 0 13.3699 0C13.2547 0 13.1416 0.0297312 13.0418 0.0862355C12.9419 0.14274 12.8588 0.224048 12.8007 0.32207Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  // {
  //   name: "Security-Management",
  //   icon: (
  //     <svg
  //       width="14"
  //       height="17"
  //       viewBox="0 0 14 17"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M7 15.2785C8.41458 14.8481 9.59583 13.9982 10.5437 12.7289C11.4917 11.4596 12.0458 10.0428 12.2062 8.47848H7V1.7L1.75 3.63671V8.09114C1.75 8.19156 1.76458 8.32068 1.79375 8.47848H7V15.2785ZM7 17C6.89792 17 6.80312 16.9928 6.71562 16.9785C6.62812 16.9641 6.54062 16.9426 6.45312 16.9139C4.48437 16.2684 2.91667 15.0742 1.75 13.3314C0.583333 11.5887 0 9.71281 0 7.7038V3.63671C0 3.27806 0.105875 2.95527 0.317625 2.66835C0.529375 2.38143 0.802667 2.17342 1.1375 2.0443L6.3875 0.107595C6.59167 0.035865 6.79583 0 7 0C7.20417 0 7.40833 0.035865 7.6125 0.107595L12.8625 2.0443C13.1979 2.17342 13.4715 2.38143 13.6832 2.66835C13.895 2.95527 14.0006 3.27806 14 3.63671V7.7038C14 9.71224 13.4167 11.5881 12.25 13.3314C11.0833 15.0748 9.51562 16.2689 7.54687 16.9139C7.45937 16.9426 7.37187 16.9641 7.28437 16.9785C7.19687 16.9928 7.10208 17 7 17Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  // {
  //   name: "Report-Generation-and-Analytics",
  //   icon: (
  //     <svg
  //       width="14"
  //       height="14"
  //       viewBox="0 0 14 14"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M12.4444 0H1.55556C0.7 0 0 0.7 0 1.55556V12.4444C0 13.3 0.7 14 1.55556 14H12.4444C13.3 14 14 13.3 14 12.4444V1.55556C14 0.7 13.3 0 12.4444 0ZM4.66667 10.8889H3.11111V5.44444H4.66667V10.8889ZM7.77778 10.8889H6.22222V3.11111H7.77778V10.8889ZM10.8889 10.8889H9.33333V7.77778H10.8889V10.8889Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  // {
  //   name: "Support-Ticket-Management",
  //   icon: (
  //     <svg
  //       width="14"
  //       height="14"
  //       viewBox="0 0 14 14"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M7 14C6.03166 14 5.12166 13.8161 4.27 13.4484C3.41833 13.0807 2.6775 12.582 2.0475 11.9525C1.4175 11.323 0.918867 10.5821 0.551601 9.73C0.184334 8.87786 0.000467552 7.96786 8.86075e-07 7C-0.00046578 6.03213 0.183401 5.12213 0.551601 4.27C0.9198 3.41787 1.41843 2.67703 2.0475 2.0475C2.67657 1.41797 3.4174 0.919333 4.27 0.5516C5.1226 0.183867 6.0326 0 7 0C7.9674 0 8.87739 0.183867 9.72999 0.5516C10.5826 0.919333 11.3234 1.41797 11.9525 2.0475C12.5816 2.67703 13.0804 3.41787 13.4491 4.27C13.8178 5.12213 14.0014 6.03213 14 7C13.9986 7.96786 13.8147 8.87786 13.4484 9.73C13.0821 10.5821 12.5834 11.323 11.9525 11.9525C11.3216 12.582 10.5807 13.0809 9.72999 13.4491C8.87926 13.8173 7.96926 14.0009 7 14ZM4.97 12.215L5.81 10.29C5.32 10.115 4.8972 9.84386 4.5416 9.4766C4.186 9.10933 3.9088 8.68046 3.71 8.19L1.785 8.995C2.05333 9.74166 2.4675 10.395 3.0275 10.955C3.5875 11.515 4.235 11.935 4.97 12.215ZM3.71 5.81C3.90833 5.32 4.18553 4.89137 4.5416 4.5241C4.89766 4.15683 5.32046 3.88547 5.81 3.71L5.005 1.785C4.25833 2.065 3.605 2.485 3.045 3.045C2.485 3.605 2.065 4.25833 1.785 5.005L3.71 5.81ZM7 9.1C7.58333 9.1 8.07916 8.89583 8.48749 8.4875C8.89583 8.07916 9.09999 7.58333 9.09999 7C9.09999 6.41667 8.89583 5.92083 8.48749 5.5125C8.07916 5.10417 7.58333 4.9 7 4.9C6.41666 4.9 5.92083 5.10417 5.5125 5.5125C5.10416 5.92083 4.9 6.41667 4.9 7C4.9 7.58333 5.10416 8.07916 5.5125 8.4875C5.92083 8.89583 6.41666 9.1 7 9.1ZM9.02999 12.215C9.76499 11.935 10.4097 11.518 10.9641 10.9641C11.5185 10.4102 11.9355 9.76546 12.215 9.03L10.29 8.19C10.115 8.68 9.84666 9.10303 9.48499 9.4591C9.12333 9.81516 8.70333 10.0921 8.22499 10.29L9.02999 12.215ZM10.29 5.775L12.215 4.97C11.935 4.235 11.518 3.59053 10.9641 3.0366C10.4102 2.48267 9.76546 2.06547 9.02999 1.785L8.22499 3.745C8.70333 3.92 9.11749 4.18553 9.46749 4.5416C9.81749 4.89767 10.0917 5.3088 10.29 5.775Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  // {
  //   name: "Course-Management",
  //   icon: (
  //     <svg
  //       width="14"
  //       height="14"
  //       viewBox="0 0 14 14"
  //       fill="none"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         d="M12.4444 0H1.55556C0.7 0 0 0.7 0 1.55556V12.4444C0 13.3 0.7 14 1.55556 14H12.4444C13.3 14 14 13.3 14 12.4444V1.55556C14 0.7 13.3 0 12.4444 0ZM4.66667 10.8889H3.11111V5.44444H4.66667V10.8889ZM7.77778 10.8889H6.22222V3.11111H7.77778V10.8889ZM10.8889 10.8889H9.33333V7.77778H10.8889V10.8889Z"
  //         fill="#A3A3A3"
  //       />
  //     </svg>
  //   ),
  // },
  {
    name: "Logout",
    icon: <LogoutIcon />,
  },
];

const handleLogout = (logout: () => void) => {
  Swal.fire({
    icon: "question",
    title: "Logging Out ...",
    text: "Are you sure?",
    showCancelButton: true,
    confirmButtonText: "Yes",
    cancelButtonText: "No",
  }).then((result) => {
    if (result.isConfirmed) {
      logout();
    }
  });
};

function SideBarChild({
  child,
  index,
  selectedIndex,
  onClickChild,
}: {
  child: string;
  index: number;
  selectedIndex: number;
  onClickChild: (childName: string) => void;
}) {
  return (
    <div
      className={`w-full cursor-pointer py-6 text-center text-lg font-normal ${index === selectedIndex ? "text-[#36537F] underline" : "text-[#A3A3A3]"}`}
      onClick={() => onClickChild(child)}
    >
      {child.replace(/-/g, " ")}
    </div>
  );
}

function SideBarItem({
  item,
  index,
  selectedIndex,
  collapseChild,
  onClick,
}: {
  item: sideBarItem;
  index: number;
  selectedIndex: number;
  collapseChild: boolean;
  onClick: () => void;
}) {
  const [selectedChild, setSelectedChild] = useState<number>(-1);
  const navigate = useNavigate();

  const handleChildClick = (childName: string) => {
    // Find the index of the child that was clicked
    const childIndex =
      item.children?.findIndex((child) => child === childName) ?? -1;
    setSelectedChild(childIndex);
    navigate("/admin/" + childName.toLowerCase());
  };

  return (
    <div className="w-full">
      <div
        className={`my-4 flex max-h-fit min-h-10 w-full cursor-pointer flex-col justify-center rounded-lg p-5 ${index === selectedIndex ? "bg-[#145DA0]" : ""}`}
        onClick={onClick}
      >
        <div className="flex flex-row items-center gap-4">
          {item.name === "Dashboard" ? (
            <DashboardIcon selected={index === selectedIndex} />
          ) : item.name === "Overview" ? (
            <OverviewIcon selected={index === selectedIndex} />
          ) : item.name === "Users" ? (
            <UserIcon selected={index === selectedIndex} />
          ) : item.name === "Course-Enrollment" ? (
            <CourseEnrollmentIcon selected={index === selectedIndex} />
          ) : item.name === "Results" ? (
            <ResultsIcon selected={index === selectedIndex} />
          ) : item.name === "Logout" ? (
            <LogoutIcon selected={index === selectedIndex} />
          ) : (
            item.icon
          )}
          <h1
            className={`text-lg font-normal ${index === selectedIndex ? "text-white" : "text-[#A3A3A3]"}`}
          >
            {item.name.replace(/-/g, " ")}
          </h1>
          {collapseChild && item.children && index === selectedIndex ? (
            <svg
              width="11"
              height="9"
              viewBox="0 0 11 9"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M5.5 9L10.2631 0.75H0.73686L5.5 9Z" fill="white" />
            </svg>
          ) : (
            <></>
          )}
        </div>
      </div>
      {collapseChild && index === selectedIndex ? (
        <div className="flex w-full flex-col items-center justify-start">
          {item.children?.map((child, index) => (
            <SideBarChild
              key={index}
              child={child}
              index={index}
              selectedIndex={selectedChild}
              onClickChild={handleChildClick}
            />
          ))}
        </div>
      ) : (
        <></>
      )}
    </div>
  );
}

function SideBar() {
  const [selectedItem, setSelectedItem] = useState<number>(-1);
  const [collapse, setCollapse] = useState<boolean>(true);
  const navigate = useNavigate();
  const { logout } = useAuth();

  return (
    <ul className="w-[20%] flex-shrink-0 divide-y-2 divide-[#E6DDB3] px-4">
      {sideBarItems.map((sideBarItem, index) => (
        <SideBarItem
          key={sideBarItem.name}
          item={sideBarItem}
          index={index}
          selectedIndex={selectedItem}
          onClick={() => {
            if (sideBarItem.children && selectedItem === index) {
              setCollapse(!collapse);
            } else {
              setCollapse(true);
              setSelectedItem(index);
            }
            if (sideBarItem.name !== "Logout") {
              if (sideBarItem.children) {
                // If it has children, just toggle collapse
              } else {
                navigate(
                  sideBarItem.name.includes("Portal")
                    ? "/admin"
                    : "/admin/" + sideBarItem.name.toLowerCase(),
                );
              }
            } else {
              handleLogout(logout);
            }
          }}
          collapseChild={collapse}
        />
      ))}
    </ul>
  );
}

export default SideBar;
