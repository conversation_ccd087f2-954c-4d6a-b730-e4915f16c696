import React from "react";

function TSVIcon() {
  return (
    <svg
      width="34"
      height="34"
      viewBox="0 0 34 34"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.983 0C7.599 0 0 7.616 0 17C0 26.384 7.599 34 16.983 34C26.384 34 34 26.384 34 17C34 7.616 26.384 0 16.983 0Z"
        fill="url(#paint0_linear_0_1)"
      />
      <path
        d="M17 27C14.6833 26.4167 12.7707 25.0873 11.262 23.012C9.75333 20.9367 8.99933 18.6327 9 16.1V10L17 7L25 10V16.1C25 18.6333 24.246 20.9377 22.738 23.013C21.23 25.0883 19.3173 26.4173 17 27ZM14 21H20V16H19V15C19 14.45 18.8043 13.9793 18.413 13.588C18.0217 13.1967 17.5507 13.0007 17 13C16.4493 12.9993 15.9787 13.1953 15.588 13.588C15.1973 13.9807 15.0013 14.4513 15 15V16H14V21ZM16 16V15C16 14.7167 16.096 14.4793 16.288 14.288C16.48 14.0967 16.7173 14.0007 17 14C17.2827 13.9993 17.5203 14.0953 17.713 14.288C17.9057 14.4807 18.0013 14.718 18 15V16H16Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_0_1"
          x1="-1.8999e-07"
          y1="17"
          x2="34"
          y2="17"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2B3D59" />
          <stop offset="1" stopColor="#375685" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default TSVIcon;
