import React from "react";

interface ProgressBarProps {
  progress: number; // Progress percentage passed from the parent
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progress }) => {
  return (
    <div className="fixed bottom-0 left-0 z-40 w-full border-t border-gray-300 bg-gray-100">
      <div className="flex flex-col px-4 py-2 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="mb-2 text-sm font-medium sm:mb-0 sm:text-base">
          Overall Progress
        </h1>

        {/* Progress Bar */}
        <div className="relative flex-grow sm:mx-4">
          <div className="h-2 rounded-full bg-gray-200">
            <div
              className="h-2 rounded-full bg-green-600"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Percentage Label */}
          <div
            className="absolute -top-3 rounded-full bg-gray-200 px-2 py-0.5 text-xs text-gray-700 sm:text-sm"
            style={{
              left: `calc(${progress}% - 16px)`,
              // Ensure label doesn't go off-screen on small devices
              maxWidth: "40px",
              textAlign: "center",
            }}
          >
            {parseInt(progress.toString())}%
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressBar;
