// src/components/ReportElement.tsx
import React from "react";
import styled from "@emotion/styled";

interface ReportElementProps {
  heading: string;
  text: any;
}

const Container = styled.div`
  padding: 1em;
`;

const Heading = styled.h3`
  margin: 0;
  font-size: 1.5em;
  color: #333;
`;

const Text = styled.p`
  margin: 0.5em 0 0;
  font-size: 1em;
  color: #666;
`;

const ReportElement: React.FC<ReportElementProps> = ({ heading, text }) => {
  return (
    <Container>
      <Heading>{heading}</Heading>
      <Text>{text}</Text>
    </Container>
  );
};

export default ReportElement;
