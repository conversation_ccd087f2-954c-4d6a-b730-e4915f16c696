import { ReactNode } from "react";
import Chat<PERSON><PERSON><PERSON><PERSON> from "./ChatWithJan";
import { useGetActivity } from "../hooks/useGetActivity";
import { useLocation } from "react-router-dom";
// import ProgressBar from "./ProgressBar";
// import { useGetCourseProgress } from "../hooks/useGetCourseProgress";
import "../components/ResponsiveStyles.css";

interface Props {
  children?: ReactNode;
  heading: string;
  icon?: ReactNode;
}

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

function ContentBox({ children, heading, icon }: Props) {
  const query = useQueryParam();
  const activity_id = query.get("activity_id") ?? "";
  const {
    data: activity,
    isLoading,
    isFetching,
    isError,
  } = useGetActivity(activity_id);
  // const { data: courses_progress } = useGetCourseProgress();

  return (
    <div className="learner-content flex h-fit w-full flex-col space-y-4 bg-white bg-opacity-10 px-4 md:space-y-9 md:px-[62px]">
      {/* Activity name or heading */}
      {activity?.name ? (
        <span className="flex items-center space-x-3 md:space-x-7">
          {icon ? (
            icon
          ) : (
            <img
              src="/practice-activity-logo.png"
              alt="Activity logo"
              className="h-8 w-8 md:h-auto md:w-auto"
            />
          )}

          <h1 className="break-words text-xl font-bold text-[#1c1c1c] md:text-3xl">
            {activity.name}
          </h1>
        </span>
      ) : (
        <span className="flex items-center space-x-3 md:space-x-7">
          {icon ? (
            icon
          ) : (
            <img
              src="/practice-activity-logo.png"
              alt="Activity logo"
              className="h-8 w-8 md:h-auto md:w-auto"
            />
          )}

          <h1 className="break-words text-xl font-bold text-[#1c1c1c] md:text-3xl">
            {heading}
          </h1>
        </span>
      )}

      {/* Main content area */}
      <div
        key={activity_id}
        className="learner-content-box learner-content-inner flex max-h-fit min-h-[85%] w-full flex-col space-y-6 overflow-x-hidden rounded-xl bg-[#1B5AA9] px-4 py-4 text-white md:w-[90%] md:space-y-14 md:px-14 md:py-8"
      >
        {children}
      </div>
    </div>
  );
}

export default ContentBox;
