import { Route, Routes, useLocation } from "react-router-dom";
import Layout from "./Layout";
import Dashboard from "./Dashboard/Dashboard";

function MainSuperAdminScreen() {
  return (
    <Layout
      heading={HeadingText()}
      children={
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          {/* Add more routes as needed */}
          {/* <Route path="/system-management" element={<SystemManagement />} /> */}
          {/* <Route path="/admin-management" element={<AdminManagement />} /> */}
          {/* <Route path="/instructor-management" element={<InstructorManagement />} /> */}
          {/* <Route path="/learner-management" element={<LearnerManagement />} /> */}
          {/* <Route path="/analytics" element={<Analytics />} /> */}
        </Routes>
      }
    />
  );
}

function HeadingText() {
  const location = useLocation();

  let headerText = "";
  switch (location.pathname) {
    case "/superadmin":
      headerText = "Super Admin Dashboard";
      break;
    case "/superadmin/dashboard":
      headerText = "Super Admin Dashboard";
      break;
    case "/superadmin/system-management":
      headerText = "System Management";
      break;
    case "/superadmin/admin-management":
      headerText = "Admin Management";
      break;
    case "/superadmin/instructor-management":
      headerText = "Instructor Management";
      break;
    case "/superadmin/learner-management":
      headerText = "Learner Management";
      break;
    case "/superadmin/analytics":
      headerText = "Analytics";
      break;
    default:
      headerText = "Super Admin Dashboard";
  }

  return headerText;
}

export default MainSuperAdminScreen;
