import React, { useEffect, useState } from "react";
import { Select, MenuItem, SelectChangeEvent } from "@mui/material";
import { useGetCohorts } from "../../Instructor/hooks/useGetCohorts";
import Spinner from "../../Learner/components/Spinner";
import { useQueryClient } from "@tanstack/react-query";
import { useEnrollUsers } from "../../Instructor/hooks/useEnrollUsers";
import showError from "../../Learner/scripts/showErrorDialog";
import showSuccess from "../../Learner/scripts/showSuccessDialog";
import UserTable from "../GroupManagement/PaginatedUsersTable";

const EnrollmentTable: React.FC = () => {
  const queryClient = useQueryClient();
  const { data: cohorts, isFetching } = useGetCohorts();
  const [cohort, setCohort] = useState<string>("");
  const mutation = useEnrollUsers();

  const handleSelect = (event: SelectChangeEvent) => {
    console.log(event.target.value);
    setCohort(event.target.value);
  };

  useEffect(() => {
    if (cohort) {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  }, [cohort]);

  useEffect(() => {
    if (cohorts && cohorts.length > 0) {
      setCohort(cohorts[0].id.toLocaleString());
    }
  }, [cohorts]);

  const handleEnrollUsers = () => {
    if (cohort) {
      mutation.mutate(
        {
          cohortId: cohort,
          courseId: "1",
        },
        {
          onSuccess: () => {
            showSuccess(
              "Success",
              "All users of selected cohort are enrolled successfully!",
            );
          },
          onError: () => {
            showError("Submission failed", "Please try again.");
            console.log("Submission failed. Please try again.");
          },
        },
      );
    }
  };

  return (
    <>
      <Spinner loading={isFetching || mutation.isPending} />

      <div className="rounded-lg bg-white p-4 shadow-md">
        <h1 className="mb-12 text-3xl font-bold text-gray-800">
          Course Enrollment
        </h1>
        <div className="mb-4 flex items-center justify-between">
          <div>
            <label
              htmlFor="cohort-select"
              className="mr-2 text-lg font-semibold text-[#2A2A2A]"
            >
              Select Cohort:
            </label>
            {cohorts && cohorts.length > 0 ? (
              <Select
                id="cohort-select"
                value={cohort}
                onChange={handleSelect}
                className="h-[36px] min-w-[164px] rounded bg-[#DAE3F2] text-center text-lg font-semibold text-[#36537F]"
              >
                {cohorts.map((c) => (
                  <MenuItem key={c.id} value={c.id}>
                    {c.name}
                  </MenuItem>
                ))}
              </Select>
            ) : null}
          </div>
        </div>
        {cohort ? <UserTable cohortId={parseInt(cohort)} /> : null}
        <div className="mt-4 flex justify-center">
          <button
            onClick={handleEnrollUsers}
            className="mx-2 h-[40px] w-[195px] rounded-lg bg-[#36537F] px-5 text-center text-lg font-medium text-white hover:bg-blue-400"
          >
            Enroll
          </button>
        </div>
      </div>
    </>
  );
};

export default EnrollmentTable;
