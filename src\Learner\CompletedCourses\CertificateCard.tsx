import React from "react";
import GradientButton from "../components/GradientButton";

const CertificateCard: React.FC = () => {
  return (
    <div className="flex items-center rounded-lg bg-[#9A9A9A] bg-opacity-20 p-6 shadow-md">
      <div className="w-3/4">
        <h2 className="mb-2 text-xl font-bold text-blue-800">Dignity</h2>
        <p className="mb-2 text-gray-700">Cohort 1</p>
        <p className="mb-2 text-gray-700">
          Overall Progress: <span className="font-bold">100%</span>
        </p>
        <div className="mb-2 h-2.5 w-full rounded-full bg-gray-200">
          <div
            className="h-2.5 rounded-full bg-yellow-500"
            style={{ width: "100%" }}
          ></div>
        </div>
        <p className="mb-4 text-gray-700">
          Great work! You can download your certificate here.
        </p>
        <div className="flex space-x-4">
          <GradientButton text="Download" color1="#2B3D59" color2="#375685" />
          {/* <GradientButton text="View" color1="#2B3D59" color2="#375685" /> */}
        </div>
        <p className="mt-4 text-gray-700">Share it on:</p>
        <div className="mt-2 flex space-x-2">
          <a
            href="https://facebook.com"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img src="/facebook.png" alt="Facebook" className="h-6 w-6" />
          </a>
          <a
            href="https://twitter.com"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img src="/twitter.png" alt="Twitter" className="h-6 w-6" />
          </a>
          <a
            href="https://linkedin.com"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img src="/linkedin.png" alt="LinkedIn" className="h-6 w-6" />
          </a>
        </div>
      </div>
      <div className="flex w-1/4 justify-end">
        <img src="/completed-courses-success.png" alt="Cartoon" />
      </div>
    </div>
  );
};

export default CertificateCard;
