# Cache Management System

This document describes the comprehensive cache clearing system implemented in the application.

## Overview

The cache management system automatically clears all application caches on first load while preserving authentication tokens. This ensures a clean state for each session while maintaining user login status.

## What Gets Cleared

### ✅ Automatically Cleared on App Load
- **React Query Cache**: All cached API responses and query data
- **localStorage**: All stored data except `token` and `userType`
- **sessionStorage**: All session-specific data
- **IndexedDB**: All databases and stored data
- **Zustand Stores**: Reset to initial state

### ✅ Preserved
- **Authentication Token**: `localStorage.getItem("token")`
- **User Type**: `localStorage.getItem("userType")`

## Implementation

### Core Components

1. **CacheManager** (`src/utils/cacheManager.ts`)
   - Central utility class for all cache operations
   - Handles different types of cache clearing
   - Provides debugging and status information

2. **useCacheManager Hook** (`src/hooks/useCacheManager.ts`)
   - React hook for manual cache management
   - Provides functions for selective cache clearing
   - Integrates with React Query client

3. **CacheDebugPanel** (`src/components/CacheDebugPanel.tsx`)
   - Development-only debug panel
   - Manual cache clearing controls
   - Cache status visualization

### Automatic Cache Clearing

Cache clearing happens automatically when the app initializes in `src/main.tsx`:

```typescript
// Set up cache manager with query client
CacheManager.setQueryClient(queryClient);

// Clear all cache except auth tokens on app initialization
CacheManager.initializeCacheClear();
```

### Manual Cache Management

Use the `useCacheManager` hook for manual cache operations:

```typescript
import { useCacheManager } from '../hooks/useCacheManager';

function MyComponent() {
  const { clearAllCache, clearReactQueryCache, getCacheStatus } = useCacheManager();

  const handleClearCache = async () => {
    await clearAllCache();
    console.log('Cache cleared!');
  };

  return (
    <button onClick={handleClearCache}>
      Clear Cache
    </button>
  );
}
```

## Available Functions

### CacheManager Methods

- `clearAllCacheExceptAuth()`: Clears all caches while preserving auth tokens
- `clearBrowserStorage()`: Clears localStorage and sessionStorage (preserves auth)
- `clearReactQueryCache()`: Clears only React Query cache
- `clearIndexedDB()`: Clears all IndexedDB databases
- `resetZustandStores()`: Resets Zustand stores to initial state
- `clearSpecificQueries(queryKeys)`: Clears specific React Query cache keys
- `getCacheStatus()`: Returns current cache status for debugging

### useCacheManager Hook

Provides the same functions as CacheManager but as React hooks:

```typescript
const {
  clearAllCache,
  clearBrowserStorage,
  clearReactQueryCache,
  clearSpecificQueries,
  resetZustandStores,
  getCacheStatus,
  clearIndexedDB,
} = useCacheManager();
```

## Debug Panel

In development mode, a cache debug panel is available:

1. Look for the "🗂️ Cache" button in the bottom-right corner
2. Click to open the debug panel
3. Use various buttons to clear different types of cache
4. View cache status and statistics

### Debug Panel Features

- **Clear All Cache**: Comprehensive cache clearing (preserves auth)
- **Clear Browser Storage**: localStorage and sessionStorage only
- **Clear React Query Cache**: API cache only
- **Clear IndexedDB**: Database storage only
- **Reset Zustand Stores**: Application state only
- **Clear Specific Queries**: Target specific React Query keys
- **Cache Status**: View current cache statistics

## Configuration

### Environment Variables

The debug panel only appears when `import.meta.env.DEV` is true (development mode).

### Zustand Store Integration

To integrate a Zustand store with the cache manager:

1. Add a `reset` method to your store:

```typescript
export const useMyStore = create<MyStore>((set) => ({
  // ... other state
  reset: () => set(initialState),
}));
```

2. Expose the reset function globally:

```typescript
if (typeof window !== 'undefined') {
  (window as any).resetMyStore = () => {
    useMyStore.getState().reset();
  };
}
```

3. Update the cache manager to call your reset function in `resetZustandStores()`.

## Best Practices

1. **Always preserve auth tokens** when clearing cache
2. **Use selective clearing** when possible for better performance
3. **Test cache clearing** in development using the debug panel
4. **Monitor cache status** to understand cache usage patterns
5. **Handle errors gracefully** when cache operations fail

## Troubleshooting

### Common Issues

1. **Auth tokens lost**: Check that `clearBrowserStorage()` is preserving tokens correctly
2. **Cache not clearing**: Verify that `CacheManager.setQueryClient()` is called with the correct client
3. **Debug panel not showing**: Ensure you're in development mode
4. **Zustand stores not resetting**: Check that reset functions are properly exposed globally

### Debugging

Use `getCacheStatus()` to inspect current cache state:

```typescript
const status = getCacheStatus();
console.log('Cache status:', status);
```

This returns information about:
- localStorage item count and auth token presence
- sessionStorage item count
- React Query cache size
- Query client availability

## Security Considerations

- Auth tokens are preserved during cache clearing
- Sensitive data in other storage is properly cleared
- Debug panel is only available in development mode
- Cache operations are logged for debugging but don't expose sensitive data
