import React from "react";

interface UserManagementIconProps {
  selected?: boolean;
}

function UserManagementIcon({ selected = false }: UserManagementIconProps) {
  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.11765 6.53333C7.93694 6.53333 9.41177 5.0708 9.41177 3.26667C9.41177 1.46254 7.93694 0 6.11765 0C4.29836 0 2.82353 1.46254 2.82353 3.26667C2.82353 5.0708 4.29836 6.53333 6.11765 6.53333Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
      <path
        d="M6.99546 7.2968C6.81795 7.2884 6.64044 7.28 6.45487 7.28C4.50227 7.28 2.67877 7.8428 1.12153 8.8088C0.411498 9.2456 0 10.0688 0 10.934V13.16H7.47151C6.90161 12.3136 6.55733 11.3252 6.47371 10.2953C6.39009 9.26534 6.57011 8.23073 6.99546 7.2968ZM15.1286 9.8C15.1286 9.6152 15.1044 9.4472 15.0802 9.2708L16 8.4224L15.1931 6.9692L14.0232 7.3808C13.765 7.154 13.4745 6.9776 13.1518 6.8516L12.9097 5.6H11.296L11.054 6.8516C10.7312 6.9776 10.4407 7.154 10.1826 7.3808L9.01261 6.9692L8.20575 8.4224L9.12557 9.2708C9.10136 9.4472 9.07716 9.6152 9.07716 9.8C9.07716 9.9848 9.10136 10.1528 9.12557 10.3292L8.20575 11.1776L9.01261 12.6308L10.1826 12.2192C10.4407 12.446 10.7312 12.6224 11.054 12.7484L11.296 14H12.9097L13.1518 12.7484C13.4745 12.6224 13.765 12.446 14.0232 12.2192L15.1931 12.6308L16 11.1776L15.0802 10.3292C15.1044 10.1528 15.1286 9.9848 15.1286 9.8ZM12.1029 11.48C11.2153 11.48 10.4892 10.724 10.4892 9.8C10.4892 8.876 11.2153 8.12 12.1029 8.12C12.9904 8.12 13.7166 8.876 13.7166 9.8C13.7166 10.724 12.9904 11.48 12.1029 11.48Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default UserManagementIcon;
