import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useReactMediaRecorder } from "react-media-recorder";
import GradientButton from "../components/GradientButton";
import NextButton from "../components/NextButton";
import Spinner from "../components/Spinner";
import RedButton from "../components/RedButton";
import showError from "../scripts/showErrorDialog";
import { usePostActivityProgress } from "../hooks/usePostActivityProgress";
import { useGetActivity } from "../hooks/useGetActivity";
import { useQueryClient } from "@tanstack/react-query";
import "../components/VideoStyle.css";
import ListeningComponent from "./ListeningComponent";
import RespondingComponent from "./RespondingComponent";
import Markdown from "react-markdown";
import useGetHeyGenURL from "../hooks/useGetHeyGenURL";
import GreenButton from "../components/GreenButton";
import RubricEvaluationComponent from "./RubricEvaluationComponent";

interface ResponseData {
  facial_expressions: number;
  eye_contact: number;
  body_movement_and_posture: number;
  gestures: number;
  tone_and_manner_of_speech: number;
  choice_of_words: number;
}

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

const ActivityContainer: React.FC = () => {
  const queryClient = useQueryClient();
  const query = useQueryParam();
  const course_id = query.get("course_id") ?? "";
  const activity_id = query.get("activity_id") ?? "";
  const [watchTutorial, setWatchTutorial] = useState(false);
  const [tutorialRecording, setTutorialRecording] = useState(false);
  const [watchVideo, setWatchVideo] = useState(false);
  const [error, setError] = useState(false);
  const [recording, setRecording] = useState(false);
  const [cameraStatus, setcameraStatus] = useState<string>("not");
  const [screenType, setScreenType] = useState<
    | "watchTutorial"
    | "cameraPreview"
    | "recordScreen"
    | "uploadScreen"
    | "waitScreen"
    | "resultScreen"
  >("watchTutorial");
  const navigate = useNavigate();
  const getHeyGenURL = useGetHeyGenURL();

  const redirectToLink = (nextActivityLink: string) => {
    navigate("/my-learning" + nextActivityLink);
  };

  const isBossChallenge = window.location.href.includes("boss");

  const [response, setResponse] = useState<boolean>(false);
  const [respondingData, setRespondingData] = useState<ResponseData | null>(
    null,
  );
  const [listeningData, setListeningData] = useState<ResponseData | null>(null);
  const [youDidWellAtListening, setyouDidWellAtListening] = useState<string[]>(
    [],
  );
  const [youDidWellAtResponding, setyouDidWellAtResponding] = useState<
    string[]
  >([]);
  const [youCanImproveOnListening, setyouCanImproveOnListening] = useState<
    string[]
  >([]);
  const [youCanImproveOnResponding, setyouCanImproveOnResponding] = useState<
    string[]
  >([]);
  const [nextActivityLink, setNextActivityLink] = useState("");
  const [heygenVideoUrl, setHeygenVideoUrl] = useState<string>("");
  const {
    data: activity,
    isLoading,
    isFetching,
    isError,
  } = useGetActivity(activity_id);
  const mutation = usePostActivityProgress("listening");
  const mutationResponding = usePostActivityProgress("responding");

  useEffect(() => {
    // Process instructions to extract Heygen video information if present
    console.log("useEffect 1");
    if (activity?.instructions) {
      // Create a temporary div to parse the HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = activity.instructions;

      // Find the div with Heygen video data
      const heygenDiv = tempDiv.querySelector("div[data-heygen-video-id]");

      if (heygenDiv) {
        console.log(
          "Found Heygen div:",
          heygenDiv.getAttribute("data-heygen-video-id"),
        );
        const heygenVideoId = heygenDiv.getAttribute("data-heygen-video-id");
        getHeyGenURL(heygenVideoId as string).then((url) => {
          setHeygenVideoUrl(url);
        });

        // Remove the Heygen div from the instructions
        heygenDiv.remove();
      }
    }
  }, [activity]);

  const handleUpload = async (videoBlob: Blob) => {
    if (screenType === "watchTutorial") {
      console.log("uploading listening video");
      setWatchTutorial(!watchTutorial);
      setScreenType("cameraPreview");
      mutation.mutate(
        {
          courseId: course_id,
          activityId: activity_id,
          userInput: "",
          video: videoBlob,
        },
        {
          onSuccess: (data) => {
            console.log(
              "screen type: ",
              screenType,
              " camera status: ",
              cameraStatus,
            );
            if (data) {
              if (data.next_activity_link) {
                setNextActivityLink(data.next_activity_link);
              }
              if (data.result) {
                // setResponse(true);
                loadListeningData(data.result);
              }
            }
          },
          onError: () => {
            setError(true);
            showError("Submission failed", "Please try again.");
            console.log("Submission failed. Please try again.");
          },
        },
      );
    } else {
      setWatchVideo(true);
      console.log("uploading responding video");
      setScreenType("waitScreen");
      mutationResponding.mutate(
        {
          courseId: course_id,
          activityId: activity_id,
          userInput: "",
          video: videoBlob,
        },
        {
          onSuccess: (data) => {
            if (data) {
              if (data.next_activity_link) {
                setNextActivityLink(data.next_activity_link);
              }
              if (data.result) {
                setResponse(true);
                loadRespondingData(data.result);
              }
            }
          },
          onError: () => {
            setError(true);
            showError("Submission failed", "Please try again.");
            console.log("Submission failed. Please try again.");
          },
        },
      );
    }
  };

  const loadListeningData = (response: any) => {
    if (response && typeof response == "object") {
      setListeningData(response["listening"]);
      setyouDidWellAtListening(response["you_did_well_at_the_following"]);
      setyouCanImproveOnListening(
        response["you_can_improve_by_focusing_on_the_following"],
      );
    }
  };

  const loadRespondingData = (response: any) => {
    // if (response && typeof response == "object") {
    //   setRespondingData(response["responding"]);
    //   setyouDidWellAtResponding(response["you_did_well_at_the_following"]);
    //   setyouCanImproveOnResponding(
    //     response["you_can_improve_by_focusing_on_the_following"],
    //   );
    // }
    setRespondingData(response);
  };

  const {
    status,
    startRecording,
    stopRecording,
    mediaBlobUrl,
    previewStream,
    clearBlobUrl,
  } = useReactMediaRecorder({
    onStop(blobUrl, blob) {
      if (screenType === "watchTutorial") {
        console.log("recorder blob url:", blobUrl);
        handleUpload(blob);
        clearBlobUrl();
        // setWatchTutorial(!watchTutorial);
      }
    },
    video: true,
    audio: true,
  });

  const handleTutorialStarted = () => {
    stopRecording();
    clearBlobUrl();
    startRecording();
  };

  const handleTutorialEnded = () => {
    stopRecording();
  };

  const getBlobAndUpload = () => {
    if (mediaBlobUrl) {
      fetch(mediaBlobUrl)
        .then((response) => response.blob())
        .then((blob) => handleUpload(blob))
        .then(() => clearBlobUrl());
    }
  };

  const handleRecord = () => {
    // handleStopRecord();
    console.log("handleRecord");
    setScreenType("recordScreen");
    console.log(screenType);
    setRecording(true);
    stopRecording();
    clearBlobUrl();
    setcameraStatus("close");
    startRecording();
  };

  const openCamera = () => {
    clearBlobUrl();
    setcameraStatus("open");
    startRecording();
    setScreenType("recordScreen");
    setRecording(false);
  };

  const handleStopRecord = () => {
    setRecording(false);
    stopRecording();
    setScreenType("uploadScreen");
    setcameraStatus("not");
  };

  useEffect(() => {
    // queryClient.invalidateQueries({ queryKey: ["activity"] });
    console.log("useEffect 2");
    setWatchVideo(false);
    setError(false);
    setResponse(false);
    setWatchTutorial(false);
    setcameraStatus("not");
    stopRecording();
    setRecording(false);
    clearBlobUrl();
    setRespondingData(null);
    setListeningData(null);
    setTutorialRecording(false);
  }, [activity_id]);

  function setResponseToNull() {
    setScreenType("cameraPreview");
    setError(false);
    setResponse(false);
    clearBlobUrl();
    setWatchVideo(false);
    setcameraStatus("not");
    setWatchTutorial(false);
    stopRecording();
    setRecording(false);
    setRespondingData(null);
    setListeningData(null);
    setTutorialRecording(false);
  }

  const closeVideo = () => {
    setWatchVideo(false);
    if (error) {
      setResponse(false);
      setScreenType("cameraPreview");
    } else {
      setResponse(true);
      setScreenType("resultScreen");
    }
    setError(false);
    setcameraStatus("not");
    clearBlobUrl();
  };

  useEffect(() => {
    console.log("useEffect 4");
    return () => {
      if (status === "recording") {
        stopRecording();
      }
    };
  }, [status]);

  const handleContextMenu = (event: React.MouseEvent<HTMLVideoElement>) => {
    event.preventDefault(); // Prevents the right-click menu from opening
  };

  const VideoPreview = ({ stream }: { stream: MediaStream | null }) => {
    const videoRef = useRef<HTMLVideoElement>(null);

    useEffect(() => {
      console.log("useEffect 6");
      if (videoRef.current && stream) {
        videoRef.current.srcObject = stream;
      }
    }, [stream]);
    if (!stream) {
      return null;
    }
    return (
      <video
        ref={videoRef}
        autoPlay
        className="mb-4 h-auto w-full max-w-full rounded-md"
      />
    );
  };

  const VideoPreviewControl = ({ stream }: { stream: MediaStream | null }) => {
    const videoRefC = useRef<HTMLVideoElement>(null);

    useEffect(() => {
      console.log("useEffect 7");
      if (videoRefC.current && stream) {
        videoRefC.current.srcObject = stream;
      }
    }, [stream]);
    if (!stream) {
      return null;
    }
    return (
      <video
        ref={videoRefC}
        autoPlay
        controls
        className="mb-4 h-auto w-full max-w-full rounded-md"
      />
    );
  };

  return (
    <div>
      <Spinner loading={(isLoading || isFetching) && !isError} />
      {activity && (
        <>
          {screenType === "watchTutorial" ? (
            <>
              <div key={activity_id} className="mx-auto w-full max-w-4xl px-4">
                <div className="text-base md:text-lg">
                  {activity?.instructions && (
                    <p
                      className="mb-4"
                      dangerouslySetInnerHTML={{
                        __html: activity.instructions,
                      }}
                    />
                  )}
                </div>
              </div>
              <div className="flex w-full flex-col items-center justify-center space-y-6 px-4 py-6 md:space-y-10 md:px-6 md:py-10">
                <div className="flex w-full max-w-4xl flex-col items-center">
                  {(activity?.video || heygenVideoUrl) && (
                    <div className="w-full rounded-md bg-slate-300 p-2 md:p-3">
                      <video
                        key={heygenVideoUrl || activity.video}
                        className="hide-seekbar h-auto w-full max-w-full"
                        controls
                        controlsList="nodownload"
                        onContextMenu={handleContextMenu}
                        onPlay={() => {
                          if (!tutorialRecording) {
                            setTutorialRecording(!tutorialRecording);
                            handleTutorialStarted();
                          }
                        }}
                        onEnded={handleTutorialEnded}
                      >
                        <source
                          src={heygenVideoUrl || activity.video || ""}
                          type="video/mp4"
                        />
                        {/* <source src="/demo-video.mov" /> */}
                      </video>
                    </div>
                  )}
                </div>
              </div>
              {activity?.instructions_below && (
                <p
                  className="mx-auto mb-4 w-full max-w-4xl px-4 text-sm md:text-base"
                  dangerouslySetInnerHTML={{
                    __html: activity.instructions_below,
                  }}
                />
              )}
            </>
          ) : screenType === "cameraPreview" ? (
            <>
              <div key={activity_id} className="mx-auto w-full max-w-4xl px-4">
                <div className="text-base md:text-lg">
                  {activity?.instructions_cp && (
                    <p
                      className="mb-4"
                      dangerouslySetInnerHTML={{
                        __html: activity.instructions_cp,
                      }}
                    />
                  )}
                </div>
              </div>
              <div className="flex w-full flex-col items-center justify-center space-y-6 px-4 py-6 md:space-y-10 md:px-6 md:py-10">
                <div className="flex w-full max-w-4xl flex-col items-center">
                  {activity?.video && (
                    <div className="w-full rounded-md bg-slate-300 p-2 md:p-3">
                      <video
                        key={activity.video}
                        className="hide-seekbar h-auto w-full max-w-full"
                        controls
                        controlsList="nodownload"
                        onContextMenu={handleContextMenu}
                      >
                        <source src={activity.video} type="video/mp4" />
                        {/* <source src="/demo-video.mov" /> */}
                      </video>
                    </div>
                  )}
                  <div className="mt-4 text-sm md:text-base">
                    {activity?.instructions_cp_below && (
                      <p
                        className="mb-4"
                        dangerouslySetInnerHTML={{
                          __html: activity.instructions_cp_below,
                        }}
                      />
                    )}
                  </div>
                  <RedButton onClick={openCamera}>Camera Preview</RedButton>
                </div>
              </div>
            </>
          ) : screenType === "recordScreen" ? (
            <>
              <div className="mx-auto w-full max-w-4xl px-4">
                <div className="text-base md:text-lg">
                  {activity?.instructions_rec && (
                    <p
                      className="mb-4"
                      dangerouslySetInnerHTML={{
                        __html: activity.instructions_rec,
                      }}
                    />
                  )}
                </div>
              </div>
              <div className="flex w-full flex-col items-center justify-center space-y-6 px-4 py-6 md:space-y-10 md:px-6 md:py-10">
                <div className="flex w-full max-w-4xl flex-col items-center">
                  {cameraStatus == "open" ? (
                    <>
                      <div className="w-full">
                        <VideoPreview stream={previewStream} />
                      </div>
                      <RedButton onClick={handleRecord}>Record</RedButton>
                    </>
                  ) : (
                    <>
                      {isBossChallenge ? (
                        <div className="w-full rounded-md bg-slate-300 p-2 md:p-3">
                          <video
                            key="third video"
                            className="hide-seekbar h-auto w-full max-w-full"
                            controls
                            autoPlay
                            controlsList="nodownload"
                            onContextMenu={handleContextMenu}
                          >
                            <source src="/demo-video.mov" type="video/mp4" />
                          </video>
                        </div>
                      ) : (
                        <div className="w-full">
                          <VideoPreviewControl stream={previewStream} />
                        </div>
                      )}

                      <div className="mt-4 text-sm md:text-base">
                        {activity?.instructions_rec_below && (
                          <p
                            className="mb-4"
                            dangerouslySetInnerHTML={{
                              __html: activity.instructions_rec_below,
                            }}
                          />
                        )}
                      </div>

                      <GradientButton
                        text="Stop Recording"
                        color1="#E6635A"
                        color2="#D64D44"
                        onClick={handleStopRecord}
                      />
                    </>
                  )}
                </div>
              </div>
            </>
          ) : screenType === "uploadScreen" ? (
            <>
              <div className="flex w-full flex-col items-center justify-center space-y-6 px-4 py-6 md:space-y-10 md:px-6 md:py-10">
                <div className="w-full max-w-4xl text-center">
                  <h1 className="mb-4 text-base text-white md:text-lg">
                    Thank you. If you would like to record your response again,
                    please press the “Record Again” button. Otherwise, you can
                    press “Upload” for the AI to analyse your response.
                  </h1>
                </div>
                <div className="flex w-full max-w-4xl flex-col items-center">
                  <div className="w-full rounded-md bg-slate-300 p-2 md:p-3">
                    <video
                      key={mediaBlobUrl}
                      className="h-auto w-full max-w-full rounded-md"
                      controls
                    >
                      <source src={mediaBlobUrl} type="video/mp4" />
                    </video>
                  </div>
                  <div className="mt-6 flex flex-col justify-center gap-4 sm:flex-row">
                    <GradientButton
                      text="Record Again"
                      color1="#2B3D59"
                      color2="#375685"
                      width="200px"
                      onClick={handleRecord}
                    />

                    <GradientButton
                      text="Upload"
                      color1="#E6635A"
                      color2="#D64D44"
                      width="200px"
                      onClick={getBlobAndUpload}
                    />
                  </div>
                </div>
              </div>
            </>
          ) : screenType === "waitScreen" ? (
            <div className="flex w-full flex-col items-center justify-center space-y-6 px-4 py-6 md:space-y-10 md:px-6 md:py-10">
              <div className="w-full max-w-4xl text-center">
                {activity?.instructions_wr && (
                  <p
                    className="mb-4 text-base md:text-lg"
                    dangerouslySetInnerHTML={{
                      __html: activity.instructions_wr,
                    }}
                  />
                )}
              </div>
              <div className="flex w-full max-w-4xl flex-col items-center">
                {activity?.second_video && (
                  <div className="w-full rounded-md bg-slate-300 p-2 md:p-3">
                    <video
                      key={activity.second_video}
                      className="hide-seekbar h-auto w-full max-w-full"
                      controls
                      controlsList="nodownload"
                      onContextMenu={handleContextMenu}
                    >
                      <source src={activity.second_video} type="video/mp4" />
                    </video>
                  </div>
                )}
                {activity?.instructions_wr_below && (
                  <p
                    className="mb-4 mt-4 text-sm md:text-base"
                    dangerouslySetInnerHTML={{
                      __html: activity.instructions_wr_below,
                    }}
                  />
                )}
              </div>
              <div className="mt-4">
                {!mutation.isPending && !mutationResponding.isPending ? (
                  <GreenButton onClick={closeVideo}>
                    {error ? "Error: Try Again" : "Continue"}
                  </GreenButton>
                ) : (
                  <RedButton disabled={true}>Processing...</RedButton>
                )}
              </div>
            </div>
          ) : screenType === "resultScreen" ? (
            <div className="flex w-full flex-col items-start">
              <div className="flex w-full flex-col items-center justify-center space-y-6 px-4 py-6 md:space-y-10 md:px-6 md:py-10">
                <h1 className="text-center text-xl font-semibold">
                  Your Results from AI Analysis
                </h1>
                <div className="grid w-full max-w-7xl grid-cols-1 gap-6 text-center md:grid-cols-2">
                  {mutation.isPending ? (
                    <p>Loading...</p>
                  ) : mutation.isError ? (
                    <p>Error...</p>
                  ) : listeningData ? (
                    <div className="mx-auto w-full max-w-md">
                      <ListeningComponent
                        listeningData={listeningData}
                        youDidWellAt={youDidWellAtListening}
                        youCanImproveOn={youCanImproveOnListening}
                      />
                    </div>
                  ) : null}
                  {mutationResponding.isPending ? (
                    <p>Loading...</p>
                  ) : mutationResponding.isError ? (
                    <p>Error...</p>
                  ) : respondingData ? (
                    <div className="mx-auto w-full max-w-md rounded-[24px] bg-yellow-300 p-4 text-left text-black shadow-lg md:rounded-[44px]">
                      <h2 className="mb-4 text-center text-xl font-semibold">
                        Responding
                      </h2>
                      {/* <Markdown>{respondingData as any}</Markdown> */}
                      <RubricEvaluationComponent data={respondingData} />
                    </div>
                  ) : null}
                </div>
                {!isBossChallenge ? (
                  <div className="text-center">
                    <GradientButton
                      text="Try Again"
                      color1="#2B3D59"
                      color2="#375685"
                      onClick={setResponseToNull}
                    />
                  </div>
                ) : null}
              </div>
              <div className="mt-4 flex w-full justify-center">
                <NextButton onClick={() => redirectToLink(nextActivityLink)} />
              </div>
            </div>
          ) : null}
        </>
      )}
    </div>
  );
};

export default ActivityContainer;
