import React from "react";

interface OverviewIconProps {
  selected?: boolean;
}

function OverviewIcon({ selected = false }: OverviewIconProps) {
  return (
    <svg
      width="13"
      height="15"
      viewBox="0 0 13 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.78114 0.525002C4.78864 0.248436 5.85929 0 6.5 0C7.14071 0 8.21136 0.249374 9.21886 0.524063C10.1188 0.774124 11.0127 1.04607 11.8996 1.33969C12.1552 1.42503 12.3817 1.5814 12.5532 1.79084C12.7246 2.00029 12.8341 2.25431 12.8691 2.52375C13.4225 6.72093 12.1383 9.83156 10.5801 11.8894C9.91941 12.7697 9.13156 13.5449 8.24293 14.1891C7.93565 14.412 7.61009 14.608 7.26979 14.775C7.00886 14.8987 6.73029 15 6.5 15C6.26972 15 5.99022 14.8987 5.73021 14.775C5.44793 14.6409 5.1155 14.445 4.75707 14.1891C3.86846 13.5448 3.08062 12.7697 2.41986 11.8894C0.861718 9.83156 -0.422494 6.72093 0.130933 2.52375C0.165929 2.25444 0.27546 2.00056 0.446942 1.79128C0.618425 1.582 0.844903 1.4258 1.10036 1.34062C1.71136 1.13906 2.75043 0.806252 3.78114 0.525002Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
      <path
        d="M6.56679 6.26814C6.1384 7.23194 5.43509 9.52585 7.00462 11.9229C6.74726 11.9666 6.48732 11.9923 6.22658 12C5.88259 11.4374 4.5526 8.9662 5.8095 6.18226C5.81861 6.1624 5.82248 6.14042 5.82074 6.11852C5.819 6.09663 5.8117 6.07559 5.79957 6.05751C5.78745 6.03943 5.77092 6.02495 5.75164 6.01551C5.73236 6.00607 5.71101 6.002 5.68972 6.00372C2.91163 6.23588 1.03489 3.19116 1 3.19116C3.64747 2.15531 6.07474 5.73178 6.14971 5.8441C6.15015 5.84512 6.15079 5.84603 6.1516 5.84677C6.15271 5.84473 6.15397 5.84278 6.15537 5.84094C6.24497 5.69951 7.97647 3.0376 11 3C11 3.07059 10.2981 5.14302 7.24794 5.72135C7.10003 5.74941 6.96089 5.81376 6.84231 5.90895C6.72373 6.00414 6.62922 6.12736 6.56679 6.26814Z"
        fill={selected ? "#A3A3A3" : "white"}
      />
    </svg>
  );
}

export default OverviewIcon;
