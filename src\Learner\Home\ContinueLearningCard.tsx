import { Course } from "../../types/CourseProgress";
import { useNavigate } from "react-router-dom";

interface ContinueLearningProps {
  course_id: number;
  progress: number;
  course: Course;
}

const ContinueLearningCard: React.FC<ContinueLearningProps> = ({
  course_id,
  course,
  progress,
}) => {
  const navigate = useNavigate();

  return (
    <button
      onClick={() =>
        navigate(
          `/my-learning/${course.activities[0].type}?course_id=${course_id}&activity_id=${course.activities[0].id}`,
        )
      }
      className="flex h-auto w-full flex-col justify-evenly space-y-4 rounded-lg bg-[#9A9A9A] bg-opacity-20 p-4 text-left md:h-52 md:w-[700px] md:flex-row md:space-x-5 md:space-y-0 md:p-6"
    >
      <div className="mx-auto flex-none md:mx-0">
        <img
          src="continue-learning-card-image.png"
          alt="Course image"
          className="h-24 w-auto md:h-auto"
        />
      </div>
      <div className="grow flex-col space-y-2 md:space-y-4">
        <p className="text-center text-xl font-semibold text-[#2C3F5C] md:text-left md:text-3xl">
          {course.name}
        </p>
        <p className="text-center text-base font-medium text-[#898989] md:text-left md:text-xl">
          Cohort 1
        </p>
        <div className="flex flex-col space-y-1 md:flex-row md:items-center md:space-x-2 md:space-y-0">
          <p className="text-center text-base font-medium text-[#898989] md:text-left md:text-xl">
            Overall Progress{" "}
          </p>
          <p className="text-center text-lg font-bold text-[#1C1C1C] md:text-left md:text-2xl">
            {" "}
            {progress.toFixed(2)}%
          </p>
        </div>
        <div className="h-2.5 w-full rounded-full bg-[#ACBFDB]">
          <div
            className="h-2.5 rounded-full bg-[#F1D13F]"
            style={{ width: `${Math.round(progress)}%` }}
          ></div>
        </div>
      </div>
      <div className="hidden flex-none md:block">
        <img src="continue-learning-plus.png" alt="Continue learning" />
      </div>
    </button>
  );
};

export default ContinueLearningCard;
