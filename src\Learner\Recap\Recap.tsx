import React, { useEffect } from "react";
import NextButton from "../components/NextButton";
import Spinner from "../components/Spinner";
import { useLocation } from "react-router-dom";
import { usePostActivityProgress } from "../hooks/usePostActivityProgress";
import { useGetActivity } from "../hooks/useGetActivity";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import "../components/VideoStyle.css"

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

const Recap: React.FC = () => {
  const queryClient = useQueryClient();
  const query = useQueryParam();
  const course_id = query.get("course_id") ?? "";
  const activity_id = query.get("activity_id") ?? "";
  const mutation = usePostActivityProgress();
  // const [video, setVideo] = useState<string | null | undefined>('');
  const { data: activity, isLoading, isFetching, isError } = useGetActivity(activity_id);
  const navigate = useNavigate();

  useEffect(() => {
    queryClient.invalidateQueries({ queryKey: ['activity'] });
    // setVideo(activity?.video)
  }, [activity_id]);

  const submitAndRedirectToLink = async () => {
    mutation.mutate({ courseId: course_id, activityId: activity_id, userInput: "" },
      {
        onSuccess: (data) => {
          if (data && data.next_activity_link) {
            navigate('/my-learning' + data.next_activity_link);
          }
        },
        onError: () => {
          console.log('Submission failed. Please try again.');
        }
      }
    )
  };

  const handleContextMenu = (event: React.MouseEvent<HTMLVideoElement>) => {
    event.preventDefault(); // Prevents the right-click menu from opening
  };

  return (
    <div>
      <Spinner loading={(isLoading || isFetching) && !isError} />
      <div>
        <div className="flex flex-col items-center justify-center space-y-14 bg-[#CCCCCC] py-10">
          <div key={activity_id} className="flex flex-col items-center">
            <h1 className="mb-4 text-2xl text-center font-bold leading-none tracking-tight text-gray-900">{activity?.name}</h1>
          </div>
          {activity?.instructions && (<p className="mb-4" dangerouslySetInnerHTML={{ __html: activity.instructions }} />)}
          {activity?.video && (
            <div className="flex bg-slate-300 p-3">
              <video key={activity.video} width="607" height="362" controls controlsList="nodownload"
                onContextMenu={handleContextMenu} className="hide-seekbar">
                <source src={activity.video} type="video/mp4" />
              </video>
            </div>
          )}
          {activity?.title_videos?.map((each) => (
            <div key={each.video} className="bg-slate-300 p-3">
              <h1 className="text-xl">{each.title}</h1>
              <video key={each.video} width="607" height="362" controls controlsList="nodownload"
                onContextMenu={handleContextMenu} className="hide-seekbar">
                <source src={each.video} type="video/mp4" />
              </video>
            </div>
          ))
          }
          {activity?.instructions_below && (<p className="mb-4" dangerouslySetInnerHTML={{ __html: activity.instructions_below }} />)}
          <NextButton onClick={submitAndRedirectToLink} />
        </div>
      </div>
    </div>
  );
};

export default Recap;
