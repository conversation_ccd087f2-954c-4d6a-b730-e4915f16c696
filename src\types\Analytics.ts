interface OverViewData {
    total_courses: number,
    total_students: number,
    total_students_completed_courses: number,
    total_students_remaining_courses: number
}

interface LoginUserData {
    active_users: number,
    users_accessed_course: number,
    average_user_session: number,
    time_of_day_most_users_active: string
}

interface PlungeData {
    most_used_pharase: number,
    average_time_to_complete: number | string,
    total_replay_time: number,
}

interface QuizData {
    average_score: string,
    average_time_to_complete: number | string,
    total_replay_time: number,
}

interface OverviewResponse {
    success: boolean,
    message: string,
    data: OverViewData
}

interface LoginUserResponse {
    success: boolean,
    message: string,
    data: LoginUserData
}

interface PlungeResponse {
    success: boolean,
    message: string,
    data: PlungeData
}

interface QuizResponse {
    success: boolean,
    message: string,
    data: QuizData
}
  
export type { OverviewResponse, LoginUserResponse, PlungeResponse, QuizResponse };