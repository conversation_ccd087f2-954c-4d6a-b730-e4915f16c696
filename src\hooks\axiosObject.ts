import { useMemo, useState } from 'react';
import axios, { AxiosInstance } from 'axios';
import { useAuth } from '../Learner/context/AuthProvider';
import Swal from 'sweetalert2';

const useAxios = (): AxiosInstance => {
  const { token, logout } = useAuth();
  const [isTokenInvalid, setIsTokenInvalid] = useState(false);

  const axiosInstance = useMemo(() => {
    const instance = axios.create({
      baseURL: import.meta.env.VITE_BACKENDURL, // Replace with your API base URL
    });

    instance.interceptors.request.use(
      (config) => {
        if (isTokenInvalid) {
          return Promise.reject(new axios.Cancel('Token is invalid. Please re-login.'));
        }
        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    instance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (
          error.response &&
          error.response.status === 401 &&
          error.response.data.message === 'invalid credentials, recheck password and email.'
        ) {
          setIsTokenInvalid(true);
          localStorage.removeItem('token');
          Swal.fire({
            icon: 'error',
            title: 'Session Expired',
            text: 'Please login again.',
            confirmButtonText: 'Ok',
          }).then((result) => {
            if (result.isConfirmed) {
              logout();
            }
          });
        }
        return Promise.reject(error);
      }
    );

    return instance;
  }, [token, isTokenInvalid, logout]); // Recreate the instance if the token or isTokenInvalid changes

  return axiosInstance;
};

export default useAxios;
