import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { AxiosInstance } from "axios";
import { GeneralResponse } from "../../types/GeneralResponse";

// api call
const deleteCourse = async (
  axiosObject: AxiosInstance,
  courseId: string,
): Promise<GeneralResponse> => {
  const response = await axiosObject.delete<GeneralResponse>(
    "/api/v1/course/delete/" + courseId
  );
  console.log(response);
  return response.data;
};

export function useDeleteCourse() {
  const axiosInstance = useAxios();
  // run the query
  return useMutation({
    mutationFn: ({ courseId }: {
      courseId: string,
    }) => {
      return deleteCourse(axiosInstance, courseId);
    },
  })
}