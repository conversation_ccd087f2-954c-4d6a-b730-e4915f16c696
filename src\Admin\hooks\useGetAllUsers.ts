import { useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { AxiosInstance } from "axios";
import { UserPaginated } from "../../types/User";

// api call

const getAllUsers = async (
  axiosObject: AxiosInstance,
  page: number,
  limit: number,
  searchTerm: string,
): Promise<UserPaginated> => {
  console.log(page, limit)
  const users = await axiosObject.get<UserPaginated>(
    `/api/v1/user/get/all?page=${page}&size=${limit}&search=${searchTerm}`,
  );
  console.log(users);
  return users.data;
};

export function useGetAllUsers(page: number, limit: number, searchTerm: string) {
  const axiosObject = useAxios();
  // run the query
  return useQuery<UserPaginated>({
    queryKey: ["users", page, limit, searchTerm],
    refetchOnWindowFocus: false,
    queryFn: () => getAllUsers(axiosObject, page, limit, searchTerm)
  });
}
