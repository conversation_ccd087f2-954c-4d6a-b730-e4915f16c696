import ReplayIcon from "@mui/icons-material/Replay";
import { useEffect, useRef, useState } from "react";
import { useReactMediaRecorder } from "react-media-recorder";
import gemLogo from "../assets/gem-logo.png";
import zaheenLogo from "../assets/zaheenLogo.png";
import Spinner from "../Learner/components/Spinner";
import showError from "../Learner/scripts/showErrorDialog";
import { usePostAIActivity } from "./hooks/usePostAIActivity";
import ListeningComponent from "./ListeningComponent";
import RespondingComponent from "./RespondingComponent";
import VideoPlayer from "./VideoPlayer";
import { MonitorPlay } from "lucide-react";
import DemoVideoModal from "./DemoVideoModal";
import { useDemoStore } from "./store/useDemoStore";
import FoodResponseComponent from "./FoodResponseComponent";
import TigerResponseComponent from "./TigerResponseComponent";
import PandaResponseComponent from "./PandaResponseComponent";
import NewListeningComponent from "./NewListeningComponent";

const VideoPreview = ({ stream }: { stream: MediaStream | null }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;
    }
  }, [stream]);
  if (!stream) {
    return null;
  }
  return (
    <div className="mt-16 flex justify-center">
      <video className="w-[709px] rounded-[42px]" ref={videoRef} autoPlay />
    </div>
  );
};

interface FacialExpressions {
  calmAndApproachableExpression: string;
  engagedListening: string;
  noSignsOfFrustrationOrAnnoyance: string;
  supportiveGestures: string;
  openAndRelaxedFacialFeatures: string;
}

interface Feedback {
  positiveAreas: string;
  improvementSuggestions: string;
}

interface ListeningData {
  facialExpressions: FacialExpressions;
  overallScore: number;
  feedback: Feedback;
}

interface SoundToneOfVoice {
  calmAndSteadyTone: string;
  empatheticTone: string;
  clearArticulation: string;
  nonDefensiveTone: string;
  appropriateVolume: string;
}

interface TextChoiceOfWords {
  acknowledgmentOfTheIssue: string;
  useOfPoliteLanguage: string;
  solutionOrientedWords: string;
  apologeticAndResponsibleLanguage: string;
  avoidanceOfBlamingLanguage: string;
}

interface RespondingData {
  facialExpressions: FacialExpressions;
  soundToneOfVoice: SoundToneOfVoice;
  textChoiceOfWords: TextChoiceOfWords;
  overallScore: number;
  feedback: Feedback;
}

function Demo() {
  const videoType = useDemoStore((state) => state.video_type);
  const { reset } = useDemoStore();
  type responseType = "First Response" | "Changing Tab" | "Response Recorded";
  const [playVideo, setPlayVideo] = useState<boolean>(false);
  const [showDemoModal, setShowDemoModal] = useState<boolean>(false);
  const [firstResponse, setFirstResponse] =
    useState<responseType>("First Response");
  const initialRecordingsState: Blob[] = [];
  const [recordingBlobs, setRecordingBlobs] = useState<Blob[]>(
    initialRecordingsState,
  );
  const mutation = usePostAIActivity("listening");

  const [response, setResponse] = useState<boolean>(false);
  const mutationResponding = usePostAIActivity("responding");

  const [respondingData, setRespondingData] = useState<RespondingData | any>(
    null,
  );
  const [listeningData, setListeningData] = useState<ListeningData | null>(
    null,
  );
  // const [youDidWellAtListening, setyouDidWellAtListening] = useState<string[]>([]);
  // const [youDidWellAtResponding, setyouDidWellAtResponding] = useState<string[]>([]);
  // const [youCanImproveOnListening, setyouCanImproveOnListening] = useState<string[]>([]);
  // const [youCanImproveOnResponding, setyouCanImproveOnResponding] = useState<string[]>([]);

  useEffect(() => {
    if (firstResponse === "Changing Tab") {
      setFirstResponse("First Response");
    }
  }, [firstResponse]);

  useEffect(() => {
    document.title = "GEM";
    const originalFavicon = document.querySelector("link[rel='icon']");

    const favicon = document.createElement("link");
    favicon.rel = "icon";
    favicon.href = "/favicon-gem.png";

    if (originalFavicon && originalFavicon.parentNode) {
      originalFavicon.parentNode.removeChild(originalFavicon);
    }
    document.head.appendChild(favicon);

    return () => {
      if (favicon.parentNode) {
        favicon.parentNode.removeChild(favicon);
      }
      if (originalFavicon) {
        document.head.appendChild(originalFavicon);
      }
    };
  }, []);

  const { status, startRecording, stopRecording, previewStream, clearBlobUrl } =
    useReactMediaRecorder({
      video: true,
      audio: true,
      stopStreamsOnStop: true,
      onStop(blobUrl, blob) {
        setRecordingBlobs([...recordingBlobs, blob]);
        if (firstResponse === "First Response") {
          setFirstResponse("Response Recorded");
          mutation.mutate(
            {
              video: blob,
            },
            {
              onSuccess: (data) => {
                if (data) {
                  // setResponse(true);
                  loadListeningData(data);
                }
              },
              onError: () => {
                showError("Submission failed", "Please try again.");
                console.log("Submission failed. Please try again.");
              },
            },
          );
        }
        if (firstResponse === "Response Recorded") {
          setResponse(true);
          mutationResponding.mutate(
            {
              video: blob,
            },
            {
              onSuccess: (data) => {
                if (data) {
                  setResponse(true);
                  loadRespondingData(data);
                }
              },
              onError: () => {
                // Error handling
                showError("Submission failed", "Please try again.");
                console.log("Submission failed. Please try again.");
              },
            },
          );
          clearBlobs();
        }
      },
    });

  const loadListeningData = (response: ListeningData) => {
    if (response && typeof response == "object") {
      setListeningData(response);
    }
  };

  const loadRespondingData = (response: RespondingData) => {
    if (response && typeof response == "object") {
      setRespondingData(response);
    }
  };

  const clearBlobs = () => {
    setRecordingBlobs(initialRecordingsState);
    console.log("Blobs cleared, current state:", recordingBlobs); // Debugging
  };

  // const uploadRecordings = (finalBlobs: Blob[]) => {
  //   console.log(finalBlobs);
  // };

  return (
    <>
      <Spinner loading={mutation.isPending && mutationResponding.isPending} />
      <div className="relative max-h-fit min-h-svh w-full bg-[#D6EEF0]">
        {showDemoModal && (
          <DemoVideoModal onClose={() => setShowDemoModal(false)} />
        )}
        <div className="flex items-start justify-between px-12 pt-5">
          <img src={zaheenLogo} className="h-[130px]" alt="Zaheen Logo" />
          <img src={gemLogo} className="w-[130px]" alt="Gem Logo" />
        </div>
        <div className="flex flex-col items-center justify-center space-y-4">
          {response && !(mutation.isPending && mutationResponding.isPending) ? (
            <>
              <div className="group fixed bottom-8 right-8">
                <button
                  className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-[#E6635A] to-[#D64D44] text-2xl text-[#F5F5F5] shadow-lg hover:from-[#D64D44] hover:to-[#E6635A]"
                  onClick={() => {
                    setResponse(false);
                    setListeningData(null);
                    setRespondingData(null);
                    reset();
                  }}
                >
                  <ReplayIcon style={{ fontSize: "40px" }} />
                </button>
                <span className="absolute bottom-20 right-1/2 w-24 translate-x-1/2 rounded bg-black px-2 py-1 text-center text-sm text-white opacity-0 transition-opacity group-hover:opacity-100">
                  Try Again
                </span>
              </div>

              <div className="flex min-h-screen w-full items-center justify-center bg-[#D6EEF0] pb-4">
                <div className="grid w-full max-w-7xl grid-cols-2 gap-4 text-center">
                  {mutation.isPending ? (
                    <p>Loading...</p>
                  ) : mutation.isError ? (
                    <p>Error...</p>
                  ) : listeningData ? (
                    <ListeningComponent listeningData={listeningData} />
                  ) : null}

                  {mutationResponding.isPending ? (
                    <p>Loading...</p>
                  ) : mutationResponding.isError ? (
                    <p>Error...</p>
                  ) : respondingData ? (
                    <>
                      <div className="group fixed bottom-36 right-8">
                        <button
                          className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-[#E6635A] to-[#D64D44] text-2xl text-[#F5F5F5] shadow-lg hover:from-[#D64D44] hover:to-[#E6635A]"
                          onClick={() => setShowDemoModal(true)}
                        >
                          <MonitorPlay size={40} />
                        </button>
                        <span className="absolute bottom-20 right-1/2 w-24 translate-x-1/2 rounded bg-black px-2 py-1 text-center text-sm text-white opacity-0 transition-opacity group-hover:opacity-100">
                          Watch Demo
                        </span>
                      </div>
                      {videoType === "food_complaint" ? (
                        <div className="flex flex-col gap-4">
                          <RespondingComponent
                            respondingData={respondingData.standard_analysis}
                          />
                          <FoodResponseComponent
                            responseData={respondingData.new_analysis}
                          />
                        </div>
                      ) : videoType === "tiger_enclosure" ? (
                        <div className="flex flex-col gap-4">
                          <RespondingComponent
                            respondingData={respondingData.standard_analysis}
                          />
                          <TigerResponseComponent
                            responseData={respondingData.new_analysis}
                          />
                        </div>
                      ) : videoType === "panda_closure" ? (
                        <div className="flex flex-col gap-4">
                          <RespondingComponent
                            respondingData={respondingData.standard_analysis}
                          />
                          <PandaResponseComponent
                            responseData={respondingData.new_analysis}
                          />
                        </div>
                      ) : (
                        <RespondingComponent respondingData={respondingData} />
                      )}
                    </>
                  ) : null}
                </div>
              </div>
            </>
          ) : (
            <div
              className={
                status === "recording"
                  ? "flex h-[600px] flex-row items-center justify-center"
                  : ""
              }
            >
              <VideoPlayer
                onVideoEnd={() => {
                  stopRecording();
                  setPlayVideo(false);
                }}
                onChangeTab={() => {
                  setFirstResponse("Changing Tab");
                  clearBlobs();
                  setPlayVideo(false);
                  clearBlobUrl();
                }}
                shouldPlay={playVideo}
                key={"anyh"}
                videoSrc="https://publicial.s3.ap-southeast-1.amazonaws.com/demo_videos/easy.mp4"
              />

              {status === "recording" ? (
                <div className="relative ml-4 flex h-full flex-col items-center justify-center">
                  <VideoPreview
                    key={"recordingPreview"}
                    stream={previewStream}
                  />
                  {firstResponse === "Response Recorded" && (
                    <button
                      className="hover:to-[#E6635A]] absolute bottom-5 mt-4 w-80 rounded-lg bg-gradient-to-r from-[#E6635A] to-[#D64D44] px-16 py-2 text-lg text-[#F5F5F5] hover:from-[#D64D44]"
                      onClick={() => {
                        stopRecording();
                        setFirstResponse("First Response");
                      }}
                    >
                      Stop Recording
                    </button>
                  )}
                </div>
              ) : (
                <div className="ml-4 flex flex-col items-center">
                  <h1>
                    {firstResponse === "Response Recorded"
                      ? "Please press the start button to respond to this customer."
                      : ""}
                  </h1>
                  <button
                    className="mt-4 w-80 rounded-lg bg-gradient-to-r from-[#E6635A] to-[#D64D44] px-16 py-2 text-lg text-[#F5F5F5] hover:from-[#D64D44] hover:to-[#E6635A]"
                    onClick={() => {
                      startRecording();
                      if (firstResponse === "First Response") {
                        setPlayVideo(true);
                        setResponse(false);
                      }
                    }}
                  >
                    Start
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      {/* <div ref={lastDivRef} /> */}
    </>
  );
}

export default Demo;
