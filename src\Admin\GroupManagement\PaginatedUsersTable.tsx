import * as React from 'react';
import { useQueryClient } from "@tanstack/react-query";
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import TextField from '@mui/material/TextField';
import { useGetCohortUsers } from '../../Instructor/hooks/useGetCohortUsers';
import Spinner from '../../Learner/components/Spinner';
import { useUpdateUser } from '../hooks/useUpdateUser';
import showSuccess from '../../Learner/scripts/showSuccessDialog';
import showError from '../../Learner/scripts/showErrorDialog';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';

interface UserTableProps {
  cohortId: number;
}

interface Column {
  id: 'name' | 'email' | 'role' | 'organization_name' | 'ethnicity';
  label: string;
  minWidth?: number;
  align?: 'right';
}

const columns: Column[] = [
  { id: 'name', label: 'Name', minWidth: 170 },
  { id: 'email', label: 'Email', minWidth: 100 },
  { id: 'role', label: 'Role', minWidth: 170 },
  { id: 'organization_name', label: 'Organization', minWidth: 170 },
  { id: 'ethnicity', label: 'Ethnicity', minWidth: 170 },
];

const UserTable: React.FC<UserTableProps> = ({ cohortId }) => {
  const queryClient = useQueryClient();
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);
  const [selectedRow, setSelectedRow] = React.useState<any | null>(null);
  const [openDialog, setOpenDialog] = React.useState(false);

  const { data: userData, isFetching, isError } = useGetCohortUsers(cohortId.toLocaleString(), page + 1, rowsPerPage);

  const mutation = useUpdateUser();

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  const handleEditClick = (row: any) => {
    setSelectedRow(row);
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
    setSelectedRow(null);
  };

  const handleSave = () => {
    // Call your API function here to update the values in the database
    console.log('Updated data:', selectedRow);
    mutation.mutate(
      {
        userId: selectedRow.id,
        name: selectedRow.name,
        ethnicity: selectedRow.ethnicity,
        organization: selectedRow.organization_name
      },
      {
        onSuccess: () => {
          showSuccess("Success", "User Updated Successfully!");
          queryClient.invalidateQueries({ queryKey: ["users", cohortId.toLocaleString(), page + 1, rowsPerPage] });
        },
        onError: () => {
          showError("Submission failed", "Please try again.");
          console.log("Submission failed. Please try again.");
        },
      },
    );
    // After saving, close the dialog
    setOpenDialog(false);
  };

  const handleFieldChange = (field: string, value: string) => {
    setSelectedRow((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <>
      <Spinner loading={(isFetching || mutation.isPending) && !(isError || mutation.isError)} />
      <Paper sx={{ width: '100%' }}>
        <TableContainer sx={{ maxHeight: 440 }}>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align={column.align}
                    style={{ top: 0, minWidth: column.minWidth }}
                  >
                    {column.label}
                  </TableCell>
                ))}
                <TableCell>Edit</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {userData?.items.map((row, index) => (
                <TableRow hover role="checkbox" tabIndex={-1} key={index}>
                  {columns.map((column) => {
                    const value = row[column.id];
                    return (
                      <TableCell key={column.id} align={column.align}>
                        {column.label === "Role" && !value ? "learner" : value}
                      </TableCell>
                    );
                  })}
                  <TableCell>
                    <Button variant="outlined" onClick={() => handleEditClick(row)}>
                      Edit
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 100]}
          component="div"
          count={userData?.total || 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      <Dialog open={openDialog} onClose={handleDialogClose}>
        <DialogTitle>Edit User</DialogTitle>
        <DialogContent>
          {selectedRow && (
            <>
              {columns
                .filter((column) => column.id !== 'role') // Remove role from the form
                .map((column) => {
                  if (column.id === 'email') {
                    // Make email uneditable
                    return (
                      <TextField
                        key={column.id}
                        margin="dense"
                        label={column.label}
                        fullWidth
                        value={selectedRow[column.id] || ''}
                        disabled={true}
                        InputProps={{ readOnly: true }}
                      />
                    );
                  }

                  if (column.id === 'ethnicity') {
                    // Add Select options for ethnicity
                    return (
                      <Select
                        key={column.id}
                        label={column.label}
                        value={selectedRow[column.id] || ''}
                        fullWidth
                        onChange={(e) => handleFieldChange(column.id, e.target.value)}
                        margin="dense"
                      >
                        <MenuItem value="chinese">Chinese</MenuItem>
                        <MenuItem value="malay">Malay</MenuItem>
                        <MenuItem value="indian">Indian</MenuItem>
                        <MenuItem value="others">Others</MenuItem>
                      </Select>
                    );
                  }

                  return (
                    <TextField
                      key={column.id}
                      margin="dense"
                      label={column.label}
                      fullWidth
                      value={selectedRow[column.id] || ''}
                      onChange={(e) => handleFieldChange(column.id, e.target.value)}
                    />
                  );
                })}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose}>Cancel</Button>
          <Button onClick={handleSave} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default UserTable;
