import { useEffect, useRef, useState } from "react";
import { useDemoStore } from "./store/useDemoStore";

interface VideoPlayerProps {
  videoSrc: string;
  shouldPlay: boolean;
  onVideoEnd: () => void;
  onChangeTab: () => void;
}

type Scenario = "Scenario1" | "Scenario2" | "Scenario3" | "Scenario4";

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoSrc,
  shouldPlay,
  onVideoEnd,
  onChangeTab,
}) => {
  const [scenario, setScenario] = useState<Scenario>("Scenario1");
  const [vidSrc, setVidSrc] = useState<string>(videoSrc);
  const videoRef = useRef<HTMLVideoElement>(null);
  const setVideoType = useDemoStore((state) => state.setVideoType);
  const setDemo = useDemoStore((state) => state.setDemo);

  useEffect(() => {
    if (shouldPlay && videoRef.current) {
      videoRef.current.play().catch((error) => {
        console.error("Error playing video:", error);
      });
    }
  }, [shouldPlay]);

  useEffect(() => {
    if (scenario === "Scenario1") {
      setDemo(false);
      return;
    } else {
      const mapping = {
        Scenario1: null,
        Scenario2: "food_complaint",
        Scenario3: "tiger_enclosure",
        Scenario4: "panda_closure",
      } as const;
      setVideoType(mapping[scenario as keyof typeof mapping]);
      setDemo(true);
    }
  }, [scenario, setVideoType, setDemo]);
  const handleScenarioChange = (newScenario: Scenario, newVideoSrc: string) => {
    onChangeTab();
    setScenario(newScenario);
    setVidSrc(newVideoSrc);
  };

  const scenarioButtons = [
    { type: "Scenario1", text: "Easy", source: videoSrc },
    {
      type: "Scenario2",
      text: "Moderately Difficult",
      source:
        "https://publicial.s3.ap-southeast-1.amazonaws.com/demo_videos/21.mp4",
    },
    {
      type: "Scenario3",
      text: "Difficult",
      source:
        "https://publicial.s3.ap-southeast-1.amazonaws.com/demo_videos/difficult.mp4",
    },
    {
      type: "Scenario4",
      text: "Very Difficult",
      source:
        "https://publicial.s3.ap-southeast-1.amazonaws.com/demo_videos/11.mp4",
    },
  ];

  return (
    <div className="flex flex-col justify-center text-white">
      <div className="flex w-full max-w-4xl justify-start space-x-2 pl-12">
        {scenarioButtons.map(({ type, text, source }) => (
          <button
            key={type}
            onClick={() => handleScenarioChange(type as Scenario, source)}
            className={`rounded-t-lg bg-[#22409A] ${
              scenario === type ? "" : "opacity-50"
            } w-36 px-4 py-2 text-center hover:bg-blue-600 focus:outline-none`}
          >
            {text}
          </button>
        ))}
      </div>

      <div className="h-fit w-[709px] items-center justify-center rounded-[44px] bg-[#22409A] p-20">
        <p className="mb-4 text-center">
          A visitor has approached you. Please press the start button to listen
          and then respond according to the situation. Keep it brief.
        </p>

        <div className="relative overflow-hidden rounded-lg">
          <video
            ref={videoRef}
            className="pointer-events-none h-auto w-full"
            src={vidSrc}
            onEnded={onVideoEnd}
            controlsList="nodownload nofullscreen noremoteplayback"
          />
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
