# TanStack Form Best Practices & Documentation Summary

## Overview
TanStack Form is a headless, performant, and type-safe form state management library for TypeScript/JavaScript, React, Vue, Angular, Solid, Lit, and Svelte. It provides a powerful and flexible approach to form management with first-class TypeScript support.

## Core Philosophy
- **Headless UI**: No built-in styling, complete control over appearance
- **Framework-agnostic**: Works across multiple frontend frameworks
- **Type-safe**: First-class TypeScript support with full type inference
- **Performance-focused**: Minimal re-renders and optimized reactivity
- **Flexible validation**: Support for sync/async validation and schema libraries

## Installation
```bash
npm install @tanstack/react-form
# or
yarn add @tanstack/react-form
# or
pnpm add @tanstack/react-form
```

## Best Practices

### 1. Form Setup and Structure

#### Use formOptions for Reusable Forms
```typescript
interface User {
  firstName: string
  lastName: string
  hobbies: Array<string>
}

const defaultUser: User = { firstName: '', lastName: '', hobbies: [] }

const formOpts = formOptions({
  defaultValues: defaultUser,
})

const form = useForm({
  ...formOpts,
  onSubmit: async ({ value }) => {
    console.log(value)
  },
})
```

#### Always Provide Default Values
```typescript
const form = useForm({
  defaultValues: {
    firstName: '',
    lastName: '',
    email: '',
    age: 0,
  },
  onSubmit: async ({ value }) => {
    // Handle submission
  },
})
```

### 2. Field Management Best Practices

#### Use Render Props Pattern
```typescript
<form.Field
  name="firstName"
  children={(field) => (
    <>
      <label htmlFor={field.name}>First Name:</label>
      <input
        id={field.name}
        name={field.name}
        value={field.state.value}
        onBlur={field.handleBlur}
        onChange={(e) => field.handleChange(e.target.value)}
      />
      <FieldInfo field={field} />
    </>
  )}
/>
```

#### Create Reusable Field Components
```typescript
function FieldInfo({ field }: { field: AnyFieldApi }) {
  return (
    <>
      {field.state.meta.isTouched && !field.state.meta.isValid ? (
        <em>{field.state.meta.errors.join(', ')}</em>
      ) : null}
      {field.state.meta.isValidating ? 'Validating...' : null}
    </>
  )
}
```

### 3. Validation Best Practices

#### Use Multiple Validation Triggers
```typescript
<form.Field
  name="firstName"
  validators={{
    onChange: ({ value }) =>
      !value
        ? 'A first name is required'
        : value.length < 3
          ? 'First name must be at least 3 characters'
          : undefined,
    onBlur: ({ value }) => 
      value.includes('error') ? 'No "error" allowed in first name' : undefined,
  }}
/>
```

#### Implement Async Validation with Debouncing
```typescript
<form.Field
  name="username"
  asyncDebounceMs={500}
  validators={{
    onChangeAsyncDebounceMs: 1000,
    onChangeAsync: async ({ value }) => {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      return value.includes('error') && 'No "error" allowed in username'
    },
  }}
/>
```

#### Use Schema Libraries for Complex Validation
```typescript
import { z } from 'zod'

const userSchema = z.object({
  age: z.number().gte(13, 'You must be 13 to make an account'),
  email: z.string().email('Invalid email format'),
})

const form = useForm({
  defaultValues: { age: 0, email: '' },
  validators: {
    onChange: userSchema,
  },
})
```

### 4. Performance Optimization

#### Use Selective Subscriptions
```typescript
// ✅ Good - Subscribe to specific values
const firstName = useStore(form.store, (state) => state.values.firstName)
const errors = useStore(form.store, (state) => state.errorMap)

// ❌ Bad - Subscribe to entire store
const store = useStore(form.store)
```

#### Use form.Subscribe for Conditional Rendering
```typescript
<form.Subscribe
  selector={(state) => [state.canSubmit, state.isSubmitting]}
  children={([canSubmit, isSubmitting]) => (
    <button type="submit" disabled={!canSubmit}>
      {isSubmitting ? '...' : 'Submit'}
    </button>
  )}
/>
```

### 5. Array Fields Management

#### Proper Array Field Implementation
```typescript
<form.Field
  name="hobbies"
  mode="array"
  children={(hobbiesField) => (
    <div>
      {hobbiesField.state.value.map((_, i) => (
        <div key={i}>
          <form.Field
            name={`hobbies[${i}].name`}
            children={(field) => (
              <input
                value={field.state.value}
                onChange={(e) => field.handleChange(e.target.value)}
              />
            )}
          />
          <button
            type="button"
            onClick={() => hobbiesField.removeValue(i)}
          >
            Remove
          </button>
        </div>
      ))}
      <button
        type="button"
        onClick={() => hobbiesField.pushValue({ name: '', description: '' })}
      >
        Add Hobby
      </button>
    </div>
  )}
/>
```

### 6. Error Handling Best Practices

#### Display Errors Conditionally
```typescript
{field.state.meta.isTouched && !field.state.meta.isValid && (
  <em role="alert">{field.state.meta.errors.join(', ')}</em>
)}
```

#### Use Error Maps for Specific Error Types
```typescript
{field.state.meta.errorMap['onChange'] && (
  <em>{field.state.meta.errorMap['onChange']}</em>
)}
```

#### Form-Level Error Handling
```typescript
const form = useForm({
  validators: {
    onSubmitAsync: async ({ value }) => {
      const hasErrors = await verifyDataOnServer(value)
      if (hasErrors) {
        return {
          form: 'Invalid data',
          fields: {
            age: 'Must be 13 or older to sign',
            'details.email': 'An email is required',
          },
        }
      }
      return null
    },
  },
})
```

### 7. Form Submission Best Practices

#### Proper Form Submission Handling
```typescript
<form
  onSubmit={(e) => {
    e.preventDefault()
    e.stopPropagation()
    form.handleSubmit()
  }}
>
  {/* Form fields */}
</form>
```

#### Reset Button Implementation
```typescript
// ✅ Prevent default HTML reset behavior
<button
  type="reset"
  onClick={(event) => {
    event.preventDefault()
    form.reset()
  }}
>
  Reset
</button>

// ✅ Alternative approach
<button
  type="button"
  onClick={() => form.reset()}
>
  Reset
</button>
```

### 8. TypeScript Best Practices

#### Define Strong Types for Form Data
```typescript
interface FormData {
  firstName: string
  lastName: string
  email: string
  age: number
  preferences: {
    newsletter: boolean
    notifications: boolean
  }
}

const form = useForm<FormData>({
  defaultValues: {
    firstName: '',
    lastName: '',
    email: '',
    age: 0,
    preferences: {
      newsletter: false,
      notifications: false,
    },
  },
})
```

### 9. Field State Understanding

#### Field State Properties
- `isTouched`: After user changes or blurs the field
- `isDirty`: After field value has been changed (persistent)
- `isPristine`: Until user changes the field value
- `isBlurred`: After field has been blurred
- `isDefaultValue`: Whether current value equals default value

### 10. Common Anti-Patterns to Avoid

#### ❌ Don't Use useField Hook for Reactivity
```typescript
// ❌ Discouraged
const field = useField({ form, name: 'firstName' })

// ✅ Use form.Field component instead
<form.Field name="firstName" children={(field) => (/* ... */)} />
```

#### ❌ Don't Subscribe to Entire Store
```typescript
// ❌ Causes unnecessary re-renders
const store = useStore(form.store)

// ✅ Subscribe selectively
const firstName = useStore(form.store, (state) => state.values.firstName)
```

## Supported Schema Libraries
- **Zod**: Most popular TypeScript-first schema validation
- **Valibot**: Lightweight alternative to Zod
- **ArkType**: High-performance runtime validation
- **Effect/Schema**: Part of the Effect ecosystem

## Key Features
- ✅ Headless UI components
- ✅ First-class TypeScript support
- ✅ Framework-agnostic design
- ✅ Synchronous and asynchronous validation
- ✅ Built-in debouncing for async validation
- ✅ Array field management
- ✅ Form and field-level validation
- ✅ Optimized reactivity and performance
- ✅ Standard Schema specification support
- ✅ Server-side rendering (SSR) support

## Resources
- **Official Documentation**: https://tanstack.com/form/latest/docs
- **GitHub Repository**: https://github.com/tanstack/form
- **Discord Community**: https://tlinz.com/discord
- **Examples**: https://tanstack.com/form/latest/docs/framework/react/examples
