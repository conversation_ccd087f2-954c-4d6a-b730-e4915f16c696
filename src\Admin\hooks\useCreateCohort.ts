import { useMutation } from '@tanstack/react-query';
import useAxios from '../../hooks/axiosObject';
import { CohortCreateResponse } from '../../types/Cohort';
import { AxiosInstance } from 'axios';


// api call
export async function postCohort(axiosObject: AxiosInstance, cohortFile: Blob): Promise<CohortCreateResponse> {
  var formData = new FormData();
  formData.append("users_file", cohortFile);
  const cohortResponse = await axiosObject.post<CohortCreateResponse>('/api/v1/user/invite-multiple-users', formData);
  console.log(cohortResponse)
  return cohortResponse.data;
};

export const useCreateCohort = () => {
  const axiosInstance = useAxios();
  return useMutation({
    mutationFn: ({ cohortFile }: {
        cohortFile: Blob
    }) => {
      return postCohort(axiosInstance, cohortFile);
    },
  })
};