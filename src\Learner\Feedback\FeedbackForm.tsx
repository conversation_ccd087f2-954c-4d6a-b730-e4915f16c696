import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { usePostActivityProgress } from "../hooks/usePostActivityProgress";
import { useGetActivity } from "../hooks/useGetActivity";
import GradientButton from "../components/GradientButton";
import NextButton from "../components/NextButton";
import { useNavigate } from "react-router-dom";
import showError from "../scripts/showErrorDialog";
import Spinner from "../components/Spinner";
import showSuccess from "../scripts/showSuccessDialog";
import { FeedbackQuestions } from "../../types/Activity";

interface QuestionAnswer {
  question: string;
  answer: string;
  options: string[];
}

const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

const FeedbackForm: React.FC = () => {
  const query = useQueryParam();
  const course_id = query.get("course_id") ?? "";
  const activity_id = query.get("activity_id") ?? "";
  const { data: activity, isError, isFetching } = useGetActivity(activity_id);
  const mutation = usePostActivityProgress();
  const [nextActivityLink, setnextActivityLink] = useState("");
  const [answers, setAnswers] = useState<QuestionAnswer[]>([]);
  const [prevAnswers, setprevAnswers] = useState<QuestionAnswer[]>([]);
  const navigate = useNavigate();

  const isFeedbackQuestionsArray = (
    questions: any,
  ): questions is FeedbackQuestions[] => {
    return (
      Array.isArray(questions) && questions.every((q) => typeof q === "object")
    );
  };

  const redirectToLink = (nextActivityLink: string) => {
    navigate("/my-learning" + nextActivityLink);
  };

  useEffect(() => {
    console.log("Previous answers:", prevAnswers);
  }, [prevAnswers]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (answers.length == 0 || answers.map((e) => e.answer).includes("")) {
      showError("Error", "Please fill all fields.");
    } else {
      setprevAnswers([...answers]);
      mutation.mutate(
        {
          courseId: course_id,
          activityId: activity_id,
          userInput: JSON.stringify(answers),
        },
        {
          onSuccess: (data) => {
            if (data && data.next_activity_link) {
              setnextActivityLink(data.next_activity_link);
            }
            setAnswers(answers.map((qa) => ({ ...qa, answer: "" })));
            showSuccess("Success", "Survey is successfully submitted");
          },
          onError: () => {
            showError("Submission failed.", "Please try again.");
            console.log("Submission failed. Please try again.");
          },
        },
      );
    }
  };

  useEffect(() => {
    if (activity?.questions !== undefined) {
      console.log(isFeedbackQuestionsArray(activity.questions));
      if (isFeedbackQuestionsArray(activity.questions)) {
        setAnswers(
          activity?.questions.map((e) => {
            return { question: e.question, answer: "", options: e.options };
          }),
        );
      }
    }
  }, [activity?.questions]);

  const handleChange = (value: string, index: number) => {
    const updatedAnswers = answers.map((qa, i) =>
      i === index ? { ...qa, answer: value } : qa,
    );
    setAnswers(updatedAnswers);
  };

  const handleEdit = () => {
    console.log(prevAnswers);
    setAnswers([...prevAnswers]);
    setprevAnswers([]);
  };

  return (
    <>
      <Spinner loading={(mutation.isPending || isFetching) && !isError} />
      <div className="mx-auto max-w-4xl rounded-lg p-6 text-white">
        <p className="mb-4">
          “Dear friends, please follow this link to a GForm, which will contain
          the questions for the Feedback. We thought it would be easier for us
          to respond using the GForm platform. Thank you.”
        </p>
        <div className="">
          <h2 className="mb-2 text-lg font-semibold text-white">
            Please click on the following link to fill the form:
          </h2>
          <a
            href="http://bit.ly/PostDignity"
            target="_blank"
            className="font-semibold text-yellow-300 underline"
          >
            http://bit.ly/PostDignity
          </a>
        </div>
        <div className="mb-6 hidden">
          <h2 className="mb-2 text-lg font-semibold">Participant Sentiments</h2>
          <div className="rounded-lg p-4">
            <form onSubmit={handleSubmit} className="space-y-6">
              {answers.map((qa, index) => (
                <div key={index}>
                  <label
                    htmlFor={`question${index + 1}`}
                    className="mb-1 block font-semibold"
                  >
                    {index + 1}. {qa.question}
                  </label>

                  {qa.options && qa.options.length > 0 ? (
                    <select
                      id={`question${index + 1}`}
                      className="w-full rounded-lg border bg-gray-100 p-2"
                      value={qa.answer}
                      onChange={(event) =>
                        handleChange(event.target.value, index)
                      }
                    >
                      <option value="" disabled>
                        Select an option
                      </option>
                      {qa.options.map((option: string, optionIndex: number) => (
                        <option key={optionIndex} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <textarea
                      id={`question${index + 1}`}
                      className="max-h-24 w-full resize-none truncate rounded-lg border bg-gray-100 p-2"
                      rows={1}
                      value={qa.answer}
                      onChange={(event) =>
                        handleChange(event.target.value, index)
                      }
                    />
                  )}
                </div>
              ))}
              <div className="text-center">
                <GradientButton
                  type="submit"
                  disabled={mutation.isPending}
                  text="Submit"
                  color1="#2B3D59"
                  color2="#375685"
                  width="100px"
                />
                {mutation.isSuccess && prevAnswers.length > 0 ? (
                  <GradientButton
                    type="button"
                    disabled={mutation.isPending}
                    text="Edit"
                    color1="#2B3D59"
                    color2="#375685"
                    width="100px"
                    onClick={handleEdit}
                  />
                ) : null}
              </div>
            </form>
          </div>
          <NextButton
            onClick={() => redirectToLink(nextActivityLink)}
            display={Boolean(nextActivityLink)}
          />
        </div>
      </div>
    </>
  );
};

export default FeedbackForm;
