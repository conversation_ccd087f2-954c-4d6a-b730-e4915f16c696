import { useMutation, useQuery } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { AxiosInstance } from "axios";

// Types for Heygen videos
export interface HeygenVideo {
  id: number;
  name: string;
  s3_path: string;
  instructor_id: number;
  created_at: string;
  updated_at: string;
}

export interface HeygenVideoWithUrl extends HeygenVideo {
  video_url: string;
}

// API call to save a Heygen video
const saveHeygenVideo = async (
  axiosObject: AxiosInstance,
  name: string,
  videoBlob: Blob,
  videoUrl?: string, // Optional URL to download video from
): Promise<HeygenVideo> => {
  const formData = new FormData();
  formData.append("name", name);

  // If we have a videoUrl and the blob is too small (likely failed recording),
  // try to download the video from the URL
  if (videoUrl && videoBlob.size < 10000) {
    // Less than 10KB is probably not a valid video
    try {
      console.log("Blob too small, trying to download from URL:", videoUrl);
      const response = await fetch(videoUrl);
      if (response.ok) {
        const downloadedBlob = await response.blob();
        if (downloadedBlob.size > 10000) {
          console.log(
            "Successfully downloaded video from URL, size:",
            downloadedBlob.size,
          );
          videoBlob = downloadedBlob;
        }
      }
    } catch (error) {
      console.error("Error downloading video from URL:", error);
    }
  }

  // Determine file extension based on blob type
  const fileExtension = videoBlob.type.includes("image") ? ".jpg" : ".webm";
  formData.append("video_file", videoBlob, `${name}${fileExtension}`);

  const response = await axiosObject.post<{
    success: boolean;
    message: string;
    data: HeygenVideo;
  }>("/api/v1/heygen-video/create", formData);

  return response.data.data;
};

// API call to list all Heygen videos for the instructor
const listHeygenVideos = async (
  axiosObject: AxiosInstance,
): Promise<HeygenVideoWithUrl[]> => {
  const response = await axiosObject.get<{
    success: boolean;
    message: string;
    data: HeygenVideoWithUrl[];
  }>("/api/v1/heygen-video/list");

  return response.data.data;
};

// API call to get a specific Heygen video
const getHeygenVideo = async (
  axiosObject: AxiosInstance,
  videoId: number,
): Promise<HeygenVideoWithUrl> => {
  const response = await axiosObject.get<{
    success: boolean;
    message: string;
    data: HeygenVideoWithUrl;
  }>(`/api/v1/heygen-video/${videoId}`);

  return response.data.data;
};

// Hook to save a Heygen video
export function useSaveHeygenVideo() {
  const axiosInstance = useAxios();

  return useMutation({
    mutationFn: ({
      name,
      videoBlob,
      videoUrl,
    }: {
      name: string;
      videoBlob: Blob;
      videoUrl?: string;
    }) => {
      return saveHeygenVideo(axiosInstance, name, videoBlob, videoUrl);
    },
  });
}

// Hook to list all Heygen videos for the instructor
export function useListHeygenVideos() {
  const axiosInstance = useAxios();

  return useQuery<HeygenVideoWithUrl[]>({
    queryKey: ["heygenVideos"],
    queryFn: async () => {
      const videos = await listHeygenVideos(axiosInstance);
      console.log("Fetched Heygen videos:", videos);
      return videos;
    },
    refetchOnWindowFocus: false,
  });
}

// Hook to get a specific Heygen video
export function useGetHeygenVideo(videoId: number) {
  const axiosInstance = useAxios();

  return useQuery<HeygenVideoWithUrl>({
    queryKey: ["heygenVideo", videoId],
    queryFn: () => getHeygenVideo(axiosInstance, videoId),
    enabled: !!videoId,
    refetchOnWindowFocus: false,
  });
}
