import SideBar from "../components/SideBar";
import TopBar from "../components/TopBar";
import MainContent from "./MainContent";
import { useState } from "react";
import "../components/ResponsiveStyles.css";
import { CourseProvider } from "../context/CourseContext";

function HomeScreen() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuClose = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <CourseProvider>
      <div className="flex h-screen flex-col">
        <TopBar onMenuToggle={handleMenuToggle} />
        <div className="learner-main-container flex flex-col md:flex-row">
          <SideBar
            selectedItem={0}
            isMobileMenuOpen={isMobileMenuOpen}
            onMobileMenuClose={handleMenuClose}
          />
          <MainContent />
        </div>
        <div></div>
      </div>
    </CourseProvider>
  );
}

export default HomeScreen;
