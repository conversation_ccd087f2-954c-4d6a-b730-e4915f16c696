import { useMutation } from "@tanstack/react-query";
import { AxiosInstance } from "axios";
import useAxios from "../../hooks/axiosObject";
import { GeneralResponse } from "../../types/GeneralResponse";

// api call

const postEnrollUsers = async (
  axiosObject: AxiosInstance,
  cohortId: string,
  courseId: string,
): Promise<GeneralResponse> => {
  const data = {
    course_id: courseId,
    cohort_id: cohortId,
  };
  const courseProgress = await axiosObject.post<GeneralResponse>(
    "/api/v1/course_progress/create-by-cohort",
    data,
  );
  console.log(courseProgress);
  return courseProgress.data;
};

export function useEnrollUsers() {
  const axiosInstance = useAxios();
  // run the query
  return useMutation({
    mutationFn: ({
      cohortId = "",
      courseId = "",
    }: {
      cohortId: string;
      courseId: string;
    }) => {
      return postEnrollUsers(axiosInstance, cohortId, courseId);
    },
  });
}
