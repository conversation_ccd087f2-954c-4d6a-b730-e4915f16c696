import React from 'react';
import { useCourse } from '../context/CourseContext';
import { useGetCourseProgress } from '../hooks/useGetCourseProgress';
import { useCourseId } from '../hooks/useCourseId';

const CourseContextTest: React.FC = () => {
  const { selectedCourseId, setSelectedCourseId, courseProgressList, selectedCourseProgress } = useCourse();
  const { data: courses_progress } = useGetCourseProgress();
  const course_id = useCourseId();

  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="text-xl font-bold mb-4">Course Context Test</h2>
      
      <div className="mb-4">
        <h3 className="text-lg font-semibold">Current State:</h3>
        <p><strong>Selected Course ID:</strong> {selectedCourseId}</p>
        <p><strong>Course ID from useCourseId hook:</strong> {course_id}</p>
        <p><strong>Selected Course Name:</strong> {selectedCourseProgress?.course.name || 'None'}</p>
        <p><strong>Progress:</strong> {selectedCourseProgress?.progress_percentage.toFixed(2) || 0}%</p>
      </div>
      
      <div className="mb-4">
        <h3 className="text-lg font-semibold">Available Courses:</h3>
        <ul className="list-disc pl-5">
          {courseProgressList?.map(cp => (
            <li key={cp.course_id} className={cp.course_id === selectedCourseId ? 'font-bold' : ''}>
              {cp.course.name} ({cp.progress_percentage.toFixed(2)}%)
            </li>
          ))}
        </ul>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-2">Switch Course:</h3>
        <div className="flex flex-wrap gap-2">
          {courses_progress?.map(cp => (
            <button
              key={cp.course_id}
              onClick={() => setSelectedCourseId(cp.course_id)}
              className={`px-4 py-2 rounded ${
                cp.course_id === selectedCourseId 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 hover:bg-gray-300'
              }`}
            >
              {cp.course.name}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CourseContextTest;
