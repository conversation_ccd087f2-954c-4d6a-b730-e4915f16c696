export const DashboardItems = [
  {
    title: "Usage",
    icon: (
      <svg
        width="75"
        height="75"
        viewBox="0 0 75 75"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M53.9062 9.3625C55.0348 9.3625 56.1523 9.58478 57.1949 10.0167C58.2376 10.4485 59.1849 11.0815 59.9829 11.8796C60.781 12.6776 61.414 13.6249 61.8458 14.6676C62.2777 15.7102 62.5 16.8277 62.5 17.9562V35.9312C58.5434 34.2879 54.1695 33.9337 50 34.9187V24.1531C49.9959 23.8494 49.9321 23.5495 49.8121 23.2705C49.6921 22.9915 49.5183 22.7389 49.3007 22.527C49.083 22.3152 48.8258 22.1483 48.5436 22.0359C48.2615 21.9235 47.9599 21.8677 47.6562 21.8719C47.3526 21.8677 47.051 21.9235 46.7689 22.0359C46.4867 22.1483 46.2295 22.3152 46.0118 22.527C45.7942 22.7389 45.6204 22.9915 45.5004 23.2705C45.3804 23.5495 45.3166 23.8494 45.3125 24.1531V36.6625C41.6124 38.5934 38.5934 41.6124 36.6625 45.3125L36.6094 39.7344C36.6003 39.1299 36.3517 38.5537 35.9181 38.1324C35.4845 37.7111 34.9014 37.4791 34.2969 37.4875C33.6924 37.4916 33.1143 37.7355 32.6895 38.1656C32.2647 38.5957 32.0279 39.1768 32.0312 39.7812L32.1406 50.8656C32.1531 52.1156 33.1781 53.1156 34.4344 53.1156C34.3969 53.6365 34.3771 54.1604 34.375 54.6875C34.3696 58.5592 35.4759 62.3511 37.5625 65.6125H14.8438C12.5645 65.6125 10.3787 64.7071 8.76705 63.0954C7.15541 61.4838 6.25 59.298 6.25 57.0187V17.9562C6.25 15.677 7.15541 13.4912 8.76705 11.8796C10.3787 10.2679 12.5645 9.3625 14.8438 9.3625H53.9062ZM21.0938 28.125C20.4738 28.125 19.879 28.3706 19.4398 28.8081C19.0005 29.2457 18.7525 29.8394 18.75 30.4594V50.7937C18.75 52.0812 19.8 53.125 21.0938 53.125C22.3875 53.125 23.4375 52.0812 23.4375 50.7937V30.4562C23.4342 29.8368 23.1858 29.2439 22.7466 28.807C22.3074 28.3702 21.7132 28.125 21.0938 28.125ZM44.6188 43.6781C44.8519 44.4861 44.9197 45.3328 44.8179 46.1675C44.7162 47.0022 44.447 47.8079 44.0266 48.5361C43.6061 49.2644 43.043 49.9003 42.371 50.4058C41.699 50.9114 40.9319 51.2761 40.1156 51.4781L38.2906 51.9281C37.9962 53.8005 38.0026 55.7078 38.3094 57.5781L39.9969 57.9844C40.8206 58.1828 41.5953 58.5466 42.274 59.0537C42.9528 59.5608 43.5213 60.2006 43.9451 60.9342C44.3689 61.6678 44.6392 62.48 44.7394 63.3213C44.8396 64.1626 44.7677 65.0155 44.5281 65.8281L43.9438 67.8C45.3188 69.0062 46.8812 69.9844 48.5812 70.6812L50.125 69.0594C50.7088 68.4453 51.4115 67.9563 52.1901 67.6221C52.9688 67.288 53.8073 67.1157 54.6547 67.1157C55.502 67.1157 56.3405 67.288 57.1192 67.6221C57.8979 67.9563 58.6005 68.4453 59.1844 69.0594L60.7406 70.7C62.4322 70.011 63.9981 69.0469 65.375 67.8469L64.7562 65.7031C64.5231 64.8952 64.4553 64.0485 64.5571 63.2138C64.6588 62.379 64.928 61.5734 65.3484 60.8451C65.7689 60.1169 66.332 59.4809 67.004 58.9754C67.676 58.4699 68.4431 58.1052 69.2594 57.9031L71.0844 57.4531C71.3788 55.5808 71.3724 53.6735 71.0656 51.8031L69.3781 51.3969C68.5544 51.1985 67.7797 50.8347 67.101 50.3276C66.4222 49.8205 65.8537 49.1807 65.4299 48.447C65.0061 47.7134 64.7358 46.9013 64.6356 46.06C64.5354 45.2187 64.6073 44.3658 64.8469 43.5531L65.4312 41.5844C64.0554 40.374 62.4883 39.4004 60.7938 38.7031L59.2531 40.3219C58.6693 40.9365 57.9664 41.4259 57.1875 41.7603C56.4085 42.0947 55.5696 42.2672 54.7219 42.2672C53.8741 42.2672 53.0353 42.0947 52.2563 41.7603C51.4773 41.4259 50.7745 40.9365 50.1906 40.3219L48.6344 38.6812C46.943 39.3693 45.3771 40.3323 44 41.5312L44.6188 43.6781ZM54.6875 59.375C52.1875 59.375 50.1562 57.275 50.1562 54.6875C50.1562 52.1 52.1875 50 54.6875 50C57.1875 50 59.2188 52.1 59.2188 54.6875C59.2188 57.275 57.1875 59.375 54.6875 59.375Z"
          fill="#145DA0"
        />
      </svg>
    ),
    onClick: () => {},
  },
  {
    title: "Organization",
    icon: (
      <svg
        width="65"
        height="71"
        viewBox="0 0 65 71"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M57.25 7.25V69.75H7.25V7.25H57.25ZM26.1312 69.75H38.6312V57.25H26.1312V69.75ZM38.5 48.5H49V38.5H38.5V48.5ZM1 7.25H63.5V1H1V7.25Z"
          fill="#145DA0"
          stroke="#145DA0"
          stroke-width="2"
        />
        <rect x="16" y="39" width="10" height="10" fill="#D9D9D9" />
        <rect x="16" y="24" width="10" height="10" fill="#D9D9D9" />
        <rect x="16" y="9" width="10" height="10" fill="#D9D9D9" />
        <rect x="39" y="39" width="10" height="10" fill="#D9D9D9" />
        <rect x="39" y="24" width="10" height="10" fill="#D9D9D9" />
        <rect x="39" y="9" width="10" height="10" fill="#D9D9D9" />
      </svg>
    ),
    onClick: () => {},
  },
  {
    title: "Users",
    icon: (
      <svg
        width="75"
        height="75"
        viewBox="0 0 75 75"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M28.9688 8.04749C25.1587 10.8825 24.135 15.3712 24.3225 17.76C24.5625 20.6812 25.155 24.4837 25.155 24.4837C25.155 24.4837 23.9813 25.1212 23.9813 27.6862C24.39 34.125 26.5425 31.3462 26.985 34.17C28.05 40.9725 30.4837 39.7612 30.4837 43.4737C30.4837 49.6575 27.9338 52.5487 19.9725 55.9762C11.985 59.4187 3.75 63.75 3.75 71.25V75H71.25V71.25C71.25 63.75 63.0112 59.4187 55.02 55.98C47.0588 52.5525 44.5163 49.665 44.5163 43.4775C44.5163 39.765 46.9425 40.9762 48.0113 34.1737C48.4575 31.35 50.6063 34.1287 51.0225 27.69C51.0225 25.125 49.845 24.4875 49.845 24.4875C49.845 24.4875 50.4375 20.685 50.6737 17.7637C50.9175 14.7 49.1813 8.15999 42.0488 6.15374C40.8 4.87874 39.9562 2.84999 43.7962 0.813744C35.3962 0.419994 33.4425 4.81499 28.9688 8.04749Z"
          fill="#145DA0"
        />
      </svg>
    ),
    onClick: () => {},
  },
  {
    title: "Reports",
    icon: (
      <svg
        width="75"
        height="75"
        viewBox="0 0 75 75"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M75 23.4375V46.875H46.875V65.625H4.6875V70.3125H0V0H4.6875V4.6875H56.25V23.4375H75ZM4.6875 9.375V23.4375H51.5625V9.375H4.6875ZM42.1875 60.9375V46.875H4.6875V60.9375H42.1875ZM70.3125 42.1875V28.125H4.6875V42.1875H70.3125Z"
          fill="#145DA0"
        />
      </svg>
    ),
    onClick: () => {},
  },
];
