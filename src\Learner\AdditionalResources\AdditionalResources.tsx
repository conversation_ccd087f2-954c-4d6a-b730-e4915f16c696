import React from "react";
import NextButton from "../components/NextButton";
import { useNavigate, useLocation } from "react-router-dom";
import { useGetActivity } from "../hooks/useGetActivity";
import Spinner from "../components/Spinner";
import { usePostActivityProgress } from "../hooks/usePostActivityProgress";

/**
 * Custom hook to get URL query parameters
 */
const useQueryParam = () => {
  return new URLSearchParams(useLocation().search);
};

/**
 * AdditionalResources component displays downloadable resources for learners
 */
const AdditionalResources: React.FC = () => {
  // Get query parameters and activity data
  const query = useQueryParam();
  const activity_id = query.get("activity_id") ?? "";
  const course_id = query.get("course_id") ?? "";
  const mutation = usePostActivityProgress();
  const { data: activity, isLoading } = useGetActivity(activity_id);
  const navigate = useNavigate();

  /**
   * Handles file download by creating a temporary anchor element
   * @param url - URL of the file to download
   */
  const handleDownload = (url: string | null) => {
    if (url) {
      const anchor = document.createElement("a");
      anchor.href = url;
      anchor.download = ""; // This triggers the browser to download the file
      anchor.click();
    }
  };

  /**
   * Opens a link in a new tab
   * @param url - URL to open
   */
  const handleOpenLink = (url: string) => {
    window.open(url, "_blank");
  };

  const submitAndRedirectToLink = async () => {
    mutation.mutate(
      { courseId: course_id, activityId: activity_id, userInput: "" },
      {
        onSuccess: (data) => {
          if (data && data.next_activity_link) {
            navigate("/my-learning" + data.next_activity_link);
          }
        },
        onError: () => {
          console.log("Submission failed. Please try again.");
        },
      },
    );
  };

  // Show loading spinner while fetching data
  if (isLoading) {
    return <Spinner loading={true} />;
  }

  return (
    <>
      <div className="min-h-[250px] p-6 text-white">
        <div className="mb-6 w-full">
          <h1 className="text-xl font-semibold">
            Please click on the following links to download resources:
          </h1>
        </div>
        <div className="w-full">
          <ul className="list-disc space-y-4 pl-5">
            {activity?.file_link && (
              <li>
                <button
                  className="font-bold text-yellow-300 hover:underline focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  onClick={() => handleOpenLink(activity.file_link)}
                  aria-label="Open SkillSeed Client Kit in new tab"
                >
                  SKILLSEED CLIENT KIT
                </button>
              </li>
            )}
            {activity?.file_path && (
              <li>
                <button
                  className="font-bold text-yellow-300 hover:underline focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  onClick={() => handleDownload(activity.file_path)}
                  aria-label="Download Dignity Infographic"
                >
                  DIGNITY INFOGRAPHIC
                </button>
              </li>
            )}
          </ul>
        </div>
      </div>
      <NextButton
        onClick={submitAndRedirectToLink}
        aria-label="Continue to completed courses"
      />
    </>
  );
};

export default AdditionalResources;
