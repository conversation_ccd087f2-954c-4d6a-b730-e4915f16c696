import { useQuery } from "@tanstack/react-query";
import { User } from "../../types/User";
import axios from "axios";
import useAxios from '../../hooks/axiosObject';
import { AxiosInstance } from "axios";

// api call
const getUser = async (axiosObject: AxiosInstance): Promise<User> => {
  const user = await axiosObject.get<User>('/api/v1/user/get');
  console.log(user)
  return user.data;
};

// user query
export function useGetUser() {
  const axiosObject = useAxios();
  // run the query
  return useQuery<User>({
    queryKey: ["user"],
    queryFn: () => getUser(axiosObject),
    staleTime: Infinity,
    //    cacheTime: Infinity,
  });
}
