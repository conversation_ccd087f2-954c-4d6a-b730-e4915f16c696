import React from "react";

function ChangePasswordIcon() {
  return (
    <svg
      width="34"
      height="34"
      viewBox="0 0 34 34"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.983 0C7.599 0 0 7.616 0 17C0 26.384 7.599 34 16.983 34C26.384 34 34 26.384 34 17C34 7.616 26.384 0 16.983 0Z"
        fill="url(#paint0_linear_0_1)"
      />
      <path
        d="M23.6667 12H21V10C21 8.93913 20.5786 7.92172 19.8284 7.17157C19.0783 6.42143 18.0609 6 17 6C15.9391 6 14.9217 6.42143 14.1716 7.17157C13.4214 7.92172 13 8.93913 13 10V12H10.3333C9.97971 12 9.64057 12.1405 9.39052 12.3905C9.14048 12.6406 9 12.9797 9 13.3333V22.6667C9 23.0203 9.14048 23.3594 9.39052 23.6095C9.64057 23.8595 9.97971 24 10.3333 24H23.6667C24.0203 24 24.3594 23.8595 24.6095 23.6095C24.8595 23.3594 25 23.0203 25 22.6667V13.3333C25 12.9797 24.8595 12.6406 24.6095 12.3905C24.3594 12.1405 24.0203 12 23.6667 12ZM17.6667 18.5525V20.6667C17.6667 20.8435 17.5964 21.013 17.4714 21.1381C17.3464 21.2631 17.1768 21.3333 17 21.3333C16.8232 21.3333 16.6536 21.2631 16.5286 21.1381C16.4036 21.013 16.3333 20.8435 16.3333 20.6667V18.5525C15.8885 18.3952 15.5136 18.0858 15.2749 17.6788C15.0362 17.2719 14.949 16.7937 15.0288 16.3287C15.1086 15.8637 15.3502 15.4419 15.7109 15.1378C16.0716 14.8337 16.5282 14.6669 17 14.6669C17.4718 14.6669 17.9284 14.8337 18.2891 15.1378C18.6498 15.4419 18.8914 15.8637 18.9712 16.3287C19.051 16.7937 18.9638 17.2719 18.7251 17.6788C18.4864 18.0858 18.1115 18.3952 17.6667 18.5525ZM19.6667 12H14.3333V10C14.3333 9.29276 14.6143 8.61448 15.1144 8.11438C15.6145 7.61428 16.2928 7.33333 17 7.33333C17.7072 7.33333 18.3855 7.61428 18.8856 8.11438C19.3857 8.61448 19.6667 9.29276 19.6667 10V12Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_0_1"
          x1="-1.8999e-07"
          y1="17"
          x2="34"
          y2="17"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2B3D59" />
          <stop offset="1" stopColor="#375685" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default ChangePasswordIcon;
