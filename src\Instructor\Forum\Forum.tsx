import React from "react";

const Forum: React.FC = () => {
  return (
    <div className="space-y-6 rounded-lg bg-yellow-50 p-6 shadow-md">
      <div className="rounded-lg bg-white p-4 shadow">
        <ul className="mb-4 flex space-x-4">
          <li className="border-b-2 border-gray-900 pb-1 font-semibold">
            Post
          </li>
        </ul>
        <div className="mb-4">
          <div className="rounded-lg border bg-gray-100 p-2">
            <div
              contentEditable
              className="h-24 w-full rounded border border-gray-300 bg-white p-2"
            ></div>
          </div>
        </div>
        <div className="mb-4">
          <label className="mb-2 block text-sm font-bold text-gray-700">
            Topic
          </label>
          <input
            type="text"
            className="mb-2 w-full rounded border border-gray-300 p-2"
            placeholder="Add Topic"
          />
          <p className="text-gray-700">To My Followers</p>
        </div>
        <button className="rounded-md bg-[#36537F] px-4 py-2 text-white">
          Share
        </button>
      </div>

      <div className="rounded-lg bg-white p-4 shadow">
        <ul className="mb-4 flex space-x-4">
          <li className="font-semibold">Featured</li>
          <li className="border-b-2 border-gray-900 pb-1 font-semibold">
            Discussions
          </li>
          <li className="font-semibold">My Feed</li>
        </ul>
        <div className="mb-4">
          <label className="mb-2 block text-sm font-bold text-gray-700">
            Sort By:
          </label>
          <select className="w-full rounded border border-gray-300 p-2">
            <option>Latest Posts</option>
          </select>
        </div>
        <div className="mb-4 rounded-lg bg-yellow-50 p-4 shadow">
          <p className="mb-2 font-semibold text-blue-600">
            Hi, I need help to create a cybersecurity portfolio please.
          </p>
          <p className="text-gray-600">New - Mhfila - 42m ago</p>
        </div>
        <div className="flex justify-between text-gray-600">
          <div className="flex space-x-2">
            <span>👍 7.7k</span>
            <span>💬 144</span>
            <span>🔄 Share</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Forum;
