/* Global responsive styles for Learner section */

/* Base mobile-first styles */
html,
body {
  overflow-x: hidden;
}

/* Video responsiveness */
video {
  max-width: 100%;
  height: auto;
}

/* Image responsiveness */
img {
  max-width: 100%;
  height: auto;
}

/* Responsive text sizing */
@media (max-width: 767px) {
  h1 {
    font-size: 1.5rem !important;
  }

  h2 {
    font-size: 1.25rem !important;
  }

  p {
    font-size: 1rem !important;
  }

  .text-3xl {
    font-size: 1.5rem !important;
  }

  .text-2xl {
    font-size: 1.25rem !important;
  }

  .text-xl {
    font-size: 1.125rem !important;
  }
}

/* Responsive padding and margins */
@media (max-width: 767px) {
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .px-14 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .py-8 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}

/* Fix for overflow issues */
.overflow-x-hidden {
  overflow-x: hidden;
}

/* Responsive tables */
@media (max-width: 767px) {
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}

/* Specific styles for 768px breakpoint to ensure smooth transition */
@media (min-width: 768px) {
  /* Ensure desktop layout is properly applied at 768px and above */
  .learner-main-container {
    flex-direction: row !important;
  }

  .learner-sidebar {
    display: flex !important;
    width: 25% !important;
  }

  .learner-content {
    width: 75% !important;
  }
}

/* Additional responsive fixes for tablet/medium screens */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Adjust content box width for tablet screens */
  .learner-content-box {
    width: 95% !important;
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }

  /* Adjust top bar padding for tablet */
  .learner-top-bar {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }

  /* Ensure proper spacing in content area */
  .learner-content-inner {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  /* Fix grid layouts for tablet screens */
  .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  /* Adjust video sizing for tablet */
  video {
    max-width: 100% !important;
    width: auto !important;
    height: auto !important;
  }

  /* Specific video container fixes */
  .bg-slate-300.p-3 {
    padding: 1rem !important;
  }

  video[width="607"] {
    width: 100% !important;
    max-width: 500px !important;
    height: auto !important;
  }

  /* Fix quiz question cards spacing */
  .grid.grid-cols-1.gap-8 {
    gap: 1.5rem !important;
  }

  /* Adjust button sizing for tablet */
  button {
    min-width: auto !important;
    padding: 0.75rem 1.5rem !important;
  }
}

/* Fix for exactly 768px width to prevent layout jumping */
@media (width: 768px) {
  .learner-main-container {
    flex-direction: row !important;
  }

  .learner-sidebar {
    display: flex !important;
    width: 25% !important;
  }

  .learner-content {
    width: 75% !important;
  }

  /* Ensure mobile menu is hidden at exactly 768px */
  .md\\:hidden {
    display: none !important;
  }

  .hidden.md\\:flex {
    display: flex !important;
  }
}

/* Additional fixes for common responsive issues */
@media (min-width: 768px) {
  /* Ensure proper flex behavior */
  .flex.flex-col.md\\:flex-row {
    flex-direction: row !important;
  }

  /* Fix sidebar width issues */
  .w-3\/12 {
    width: 25% !important;
  }

  /* Ensure content area takes remaining space */
  .learner-content {
    flex: 1 !important;
  }

  /* Fix potential overflow issues */
  .overflow-x-hidden {
    overflow-x: hidden !important;
  }

  /* Ensure proper spacing for content */
  .space-y-4.md\\:space-y-9 > * + * {
    margin-top: 2.25rem !important;
  }

  .space-y-6.md\\:space-y-14 > * + * {
    margin-top: 3.5rem !important;
  }
}

/* Fix for potential layout shifts during resize */
@media (min-width: 767px) and (max-width: 769px) {
  .learner-main-container {
    transition: none !important;
  }

  .learner-sidebar {
    transition: none !important;
  }

  .learner-content {
    transition: none !important;
  }
}
