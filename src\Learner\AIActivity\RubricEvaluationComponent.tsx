type Props = {
  data: any;
};
export default function RubricEvaluationComponent({ data }: Props) {
  const totalKeys = Object.keys(data);
  const totalScoreKey = totalKeys.find(
    (k) => k.toLowerCase() === "total score",
  );
  const suggestionKey = totalKeys.find(
    (k) => k.toLowerCase() === "suggestion for improvements",
  );
  const totalCriterias = totalKeys
    .filter((k) => k !== totalScoreKey)
    .filter((k) => k !== suggestionKey);
  const score = data[totalScoreKey];
  const obtainedScore = score.split("/")[0];
  const totalScore = score.split("/")[1];
  const suggestionForImprovements = data[suggestionKey];

  const renderStars = (rating: number, total: number) => {
    const fullStar = "★";
    const emptyStar = "☆";
    return (
      <>
        {[...Array(total)].map((_, i) => (
          <span key={i} className="text-black">
            {i < rating ? fullStar : emptyStar}
          </span>
        ))}
      </>
    );
  };
  return (
    <>
      {totalCriterias.map((criteria) => {
        return (
          <div key={criteria} className="mb-4">
            <h2>
              <strong>Criteria: {criteria}</strong>
            </h2>
            <QuestionComponet data={data[criteria]} />
          </div>
        );
      })}
      <h2 className="mb-4">
        <strong>Suggestion for improvements: </strong>
        <p>{suggestionForImprovements}</p>
      </h2>

      <span>
        <strong>Total Score: </strong>
        {renderStars(Number(obtainedScore), Number(totalScore))}
      </span>
    </>
  );
}
type QuestionComponentProps = {
  data: any;
};
function QuestionComponet({ data }: QuestionComponentProps) {
  const keys = Object.keys(data);
  const subTotalKey = keys.find((k) => k.toLowerCase() === "sub total");
  const questions = keys.filter((k) => k !== subTotalKey);
  const renderStars = (rating: number, total: number) => {
    const fullStar = "★";
    const emptyStar = "☆";
    return (
      <>
        {[...Array(total)].map((_, i) => (
          <span key={i} className="text-black">
            {i < rating ? fullStar : emptyStar}
          </span>
        ))}
      </>
    );
  };
  const sub_total = data[subTotalKey];
  const obtainedScore = sub_total.split("/")[0];
  const totalScore = sub_total.split("/")[1];
  return (
    <>
      {questions.map((question) => {
        return (
          <div key={question}>
            <h3>
              <strong>{question}</strong>
            </h3>
            <p>
              <strong>Explanation: </strong>
              {data[question][Object.keys(data[question])[0]]}
            </p>
          </div>
        );
      })}
      <span>
        <strong>Score: </strong>
        {renderStars(Number(obtainedScore), Number(totalScore))}
      </span>
    </>
  );
}
