import { Question } from "../Learner/hooks/useQuestion";

interface Video {
  title: string;
  video: string;
}

interface ActivityName {
  id: number;
  name: string;
  type: string;
  selected?: boolean;
}

interface PracticeActivityRubricQuestion {
  question: string;
}

interface PracticeActivityRubricCriterion {
  name: string;
  questions: PracticeActivityRubricQuestion[];
}

interface PracticeActivityData {
  interlocutorProfile: string;
  scenarioDescription: string;
  evaluationRubric: {
    criteria: PracticeActivityRubricCriterion[];
  };
}

interface Activity {
  id: number;
  name: string;
  instructions?: string | null;
  instructions_below?: string | null;
  instructions_cp?: string | null;
  instructions_cp_below?: string | null;
  instructions_rec?: string | null;
  instructions_rec_below?: string | null;
  instructions_wr?: string | null;
  instructions_wr_below?: string | null;
  type: string;
  video: string | null;
  second_video: string | null;
  questions: any[];
  mcqs: Question[];
  title_videos: Video[];
  mandatory: boolean;
  show_correct_option: boolean;
  file_link: string;
  file_path: string | null;
  practiceActivityData?: PracticeActivityData;
  extra_fields?: {
    heygen_video_id?: number;
    heygen_video_url?: string;
    practiceActivityData?: PracticeActivityData;
    [key: string]: any;
  };
}

interface FeedbackQuestions {
  question: string;
  options: string[];
}

interface ActivityUpdateResponse {
  success: boolean;
  message: string;
}

interface ActivityDeleteResponse {
  success: boolean;
  message: string;
}

interface ActivityCreateData {
  id: number;
  name: string;
  type: string;
}

interface ActivityCreateResponse {
  success: boolean;
  message: string;
  data: ActivityCreateData;
}

interface ActivityResultResponse {
  id: number;
  user_input: any;
  result: any;
}

interface ActivityResultCohortResponse {
  result: any;
}

export type {
  Activity,
  ActivityName,
  ActivityUpdateResponse,
  ActivityCreateResponse,
  ActivityDeleteResponse,
  ActivityResultResponse,
  FeedbackQuestions,
  ActivityResultCohortResponse,
  PracticeActivityData,
  PracticeActivityRubricCriterion,
  PracticeActivityRubricQuestion,
};
