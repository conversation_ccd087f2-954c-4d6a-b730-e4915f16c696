import { useMutation } from "@tanstack/react-query";
import useAxios from "../../hooks/axiosObject";
import { GeneralResponse } from "../../types/GeneralResponse";
import { AxiosInstance } from "axios";

// api call

const updateUser = async (
  axiosObject: AxiosInstance,
  userId: string,
  name: string | null,
  role: "learner" | "instructor" | "admin" | null | number | undefined,
  ethnicity: string | null, 
  organization: string | null
): Promise<GeneralResponse> => {
  const data = {
    course_id: userId,
    role: role,
    name: name,
    ethnicity: ethnicity,
    organization_name: organization
  }
  const updatedUser = await axiosObject.patch<GeneralResponse>(
    "/api/v1/user/update/" + userId, data
  );
  console.log(updatedUser);
  return updatedUser.data;
};

export function useUpdateUser() {
  const axiosInstance = useAxios();
  // run the query
  return useMutation({
    mutationFn: ({ userId, name = null, role = null, ethnicity = null, organization = null }: {
      userId: string,
      name?: string | null,
      role?: "learner" | "instructor" | "admin" | null | number | undefined,
      ethnicity?: string | null, 
      organization?: string | null
    }) => {
      return updateUser(axiosInstance, userId, name, role, ethnicity, organization);
    },
  })
}
