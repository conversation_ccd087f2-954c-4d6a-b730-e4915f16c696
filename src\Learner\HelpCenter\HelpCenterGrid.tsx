import React from 'react';
import InfoCard from './HelpCenterCard';
import NotificationsIcon from '@mui/icons-material/Notifications';
import PaymentIcon from '@mui/icons-material/Payment';
import EditIcon from '@mui/icons-material/Edit';
import SchoolIcon from '@mui/icons-material/School';
import VerifiedIcon from '@mui/icons-material/Verified';
import PolicyIcon from '@mui/icons-material/Policy';
import PopularArticles from './PopularArticles';

const HelpCenterGrid: React.FC = () => {
  return (
    <>
      <div className="bg-gray-100 p-6 flex justify-center items-center">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <InfoCard
            icon={<NotificationsIcon style={{ fontSize: 48 }} />}
            title="Accounts & Notifications"
            description="Account Settings, Login issues, and notification preferences."
          />
          <InfoCard
            icon={<PaymentIcon style={{ fontSize: 48 }} />}
            title="Payments & Subscriptions"
            description="Help with payments, subscription options & Financial Aid."
          />
          <InfoCard
            icon={<EditIcon style={{ fontSize: 48 }} />}
            title="Enrollment"
            description="Find courses to take & learn about enrollment options."
          />
          <InfoCard
            icon={<SchoolIcon style={{ fontSize: 48 }} />}
            title="Grades & Assignments"
            description="Grades, peer reviews, assignments, and labs."
          />
          <InfoCard
            icon={<VerifiedIcon style={{ fontSize: 48 }} />}
            title="Certificates & Verifications"
            description="How to get and share a Course Certificate."
          />
          <InfoCard
            icon={<PolicyIcon style={{ fontSize: 48 }} />}
            title="Policies"
            description="Learn about our policies & program terms."
          />
        </div>
      </div>
      <div className="bg-gray-100 p-6 flex justify-center items-center">
        <PopularArticles />
      </div>
    </>
  );
};

export default HelpCenterGrid;
