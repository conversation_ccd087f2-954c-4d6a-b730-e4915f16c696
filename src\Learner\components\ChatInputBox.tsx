import React from "react";
import { Message } from "../../types/Message";
import DebouncedInput from "./DebouncedInput";

interface ChatInputBoxProps {
  sendANewMessage: (message: Message) => void;
}

const ChatInputBox = ({ sendANewMessage }: ChatInputBoxProps) => {
  const [newMessage, setNewMessage] = React.useState("");

  /**
   * Send message handler
   * Should empty text field after sent
   */
  const doSendMessage = () => {
    if (newMessage && newMessage.length > 0) {
      const newMessagePayload: Message = {
        sender: "user",
        message: newMessage,
        created_at: new Date(),
      };
      sendANewMessage(newMessagePayload);
      setNewMessage("");
    }
  };

  return (
    <div className="w-100 rounded-br-xla overflow-hidden rounded-bl-xl bg-white px-6 py-3">
      <div className="flex flex-row items-center space-x-5">
        <DebouncedInput
          value={newMessage ?? ""}
          debounce={100}
          onChange={(value) => setNewMessage(String(value))}
          onPressEnter={doSendMessage}
        />
        <button
          type="button"
          disabled={!newMessage || newMessage.length === 0}
          className="shrink-0 rounded-lg bg-[#EEC300] px-3 py-2 text-center text-lg font-medium text-[#f5f5f5] hover:bg-yellow-500 focus:outline-none focus:ring-4 focus:ring-yellow-300 disabled:opacity-50"
          onClick={() => doSendMessage()}
        >
          Message Jan
        </button>
      </div>
    </div>
  );
};

export default ChatInputBox;
