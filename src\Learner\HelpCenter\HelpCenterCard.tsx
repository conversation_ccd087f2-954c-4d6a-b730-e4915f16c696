import React from 'react';

interface InfoCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const InfoCard: React.FC<InfoCardProps> = ({ icon, title, description }) => {
  return (
    <div className="w-100 h-80 bg-white rounded-lg shadow-md p-6 flex flex-col items-center justify-center text-center">
      <div className="flex justify-center mb-4 text-yellow-500">
        {icon}
      </div>
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <p className="text-gray-600">{description}</p>
    </div>
  );
};

export default InfoCard;
