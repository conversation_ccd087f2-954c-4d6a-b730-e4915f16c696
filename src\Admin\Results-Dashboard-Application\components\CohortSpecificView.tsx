import {
  Award,
  Clock,
  Download,
  Search,
  TrendingUp,
  Users,
} from "lucide-react";
import { useState } from "react";
import {
  Area,
  Bar,
  BarChart,
  CartesianGrid,
  ComposedChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import useCohortSummaries from "../hooks/useCohortSummaries";
import useActivityResults from "../hooks/useActivityResults";

interface CohortSpecificViewProps {
  selectedCourse: string;
  onCourseChange: (course: string) => void;
  selectedTimePeriod: string;
}

export function CohortSpecificView({
  selectedCourse,
  onCourseChange,
  selectedTimePeriod,
}: CohortSpecificViewProps) {
  const [selectedCohort, setSelectedCohort] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedActivity, setSelectedActivity] = useState("all");

  // Helper function to ensure valid numeric values
  const ensureValidNumber = (value: any): number => {
    const num = Number(value);
    return isNaN(num) || !isFinite(num) ? 0 : num;
  };

  // Helper function to create consistent activity keys
  const createActivityKey = (
    activityType: string,
    activityNumber: number,
  ): string => {
    // For Course 3, always include the number to match cohort summary data
    if (selectedCourse === "Hospitality") {
      if (activityType === "AI Activity") {
        return `AI Activity ${activityNumber}`;
      } else if (activityType === "Quiz") {
        return `Quiz ${activityNumber}`;
      } else if (activityType === "Vira Activity") {
        return "Vira Activity";
      }
    }

    // For other courses, use the original logic
    if (activityNumber > 1) {
      return `${activityType} ${activityNumber}`;
    } else {
      return activityType;
    }
  };

  const { data: mockCohortSummaries, isLoading } = useCohortSummaries();
  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Get available courses and cohorts - use same data source as OverallView
  const courses = [...new Set(mockCohortSummaries.map((c) => c.course))];
  const availableCohorts = [
    ...new Set(
      mockCohortSummaries
        .filter((c) => c.course === selectedCourse)
        .map((c) => c.cohort),
    ),
  ];

  // Auto-select first cohort if none selected or if course changed
  const currentCohort =
    selectedCohort && availableCohorts.includes(selectedCohort)
      ? selectedCohort
      : availableCohorts[0] || "";

  // Update selectedCohort if it changed
  if (currentCohort !== selectedCohort) {
    setSelectedCohort(currentCohort);
  }

  // Get cohort summary data from the same source as OverallView
  const currentCohortSummary = mockCohortSummaries.find(
    (c) => c.course === selectedCourse && c.cohort === currentCohort,
  );

  const { data: mockActivityResults, isLoading: isResultsLoading } =
    useActivityResults();
  if (isResultsLoading) {
    return <div>Loading...</div>;
  }

  // Filter activity results for detailed analysis
  const cohortFilteredResults = mockActivityResults.filter(
    (result) =>
      result.course === selectedCourse && result.cohort === currentCohort,
  );

  // Get unique activities for the selected cohort using consistent naming
  const activities = [
    ...new Set(cohortFilteredResults.map((r) => r.activityType)),
  ];

  // Helper function to parse activity key into type and number
  const parseActivityKey = (activityKey: string) => {
    // Handle Course 3 specific naming
    console.log("=== Parse Activity Key Debug ===");
    console.log("Activity Key:", activityKey);
    console.log("Selected Course:", selectedCourse);
    console.log("=== End Parse Activity Key Debug ===");

    if (selectedCourse === "Hospitality") {
      if (activityKey.startsWith("AI Activity")) {
        const match = activityKey.match(/AI Activity (\d+)/);
        return { type: "AI Activity", number: match ? parseInt(match[1]) : 1 };
      } else if (activityKey.startsWith("Quiz")) {
        const match = activityKey.match(/Quiz (\d+)/);
        return { type: "Quiz", number: match ? parseInt(match[1]) : 1 };
      } else if (activityKey === "Vira Activity") {
        return { type: "Vira Activity", number: 1 };
      }
    }

    // Handle other courses
    const parts = activityKey.split(" ");
    if (parts.length === 1) {
      return { type: parts[0], number: 1 };
    } else if (parts.length === 2) {
      const secondPart = parts[1];
      if (secondPart === "Activity" || !isNaN(parseInt(secondPart))) {
        // Case: "AI Activity" or "Quiz 2"
        if (secondPart === "Activity") {
          return { type: `${parts[0]} Activity`, number: 1 };
        } else {
          return { type: parts[0], number: parseInt(secondPart) };
        }
      } else {
        // Case: "Vira Activity"
        return { type: activityKey, number: 1 };
      }
    } else if (parts.length === 3) {
      // Case: "AI Activity 2"
      return {
        type: `${parts[0]} ${parts[1]}`,
        number: parseInt(parts[2]) || 1,
      };
    }
    return { type: activityKey, number: 1 };
  };

  // Generate bell curve data for activities
  const generateBellCurve = (activityKey: string) => {
    console.log("=== Bell Curve Debug ===");
    console.log("Activity Key:", activityKey);
    console.log("Selected Course:", selectedCourse);
    console.log("Current Cohort:", currentCohort);
    console.log("Available results:", cohortFilteredResults.length);

    const { type, number } = parseActivityKey(activityKey);
    console.log("Parsed - Type:", type, "Number:", number);

    // Find matching activity results with exact matching
    const activityScores = cohortFilteredResults
      .filter((result) => {
        const resultKey = result.activityType;
        const matches = resultKey === activityKey;
        console.log(
          "Comparing result:",
          `${result.activityType} ${result.activityNumber}`,
          "Key:",
          resultKey,
          "vs Target:",
          activityKey,
          "- matches:",
          matches,
        );
        return matches;
      })
      .map((r) => ensureValidNumber(r.score));

    console.log("Found scores:", activityScores);
    console.log("Score count:", activityScores.length);
    console.log("=== End Bell Curve Debug ===");

    if (activityScores.length === 0) {
      console.log("No scores found for activity:", activityKey);
      return [];
    }

    const mean =
      activityScores.reduce((sum, score) => sum + score, 0) /
      activityScores.length;
    const variance =
      activityScores.reduce(
        (sum, score) => sum + Math.pow(score - mean, 2),
        0,
      ) / activityScores.length;
    const stdDev = Math.sqrt(variance);

    console.log(
      "Statistics - Mean:",
      mean,
      "StdDev:",
      stdDev,
      "Sample size:",
      activityScores.length,
    );

    // Create both histogram and normal curve data
    const histogramData = [];
    for (let i = 0; i <= 100; i += 5) {
      const count = activityScores.filter(
        (score) => score >= i && score < i + 5,
      ).length;
      const normalValue =
        (1 / (stdDev * Math.sqrt(2 * Math.PI))) *
        Math.exp(-0.5 * Math.pow((i + 2.5 - mean) / stdDev, 2));

      histogramData.push({
        range: `${i}-${i + 4}`,
        actualCount: count,
        normalCurve: normalValue * activityScores.length * 5,
        midPoint: i + 2.5,
      });
    }

    return histogramData;
  };

  // Get cohort activity performance data using both sources
  const getCohortActivityPerformance = () => {
    if (!currentCohortSummary) return [];

    const activityPerformance: any[] = [];

    // Use activities from cohort summary for completion rates (matches OverallView)
    if (currentCohortSummary.activities) {
      Object.entries(currentCohortSummary.activities).forEach(
        ([activityKey, activity]) => {
          // Get detailed results for this activity from mockActivityResults
          console.log("=== Activity Results Debug ===");
          console.log("Activity Key:", activityKey);
          console.log("activity: ", activity);
          console.log("Cohort Filtered Results:", cohortFilteredResults);
          console.log("=== End Activity Results Debug ===");

          const activityResults = cohortFilteredResults.filter((result) => {
            const resultKey = result.activityType;
            return resultKey === activityKey;
          });

          console.log("=== Activity Performance Debug ===");
          console.log("Activity Key:", activityKey);
          console.log("Matching results:", activityResults.length);
          console.log("=== End Activity Performance Debug ===");

          const avgTime =
            activityResults.length > 0
              ? activityResults.reduce(
                  (sum, r) => sum + ensureValidNumber(r.timeSpent),
                  0,
                ) / activityResults.length
              : 0;

          // Calculate completion rate from cohort summary data
          const totalStudents = ensureValidNumber(
            currentCohortSummary.totalStudents,
          );
          const completedStudents = ensureValidNumber(activity.completed);
          const completionRate =
            totalStudents > 0 ? (completedStudents / totalStudents) * 100 : 0;

          const { type, number } = parseActivityKey(activityKey);

          activityPerformance.push({
            name: activityKey,
            type: type,
            number: number,
            averageScore: ensureValidNumber(activity.avg),
            averageTime: avgTime,
            completionRate: completionRate,
            totalAttempts: activityResults.length,
            completions: completedStudents,
            scores: activityResults.map((r) => ensureValidNumber(r.score)),
            timeSpent: activityResults.map((r) =>
              ensureValidNumber(r.timeSpent),
            ),
          });
        },
      );
    }

    return activityPerformance;
  };

  const activityPerformance = getCohortActivityPerformance();

  // Prepare data for activity-wise performance chart
  const prepareActivityPerformanceChartData = () => {
    return activityPerformance
      .map((activity) => ({
        activity: activity.name,
        averageScore: ensureValidNumber(activity.averageScore),
        students: activity.totalAttempts,
      }))
      .sort((a, b) => b.averageScore - a.averageScore);
  };

  // Prepare data for completion rate chart
  const prepareCompletionChartData = () => {
    return activityPerformance
      .map((activity) => ({
        activity: activity.name,
        completionRate: ensureValidNumber(activity.completionRate),
        completed: activity.completions,
        total: ensureValidNumber(currentCohortSummary?.totalStudents || 0),
      }))
      .sort((a, b) => b.completionRate - a.completionRate);
  };

  const activityPerformanceChartData = prepareActivityPerformanceChartData();
  const completionChartData = prepareCompletionChartData();

  // Filter results for detailed table
  const filteredResults = cohortFilteredResults.filter((result) => {
    const activityName = result.activityType;
    const matchesSearch =
      result.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      result.course.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesActivity =
      selectedActivity === "all" || activityName === selectedActivity;

    return matchesSearch && matchesActivity;
  });

  // Calculate summary statistics using cohort summary data (same as OverallView)
  const totalStudents = ensureValidNumber(
    currentCohortSummary?.totalStudents || 0,
  );
  const averagePerformance = ensureValidNumber(
    currentCohortSummary?.averageScore || 0,
  );
  const completionRate = ensureValidNumber(
    currentCohortSummary?.completionRate || 0,
  );
  const averageTimeSpent =
    cohortFilteredResults.length > 0
      ? cohortFilteredResults.reduce(
          (sum, r) => sum + ensureValidNumber(r.timeSpent),
          0,
        ) / cohortFilteredResults.length
      : 0;

  // Download function
  const handleDownload = () => {
    const csvContent = [
      [
        "Student",
        "Course",
        "Cohort",
        "Activity",
        "Score",
        "Max Score",
        "Percentage",
        "Time Spent",
        "Completed",
      ],
      ...filteredResults.map((result) => [
        result.studentName,
        result.course,
        result.cohort,
        result.activityType,
        ensureValidNumber(result.score).toString(),
        ensureValidNumber(result.maxScore).toString(),
        (
          (ensureValidNumber(result.score) /
            ensureValidNumber(result.maxScore)) *
          100
        ).toFixed(1),
        ensureValidNumber(result.timeSpent).toString(),
        result.completedAt || "No",
      ]),
    ]
      .map((row) => row.map((cell) => `"${cell}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `cohort-${currentCohort}-${selectedCourse.toLowerCase().replace(" ", "-")}-results.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      {/* Course and Cohort Selection */}
      <Card className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="text-primary h-5 w-5" />
            <h3>Select Course and Cohort</h3>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="flex flex-col space-y-2">
            <label className="text-muted-foreground text-sm">Course</label>
            <select
              value={selectedCourse}
              onChange={(e) => onCourseChange(e.target.value)}
              className="bg-background rounded-md border px-4 py-2"
            >
              {courses.map((course) => (
                <option key={course} value={course}>
                  {course}
                </option>
              ))}
            </select>
          </div>
          <div className="flex flex-col space-y-2">
            <label className="text-muted-foreground text-sm">Cohort</label>
            <select
              value={currentCohort}
              onChange={(e) => setSelectedCohort(e.target.value)}
              className="bg-background rounded-md border px-4 py-2"
              disabled={availableCohorts.length === 0}
            >
              {availableCohorts.length > 0 ? (
                availableCohorts.map((cohort) => (
                  <option key={cohort} value={cohort}>
                    {cohort}
                  </option>
                ))
              ) : (
                <option value="">No cohorts available</option>
              )}
            </select>
          </div>
        </div>
      </Card>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <Users className="text-primary h-4 w-4" />
            <div className="flex flex-col">
              <span className="text-muted-foreground text-sm">
                Total Students
              </span>
              <span className="text-2xl font-bold">{totalStudents}</span>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <Award className="text-primary h-4 w-4" />
            <div className="flex flex-col">
              <span className="text-muted-foreground text-sm">
                Avg Performance
              </span>
              <span className="text-2xl font-bold">
                {averagePerformance.toFixed(1)}%
              </span>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <TrendingUp className="text-primary h-4 w-4" />
            <div className="flex flex-col">
              <span className="text-muted-foreground text-sm">
                Completion Rate
              </span>
              <span className="text-2xl font-bold">
                {completionRate.toFixed(1)}%
              </span>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <Clock className="text-primary h-4 w-4" />
            <div className="flex flex-col">
              <span className="text-muted-foreground text-sm">
                Avg Time Spent
              </span>
              <span className="text-2xl font-bold">
                {averageTimeSpent.toFixed(0)}m
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Activity Performance and Completion Charts */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Activity-wise Performance Chart */}
        <Card className="p-6">
          <h3 className="mb-4">Activity-wise Performance</h3>
          {activityPerformanceChartData.length > 0 ? (
            <div className="w-full">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={activityPerformanceChartData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="activity"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    interval={0}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis
                    domain={[0, 100]}
                    tickFormatter={(value) => `${value}%`}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip
                    formatter={(value: any, name: any) => [
                      `${Number(value).toFixed(1)}%`,
                      "Average Score",
                    ]}
                    labelFormatter={(label) => `Activity: ${label}`}
                    contentStyle={{
                      backgroundColor: "white",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      fontSize: "14px",
                    }}
                  />
                  <Bar
                    dataKey="averageScore"
                    fill="#3B82F6"
                    name="Average Score"
                    radius={[4, 4, 0, 0]}
                    minPointSize={5}
                  />
                </BarChart>
              </ResponsiveContainer>

              <div className="text-muted-foreground mt-2 text-xs">
                Showing average performance across{" "}
                {activityPerformanceChartData.length} activities for{" "}
                {currentCohort}
              </div>
            </div>
          ) : (
            <div className="text-muted-foreground flex h-[300px] flex-col items-center justify-center">
              <div>No activity performance data available</div>
              <div className="mt-2 text-xs">
                {currentCohort
                  ? `for ${currentCohort} in ${selectedCourse}`
                  : "Please select a cohort"}
              </div>
            </div>
          )}
        </Card>

        {/* Activity Completion Chart */}
        <Card className="p-6">
          <h3 className="mb-4">Activity Completion</h3>
          {completionChartData.length > 0 ? (
            <div className="w-full">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={completionChartData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="activity"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    interval={0}
                    tick={{ fontSize: 12 }}
                    axisLine={{ stroke: "#e0e0e0" }}
                    tickLine={{ stroke: "#e0e0e0" }}
                  />
                  <YAxis
                    domain={[0, 100]}
                    tickFormatter={(value) => `${value}%`}
                    tick={{ fontSize: 12 }}
                    axisLine={{ stroke: "#e0e0e0" }}
                    tickLine={{ stroke: "#e0e0e0" }}
                    label={{
                      value: "Completion %",
                      angle: -90,
                      position: "insideLeft",
                      style: {
                        textAnchor: "middle",
                        fontSize: "12px",
                        fill: "#666",
                      },
                    }}
                  />
                  <Tooltip
                    formatter={(value: any, name: any) => [
                      `${Number(value).toFixed(1)}%`,
                      "Completion Rate",
                    ]}
                    labelFormatter={(label) => `Activity: ${label}`}
                    contentStyle={{
                      backgroundColor: "white",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      fontSize: "14px",
                    }}
                  />
                  <Bar
                    dataKey="completionRate"
                    fill="#10B981"
                    name="Completion Rate"
                    radius={[4, 4, 0, 0]}
                    minPointSize={5}
                  />
                </BarChart>
              </ResponsiveContainer>

              <div className="text-muted-foreground mt-2 text-xs">
                Completion rates across {completionChartData.length} activities
                for {currentCohort}
              </div>
            </div>
          ) : (
            <div className="text-muted-foreground flex h-[300px] flex-col items-center justify-center">
              <div>No completion data available</div>
              <div className="mt-2 text-xs">
                {currentCohort
                  ? `for ${currentCohort} in ${selectedCourse}`
                  : "Please select a cohort"}
              </div>
            </div>
          )}
        </Card>
      </div>

      {/* Activity Performance Cards */}
      <Card className="p-6">
        <h3 className="mb-4">Activity Performance Overview</h3>
        {activityPerformance.length > 0 ? (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {activityPerformance.map((activity, index) => (
              <Card key={`${activity.name}-${index}`} className="border-2 p-4">
                <div className="flex flex-col space-y-2">
                  <h4>{activity.name}</h4>
                  <Badge variant="outline" className="w-fit text-xs">
                    {activity.type}
                  </Badge>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground text-xs">
                        Avg Score:
                      </span>
                      <div className="font-semibold">
                        {activity.averageScore.toFixed(1)}%
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground text-xs">
                        Completion:
                      </span>
                      <div className="font-semibold">
                        {activity.completionRate.toFixed(1)}%
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground text-xs">
                        Avg Time:
                      </span>
                      <div className="font-semibold">
                        {activity.averageTime.toFixed(0)}m
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground text-xs">
                        Completed:
                      </span>
                      <div className="font-semibold">
                        {activity.completions}/{totalStudents}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-muted-foreground py-8 text-center">
            {currentCohort
              ? `No activity data available for ${currentCohort} in ${selectedCourse}`
              : "Please select a cohort"}
          </div>
        )}
      </Card>

      {/* Bell Curves for Each Activity */}
      {activityPerformance.length > 0 && (
        <div className="space-y-6">
          <h3>Score Distribution Analysis</h3>
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {activityPerformance.map((activity, index) => {
              console.log("=== Bell Curve Debug ===");
              console.log("Activity Name:", activity.name);
              console.log("Selected Course:", selectedCourse);
              console.log("Current Cohort:", currentCohort);
              console.log("Available scores:", activity.scores.length);
              console.log("=== End Bell Curve Debug ===");

              const bellCurveData = generateBellCurve(activity.name);
              const mean =
                activity.scores.length > 0
                  ? activity.scores.reduce((sum, score) => sum + score, 0) /
                    activity.scores.length
                  : 0;
              const stdDev =
                activity.scores.length > 0
                  ? Math.sqrt(
                      activity.scores.reduce(
                        (sum, score) => sum + Math.pow(score - mean, 2),
                        0,
                      ) / activity.scores.length,
                    )
                  : 0;

              return (
                <Card key={`${activity.name}-bell-${index}`} className="p-6">
                  <div className="space-y-4">
                    <div>
                      <h4>{activity.name} - Score Distribution</h4>
                      <div className="text-muted-foreground text-sm">
                        {currentCohort} • {selectedCourse}
                      </div>
                      <div className="text-muted-foreground mt-1 text-sm">
                        Mean: {ensureValidNumber(mean).toFixed(1)}% | Std Dev:{" "}
                        {ensureValidNumber(stdDev).toFixed(1)} | Students:{" "}
                        {activity.scores.length}
                      </div>
                    </div>

                    {bellCurveData.length > 0 ? (
                      <ResponsiveContainer width="100%" height={300}>
                        <ComposedChart data={bellCurveData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            dataKey="range"
                            angle={-45}
                            textAnchor="end"
                            height={80}
                          />
                          <YAxis />
                          <Tooltip
                            formatter={(value, name) => [
                              Number(value).toFixed(
                                name === "actualCount" ? 0 : 2,
                              ),
                              name === "actualCount"
                                ? "Students"
                                : "Normal Distribution",
                            ]}
                          />
                          <Bar
                            dataKey="actualCount"
                            fill="#8884d8"
                            opacity={0.7}
                            name="Students"
                          />
                          <Area
                            type="monotone"
                            dataKey="normalCurve"
                            stroke="#ff7300"
                            fill="#ff7300"
                            fillOpacity={0.3}
                            name="Normal Distribution"
                          />
                        </ComposedChart>
                      </ResponsiveContainer>
                    ) : (
                      <div className="text-muted-foreground flex h-[300px] items-center justify-center">
                        <div className="text-center">
                          <div>No distribution data available</div>
                          <div className="mt-2 text-xs">
                            Activity: {activity.name} | Scores available:{" "}
                            {activity.scores.length}
                          </div>
                          <div className="mt-1 text-xs">
                            Check browser console for detailed debugging info
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Search and Filters for Detailed Results */}
      <Card className="p-6">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center space-x-2">
            <Search className="text-muted-foreground h-4 w-4" />
            <h3>Search & Filter Results</h3>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="relative">
              <Search className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform" />
              <Input
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <select
              value={selectedActivity}
              onChange={(e) => setSelectedActivity(e.target.value)}
              className="bg-background rounded-md border px-3 py-2"
            >
              <option value="all">All Activities</option>
              {activities.map((activity) => (
                <option key={activity} value={activity}>
                  {activity}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>

      {/* Detailed Results Table */}
      <Card className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <h3>Detailed Results ({filteredResults.length} results)</h3>
          <Button
            onClick={handleDownload}
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
            disabled={filteredResults.length === 0}
          >
            <Download className="h-4 w-4" />
            <span>Download</span>
          </Button>
        </div>
        <div className="overflow-x-auto">
          {filteredResults.length > 0 ? (
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="p-2 text-left">Student</th>
                  <th className="p-2 text-left">Activity</th>
                  <th className="p-2 text-left">Score</th>
                  <th className="p-2 text-left">Percentage</th>
                  <th className="p-2 text-left">Time</th>
                  <th className="p-2 text-left">Date</th>
                </tr>
              </thead>
              <tbody>
                {filteredResults.map((result, index) => (
                  <tr
                    key={`${result.studentId}-${result.activityType}-${result.activityNumber}-${index}`}
                    className="hover:bg-secondary/20 border-b"
                  >
                    <td className="p-2">{result.studentName}</td>
                    <td className="p-2">{result.activityType}</td>
                    <td className="p-2">
                      {ensureValidNumber(result.score)}/
                      {ensureValidNumber(result.maxScore)}
                    </td>
                    <td className="p-2">
                      <Badge
                        variant={
                          (ensureValidNumber(result.score) /
                            ensureValidNumber(result.maxScore)) *
                            100 >=
                          70
                            ? "default"
                            : "destructive"
                        }
                      >
                        {(
                          (ensureValidNumber(result.score) /
                            ensureValidNumber(result.maxScore)) *
                          100
                        ).toFixed(1)}
                        %
                      </Badge>
                    </td>
                    <td className="p-2">
                      {ensureValidNumber(result.timeSpent)}m
                    </td>
                    <td className="p-2">{result.completedAt || "N/A"}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-muted-foreground py-8 text-center">
              No results found. Try adjusting your search or filter criteria.
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
