import React from "react";

interface Props {
  selected?: boolean;
}

function SystemManagementIcon({ selected }: Props) {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.5 0C3.80558 0 0 3.80558 0 8.5C0 13.1944 3.80558 17 8.5 17C13.1944 17 17 13.1944 17 8.5C17 3.80558 13.1944 0 8.5 0ZM8.5 15.3C4.74446 15.3 1.7 12.2555 1.7 8.5C1.7 4.74446 4.74446 1.7 8.5 1.7C12.2555 1.7 15.3 4.74446 15.3 8.5C15.3 12.2555 12.2555 15.3 8.5 15.3ZM11.9 8.5C11.9 10.3777 10.3777 11.9 8.5 11.9C6.62228 11.9 5.1 10.3777 5.1 8.5C5.1 6.62228 6.62228 5.1 8.5 5.1C10.3777 5.1 11.9 6.62228 11.9 8.5Z"
        fill={selected ? "white" : "#A3A3A3"}
      />
    </svg>
  );
}

export default SystemManagementIcon;
